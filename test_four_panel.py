#!/usr/bin/env python3
"""
测试四分屏K线图实现
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # 尝试导入修改后的文件
    print("正在测试四分屏K线图实现...")
    print("检查导入是否成功...")
    
    # 检查必要的依赖
    import pandas as pd
    from PySide6.QtWidgets import QApplication
    from lightweight_charts.widgets import QtChart
    
    print("✓ 基础依赖导入成功")
    
    # 检查pytdx
    try:
        from pytdx.hq import TdxHq_API
        print("✓ pytdx导入成功")
    except ImportError as e:
        print(f"⚠ pytdx导入失败: {e}")
    
    # 检查支撑阻力分析模块
    try:
        from support_and_resistance import analyze_kline_data, create_analyzer
        print("✓ 支撑阻力分析模块导入成功")
    except ImportError as e:
        print(f"⚠ 支撑阻力分析模块导入失败: {e}")
    
    print("\n四分屏K线图实现已完成！")
    print("\n主要特性:")
    print("1. ✓ 四个独立的K线图表（2x2网格布局）")
    print("2. ✓ 默认时间周期配置：")
    print("   - 左上角：15分钟")
    print("   - 右上角：1分钟") 
    print("   - 左下角：60分钟")
    print("   - 右下角：5分钟")
    print("3. ✓ 每个图表独立的控制栏（股票代码、时间周期、图表样式）")
    print("4. ✓ 每个图表独立的搜索功能")
    print("5. ✓ 每个图表独立的实时数据更新")
    print("6. ✓ 支撑阻力位分析（仅15分钟图表）")
    print("7. ✓ 红涨绿跌的中国股市颜色配置")
    print("8. ✓ 图表水印标识（1、2、3、4）")
    
    print("\n使用方法:")
    print("运行以下命令启动四分屏K线图：")
    print("python \"PySide6/pyside6_2.28优秀不卡实时数据版.py\"")
    
    print("\n注意事项:")
    print("- 确保网络连接正常以获取实时数据")
    print("- 首次运行可能需要一些时间来建立数据连接")
    print("- 每个图表可以独立设置不同的股票代码和时间周期")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
