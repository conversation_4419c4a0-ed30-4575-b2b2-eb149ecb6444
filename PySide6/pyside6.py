import pandas as pd
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTabWidget, QPushButton, QHBoxLayout, QTabBar, QGridLayout, QSizePolicy
from PySide6.QtCore import QThread, Signal, Qt
import time

from lightweight_charts.widgets import QtChart
from datetime import datetime, timedelta

from xtquant import xtdata

#####################################################################
# 以下新增函数用于获取历史数据，不同 timeframe 调用不同的逻辑
#####################################################################

# 常量定义：分钟级数据周期数和日线数据月份数（便于统一调整）
MINUTE_CYCLE_COUNT = 240
DAILY_MONTH_COUNT = 6

# 全局缓存，用于存储已获取的历史数据，格式为： { "symbol_timeframe": DataFrame }


def fetch_5minute_data(symbol):
    """获取5分钟级历史数据，返回一个按分钟排列的 DataFrame（共 240 个周期）"""
    now = datetime.now()
    window_days = 14  # 改为14天
    current_start = now - timedelta(days=window_days)

    while True:
        start_time_str = current_start.strftime("%Y%m%d") + "000000"
        end_time_str = now.strftime("%Y%m%d%H%M%S")

        # 下载当前窗口内的 5min 数据，并增加异常处理
        try:
            xtdata.download_history_data(symbol, period='5m', start_time=start_time_str, end_time=end_time_str)
            data = xtdata.get_market_data([], [symbol], period='5m', start_time=start_time_str, end_time=end_time_str,
                                          dividend_type='none')
        except Exception as e:
            err_msg = str(e)
            from PySide6.QtWidgets import QMessageBox
            if "invalid stockcode" in err_msg.lower() or "invalid stock" in err_msg.lower():
                QMessageBox.warning(None, "警告", "不是正确的股票代码")
            else:
                QMessageBox.critical(None, "错误", err_msg)
            return pd.DataFrame()

        # 将 data 内每个字段转换为列表，构造 DataFrame
        extracted = {}
        for field, df_field in data.items():
            if isinstance(df_field, pd.DataFrame) and not df_field.empty:
                extracted[field] = df_field.iloc[0].tolist()
            else:
                extracted[field] = []

        df = pd.DataFrame(extracted)

        # 如果存在 time 列，转换为 datetime 类型并排序
        if "time" in df.columns:
            try:
                df["time"] = pd.to_datetime(df["time"], unit='ms') + pd.Timedelta(hours=8)
            except Exception as e:
                print("DEBUG: Error converting time in fetch_5minute_data:", e)
            # df.drop(columns=['volume'], inplace=True, errors='ignore')
            df.sort_values("time", inplace=True)

        # 检查是否达到 240 个周期数据点
        if len(df) >= MINUTE_CYCLE_COUNT:
            # 取最新 240 行，并格式化 time 列
            df = df.tail(MINUTE_CYCLE_COUNT)
            if "time" in df.columns:
                df["time"] = df["time"].dt.strftime("%Y-%m-%d %H:%M")
            break
        else:
            # 数据不足 240 个周期，往前再扩展14天查询区间
            current_start = current_start - timedelta(days=window_days)
            print(f"DEBUG: 数据不足{MINUTE_CYCLE_COUNT}周期，扩展查询区间到 {current_start.strftime('%Y-%m-%d')} 至 {now.strftime('%Y-%m-%d %H:%M:%S')}")

    return df


def fetch_daily_data(symbol):
    """获取日线级历史数据，返回一个按日排列的 DataFrame（取最新 DAILY_MONTH_COUNT 个月数据）"""
    now = datetime.now()
    # 计算查询区间：取最新 DAILY_MONTH_COUNT 个月的数据（当月及之前 DAILY_MONTH_COUNT-1 个月）
    if now.month >= DAILY_MONTH_COUNT:
        start_year = now.year
        start_month = now.month - (DAILY_MONTH_COUNT - 1)
    else:
        start_year = now.year - 1
        start_month = now.month - (DAILY_MONTH_COUNT - 1) + 12
    # 起始日期为指定月份的第一天
    start_date = datetime(start_year, start_month, 1)
    # 结束日期取当前日期，当日结束时刻设为235959
    end_date = now
    start_time_str = start_date.strftime("%Y%m%d") + "000000"
    end_time_str = end_date.strftime("%Y%m%d") + "235959"

    xtdata.download_history_data(symbol, period='1d', start_time=start_time_str, end_time=end_time_str)
    data = xtdata.get_market_data([], [symbol], period='1d', start_time=start_time_str, end_time=end_time_str,
                                  dividend_type='none')

    extracted = {}
    for field, df_field in data.items():
        if isinstance(df_field, pd.DataFrame) and not df_field.empty:
            extracted[field] = df_field.iloc[0].tolist()
        else:
            extracted[field] = []

    df = pd.DataFrame(extracted)

    if "time" in df.columns:
        try:
            df["time"] = pd.to_datetime(df["time"], unit='ms') + pd.Timedelta(hours=8)
            df["time"] = df["time"].dt.strftime("%Y-%m-%d")
        except Exception as e:
            print("DEBUG: Error converting time in fetch_daily_data:", e)
    # df.drop(columns=['volume'], inplace=True, errors='ignore')
    if "time" in df.columns:
        df.sort_values("time", inplace=True)
    return df


def fetch_30minute_data(symbol):
    """获取30分钟级历史数据，返回一个按30分钟排列的 DataFrame（共 240 个周期）"""
    now = datetime.now()
    window_days = 60
    current_start = now - timedelta(days=window_days)

    while True:
        start_time_str = current_start.strftime("%Y%m%d") + "000000"
        end_time_str = now.strftime("%Y%m%d%H%M%S")
        
        print(f"\nDEBUG: Fetching 30min data from {start_time_str} to {end_time_str}")

        try:
            xtdata.download_history_data(symbol, period='30m', start_time=start_time_str, end_time=end_time_str)
            data = xtdata.get_market_data([], [symbol], period='30m', start_time=start_time_str, end_time=end_time_str,
                                          dividend_type='none')
            
            print(f"DEBUG: Raw data length before processing: {len(data['time']) if 'time' in data else 0}")
            
            extracted = {}
            for field, df_field in data.items():
                if isinstance(df_field, pd.DataFrame) and not df_field.empty:
                    extracted[field] = df_field.iloc[0].tolist()
                else:
                    extracted[field] = []

            df = pd.DataFrame(extracted)

            if "time" in df.columns:
                try:
                    df["time"] = pd.to_datetime(df["time"], unit='ms') + pd.Timedelta(hours=8)
                    df = df[df["time"] <= now]
                    print(f"DEBUG: Data length after filtering: {len(df)}")
                    
                    # 打印所有数据点
                    print("\n=== 所有30分钟数据点 ===")
                    pd.set_option('display.max_rows', None)  # 显示所有行
                    print(df[['time', 'open', 'high', 'low', 'close', 'volume']].to_string())
                    print("=====================\n")
                    
                except Exception as e:
                    print("DEBUG: Error converting time in fetch_30minute_data:", e)
                df.sort_values("time", inplace=True)

            if len(df) >= MINUTE_CYCLE_COUNT:
                df = df.tail(MINUTE_CYCLE_COUNT)
                if "time" in df.columns:
                    df["time"] = df["time"].dt.strftime("%Y-%m-%d %H:%M")
                print(f"DEBUG: Final data length: {len(df)}")
                break
            else:
                print(f"DEBUG: Not enough data points ({len(df)} < {MINUTE_CYCLE_COUNT})")
                current_start = current_start - timedelta(days=window_days)
                continue

        except Exception as e:
            err_msg = str(e)
            print(f"DEBUG: Error fetching data: {err_msg}")
            from PySide6.QtWidgets import QMessageBox
            if "invalid stockcode" in err_msg.lower() or "invalid stock" in err_msg.lower():
                QMessageBox.warning(None, "警告", "不是正确的股票代码")
            else:
                QMessageBox.critical(None, "错误", err_msg)
            return pd.DataFrame()

    return df


class DataUpdateThread(QThread):
    """后台数据更新线程"""
    data_updated = Signal(pd.Series)  # 信号：用于发送新数据到主线程

    def __init__(self, symbol, parent=None):
        super().__init__(parent)
        self.symbol = symbol
        self.running = True
        self.last_timestamp = None

    def run(self):
        while self.running:
            try:
                # 获取当前时间
                now = datetime.now()
                # 构造查询时间范围（最近25分钟，确保能获取到5分钟数据）
                end_time = now.strftime("%Y%m%d%H%M%S")
                start_time = (now - timedelta(minutes=25)).strftime("%Y%m%d%H%M%S")

                # 下载最新数据
                xtdata.download_history_data(self.symbol, period='5m',  # 改为 5m
                                         start_time=start_time, 
                                         end_time=end_time)
                data = xtdata.get_market_data([], [self.symbol], period='5m',  # 改为 5m
                                           start_time=start_time,
                                           end_time=end_time,
                                           dividend_type='none')

                # 转换数据格式
                extracted = {}
                for field, df_field in data.items():
                    if isinstance(df_field, pd.DataFrame) and not df_field.empty:
                        extracted[field] = df_field.iloc[0].tolist()
                    else:
                        extracted[field] = []

                df = pd.DataFrame(extracted)

                if not df.empty and "time" in df.columns:
                    # 转换时间戳
                    df["time"] = pd.to_datetime(df["time"], unit='ms') + pd.Timedelta(hours=8)
                    df["time"] = df["time"].dt.strftime("%Y-%m-%d %H:%M")
                    df = df.sort_values("time")

                    # 获取最新一条记录
                    latest_data = df.iloc[-1]
                    current_timestamp = latest_data["time"]

                    # 如果时间戳不同，发送更新信号
                    if current_timestamp != self.last_timestamp:
                        self.last_timestamp = current_timestamp
                        # 直接发送信号，让 ChartTab 处理缓存更新
                        self.data_updated.emit(latest_data)

            except Exception as e:
                print(f"DEBUG: Error in update thread: {str(e)}")

            # 休眠3秒
            time.sleep(3)

    def stop(self):
        self.running = False


def convert_to_heikin_ashi(df):
    """将普通K线数据转换为HeikinAshi格式"""
    ha_df = df.copy()
    
    # 计算HeikinAshi的open、high、low、close
    ha_df['ha_close'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4
    
    # 计算第一个HeikinAshi open
    ha_df.loc[ha_df.index[0], 'ha_open'] = (df.loc[df.index[0], 'open'] + df.loc[df.index[0], 'close']) / 2
    
    # 计算其余的HeikinAshi open
    for i in range(1, len(df)):
        ha_df.loc[ha_df.index[i], 'ha_open'] = (ha_df.loc[ha_df.index[i-1], 'ha_open'] + 
                                               ha_df.loc[ha_df.index[i-1], 'ha_close']) / 2
    
    # 计算HeikinAshi high和low
    ha_df['ha_high'] = ha_df[['high', 'ha_open', 'ha_close']].max(axis=1)
    ha_df['ha_low'] = ha_df[['low', 'ha_open', 'ha_close']].min(axis=1)
    
    # 替换原始OHLC数据
    ha_df['open'] = ha_df['ha_open']
    ha_df['high'] = ha_df['ha_high']
    ha_df['low'] = ha_df['ha_low']
    ha_df['close'] = ha_df['ha_close']
    
    # 删除临时列
    ha_df = ha_df.drop(['ha_open', 'ha_high', 'ha_low', 'ha_close'], axis=1)
    
    return ha_df


class ChartTab(QWidget):
    """单个标签页的图表容器，包含2x2网格的图表"""
    def __init__(self, parent=None, symbol="600895.SH"):
        super().__init__(parent)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)  # 减少图表之间的间距
        
        # 创建该标签页的私有缓存
        self.history_cache = {}
        self.current_symbol = symbol
        
        # 创建网格布局
        self.grid_layout = QGridLayout()
        self.grid_layout.setContentsMargins(0, 0, 0, 0)
        self.grid_layout.setSpacing(1)  # 设置网格间距
        
        # 修改时间周期选项
        PERIODS = ('5min', '30min', '1day')
        
        # 创建四个独立的图表
        self.charts = []
        for i in range(4):
            chart = QtChart(self)
            # 为每个图表添加独立的控制栏
            chart.topbar.textbox('symbol', symbol)
            chart.topbar.switcher('timeframe', PERIODS, default=PERIODS[0],
                                func=lambda c, idx=i: self.on_timeframe_selection(c, idx))
            chart.topbar.switcher('style', ('Candlestick', 'HeikinAshi'), default='Candlestick', 
                                func=lambda c, idx=i: self.update_chart_style(c, idx))
            
            # 绑定搜索事件
            chart.events.search += lambda c, s, idx=i: self.on_search(c, s, idx)
            
            # 设置图表大小策略
            webview = chart.get_webview()
            webview.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            
            # 添加水印
            chart.watermark(str(i + 1))
            
            # 将图表添加到网格布局中
            row = i // 2  # 0, 0, 1, 1
            col = i % 2   # 0, 1, 0, 1
            self.grid_layout.addWidget(webview, row, col)
            
            self.charts.append(chart)
        
        # 将网格布局添加到主布局
        self.layout.addLayout(self.grid_layout)
        
        # 初始加载数据
        for i, chart in enumerate(self.charts):
            self.on_timeframe_selection(chart, i)
        
        # 启动单个更新线程
        self.update_thread = self.start_update_thread(symbol)

    def start_update_thread(self, symbol):
        """启动更新线程"""
        thread = DataUpdateThread(symbol, self)
        thread.data_updated.connect(self.update_all_charts)
        thread.start()
        return thread

    def update_all_charts(self, new_data):
        """更新所有图表的数据"""
        try:
            # 更新缓存
            cache_key = f"{self.current_symbol}_5min"
            if cache_key in self.history_cache:
                self.update_cache(cache_key, new_data)
            
            # 更新所有图表
            for chart in self.charts:
                if chart.topbar['timeframe'].value == '5min':
                    # 处理 HeikinAshi 转换
                    if chart.topbar['style'].value == 'HeikinAshi':
                        cached_df = self.history_cache[cache_key].copy()
                        temp_df = pd.concat([cached_df, pd.DataFrame([new_data])], ignore_index=True)
                        ha_df = convert_to_heikin_ashi(temp_df)
                        chart_data = ha_df.iloc[-1]
                    else:
                        chart_data = new_data
                    
                    # 更新图表
                    chart.update(chart_data)
                    
                    # 更新成交量子图
                    if hasattr(chart, '_subcharts') and chart._subcharts:
                        vol_data = pd.Series({
                            'time': chart_data['time'],
                            'value': chart_data['volume'],
                            'color': '#26a69a'
                        })
                        chart._subcharts[0]._series[0].update(vol_data)
        except Exception as e:
            print(f"DEBUG: Error updating charts: {str(e)}")

    def on_search(self, chart, searched_string, chart_index):
        """搜索回调，更新所有子图的股票代码"""
        # 先获取新数据
        new_data = self.get_bar_data(searched_string, chart.topbar['timeframe'].value)
        if new_data.empty:
            return
        
        # 更新当前股票代码
        self.current_symbol = searched_string
        
        # 更新所有子图
        for i, subchart in enumerate(self.charts):
            # 更新股票代码
            subchart.topbar['symbol'].set(searched_string)
            
            # 获取每个子图当前的时间周期和样式
            timeframe = subchart.topbar['timeframe'].value
            style = subchart.topbar['style'].value
            
            # 获取对应时间周期的数据
            if timeframe != chart.topbar['timeframe'].value:
                chart_data = self.get_bar_data(searched_string, timeframe)
            else:
                chart_data = new_data.copy()
            
            # 应用样式转换
            if style == 'HeikinAshi':
                chart_data = convert_to_heikin_ashi(chart_data)
            
            # 更新图表数据
            subchart.set(chart_data)
            
            # 更新成交量子图
            if "volume" in chart_data.columns:
                if not hasattr(subchart, '_subcharts') or not subchart._subcharts:
                    self.create_volume_subchart(subchart, chart_data)
                else:
                    vol_df = chart_data[['time', 'volume']].copy()
                    vol_df['color'] = '#26a69a'
                    subchart._subcharts[0]._series[0].set(vol_df)
        
        # 重启更新线程
        if hasattr(self, 'update_thread'):
            self.update_thread.stop()
            self.update_thread.wait()
        self.update_thread = self.start_update_thread(searched_string)

    def on_timeframe_selection(self, chart, chart_index):
        """每个图表的时间周期切换回调"""
        timeframe = chart.topbar['timeframe'].value
        symbol = chart.topbar['symbol'].value
        
        df = self.get_bar_data(symbol, timeframe)
        
        if chart.topbar['style'].value == 'HeikinAshi':
            df = convert_to_heikin_ashi(df)
        
        chart.set(df)
        
        # 处理成交量子图
        if "volume" in df.columns:
            self.create_volume_subchart(chart, df)

    def update_chart_style(self, chart, chart_index):
        """每个图表的样式更新"""
        style = chart.topbar['style'].value
        timeframe = chart.topbar['timeframe'].value
        symbol = chart.topbar['symbol'].value
        
        cache_key = f"{symbol}_{timeframe}"
        if cache_key in self.history_cache:
            df = self.history_cache[cache_key].copy()
            if style == 'HeikinAshi':
                df = convert_to_heikin_ashi(df)
            
            chart.set(df)
            
            # 更新成交量子图
            if "volume" in df.columns and hasattr(chart, '_subcharts') and chart._subcharts:
                vol_df = df[['time', 'volume']].copy()
                vol_df['color'] = '#26a69a'
                chart._subcharts[0]._series[0].set(vol_df)

    def create_volume_subchart(self, chart, df):
        """为指定图表创建成交量子图"""
        vol_df = df[['time', 'volume']].copy()
        vol_df['color'] = '#26a69a'
        vol_chart = chart.create_subchart(
            position='bottom',
            width=1,
            height=0.25,
            sync=True,
            sync_crosshairs_only=False,
            scale_candles_only=False,
            toolbox=False
        )
        vol_hist = vol_chart.create_histogram(
            name="volume",
            color='#26a69a',
            price_line=False,
            price_label=False,
            scale_margin_top=0.8,
            scale_margin_bottom=0
        )
        vol_hist.set(vol_df)

    def update_cache(self, cache_key, new_data):
        """更新缓存数据"""
        cached_df = self.history_cache[cache_key]
        existing_row_idx = cached_df[cached_df['time'] == new_data['time']].index
        if len(existing_row_idx) > 0:
            self.history_cache[cache_key].loc[existing_row_idx[0]] = new_data
        else:
            self.history_cache[cache_key] = pd.concat([cached_df, pd.DataFrame([new_data])], 
                                                    ignore_index=True)
            if len(self.history_cache[cache_key]) > MINUTE_CYCLE_COUNT:
                self.history_cache[cache_key] = self.history_cache[cache_key].tail(MINUTE_CYCLE_COUNT)

    def get_bar_data(self, symbol, timeframe):
        """获取数据（使用标签页私有缓存）"""
        cache_key = f"{symbol}_{timeframe}"
        if cache_key in self.history_cache:
            print(f"DEBUG: Using cached data for {cache_key}")
            return self.history_cache[cache_key].copy()
        
        print(f"DEBUG: Fetching data for {cache_key}")
        if timeframe == '5min':
            new_data = fetch_5minute_data(symbol)
        elif timeframe == '30min':
            new_data = fetch_30minute_data(symbol)
        elif timeframe == '1day':
            new_data = fetch_daily_data(symbol)
        else:
            print("DEBUG: Unknown timeframe selected")
            return pd.DataFrame()
        
        self.history_cache[cache_key] = new_data.copy()
        return new_data


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Multi-Chart Viewer")
        self.resize(1200, 800)
        
        # 创建中心部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)  # 允许关闭标签页
        self.tab_widget.tabCloseRequested.connect(self.close_tab)
        
        # 设置标签页样式
        self.tab_widget.setStyleSheet("""
            QTabBar::tab {
                padding: 8px;
                margin: 2px;
                border: 1px solid #C4C4C4;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                background-color: #F0F0F0;
            }
            QTabBar::tab:selected {
                background-color: #4A90E2;  /* 选中标签页的背景色 */
                color: white;               /* 选中标签页的文字颜色 */
            }
            QTabBar::tab:hover:!selected {
                background-color: #E3E3E3;  /* 鼠标悬停时的背景色 */
            }
            QTabBar::close-button {
                image: url(close.png);      /* 关闭按钮图标 */
                subcontrol-position: right;
            }
            QTabBar::close-button:hover {
                background-color: #FF6B6B;  /* 鼠标悬停在关闭按钮上时的颜色 */
                border-radius: 2px;
            }
        """)
        
        # 添加标签页控件到主布局
        self.main_layout.addWidget(self.tab_widget)
        
        # 创建第一个标签页
        self.add_new_tab(is_first=True)
        
        # 添加 "+" 按钮作为特殊的标签页
        self.add_button_tab = QWidget()
        add_tab_index = self.tab_widget.addTab(self.add_button_tab, "+")
        
        # 设置 "+" 按钮标签页的样式
        self.tab_widget.tabBar().setTabButton(add_tab_index, QTabBar.RightSide, None)
        self.tab_widget.tabBar().setTabTextColor(add_tab_index, Qt.red)
        
        # 连接标签页切换信号
        self.tab_widget.tabBarClicked.connect(self.handle_tab_click)

    def handle_tab_click(self, index):
        """处理标签页点击事件"""
        # 如果点击的是最后一个标签页（"+" 按钮）
        if index == self.tab_widget.count() - 1:
            self.add_new_tab()

    def add_new_tab(self, is_first=False):
        """添加新的标签页"""
        # 创建新的图表标签页
        new_tab = ChartTab(self)
        # 在 "+" 按钮标签页之前插入新标签页
        tab_index = self.tab_widget.count() - 1 if self.tab_widget.count() > 0 else 0
        self.tab_widget.insertTab(tab_index, new_tab, f"Chart {tab_index + 1}")
        
        # 如果是第一个标签页，移除关闭按钮
        if is_first:
            self.tab_widget.tabBar().setTabButton(0, QTabBar.RightSide, None)
        
        self.tab_widget.setCurrentIndex(tab_index)

    def close_tab(self, index):
        """关闭标签页"""
        # 不允许关闭第一个标签页和 "+" 按钮标签页
        if index == 0 or index == self.tab_widget.count() - 1:
            return
            
        # 保存当前要关闭的标签页的前一个标签页的索引
        prev_index = index - 1
            
        widget = self.tab_widget.widget(index)
        if hasattr(widget, 'main_chart') and hasattr(widget.main_chart, 'update_thread'):
            widget.main_chart.update_thread.stop()
            widget.main_chart.update_thread.wait()
        self.tab_widget.removeTab(index)
        
        # 重新编号剩余的标签页（跳过第一个标签页）
        for i in range(1, self.tab_widget.count() - 1):  # -1 是因为不包括 "+" 按钮标签页
            self.tab_widget.setTabText(i, f"Chart {i + 1}")
        
        # 切换到前一个标签页
        self.tab_widget.setCurrentIndex(prev_index)

    def closeEvent(self, event):
        """程序关闭时清理资源"""
        # 只清理图表标签页，不包括 "+" 按钮标签页
        for i in range(self.tab_widget.count() - 1):
            widget = self.tab_widget.widget(i)
            if hasattr(widget, 'main_chart') and hasattr(widget.main_chart, 'update_thread'):
                widget.main_chart.update_thread.stop()
                widget.main_chart.update_thread.wait()
        event.accept()

# 修改主程序部分
if __name__ == "__main__":
    app = QApplication([])
    
    window = MainWindow()
    window.show()
    
    app.exec_()
