#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试实时更新逻辑
测试11:30时各个时间周期的实时更新是否正常工作
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'PySide6'))

import pandas as pd
from datetime import datetime

def is_trading_time(dt):
    """检查是否在股票交易时间内"""
    hour = dt.hour
    minute = dt.minute

    # 上午交易时间：9:30-11:30（包含11:30）
    if hour == 9 and minute >= 30:
        return True
    elif hour == 10:
        return True
    elif hour == 11 and minute <= 30:
        return True

    # 下午交易时间：13:00-15:00（包含15:00）
    elif hour == 13:
        return True
    elif hour == 14:
        return True
    elif hour == 15 and minute == 0:
        return True

    return False

def test_realtime_update_conditions():
    """测试实时更新条件"""
    print("测试实时更新条件")
    print("="*60)
    
    # 模拟11:30的1分钟数据
    test_data_1130 = {
        'time': '2025-07-14 11:30',
        'open': 25.50,
        'high': 25.55,
        'low': 25.48,
        'close': 25.54,
        'volume': 1000
    }
    
    # 模拟13:00的1分钟数据
    test_data_1300 = {
        'time': '2025-07-14 13:00',
        'open': 25.55,
        'high': 25.58,
        'low': 25.52,
        'close': 25.57,
        'volume': 1200
    }
    
    test_cases = [
        ('11:30', test_data_1130),
        ('13:00', test_data_1300)
    ]
    
    for time_desc, test_data in test_cases:
        print(f"\n🕐 测试时间: {time_desc}")
        current_time = datetime.strptime(test_data['time'], '%Y-%m-%d %H:%M')
        
        # 1. 检查是否在交易时间内
        is_trading = is_trading_time(current_time)
        print(f"  交易时间检查: {'✓ 通过' if is_trading else '✗ 失败'}")
        
        if not is_trading:
            print(f"  → 不在交易时间内，所有周期都不会更新")
            continue
        
        # 2. 检查各个时间周期的更新条件
        print(f"  各周期更新条件检查:")
        
        # 1分钟周期：每分钟都更新
        update_1min = True
        print(f"    1min: {'✓ 会更新' if update_1min else '✗ 不更新'}")
        
        # 5分钟周期：每5分钟更新
        update_5min = current_time.minute % 5 == 0
        print(f"    5min: {'✓ 会更新' if update_5min else '✗ 不更新'} (分钟数: {current_time.minute}, 5分钟边界: {current_time.minute % 5 == 0})")
        
        # 15分钟周期：检查是否是有效的15分钟边界
        valid_15min_boundaries = [
            (9, 45), (10, 0), (10, 15), (10, 30), (10, 45), (11, 0), (11, 15), (11, 30),  # 上午
            (13, 15), (13, 30), (13, 45), (14, 0), (14, 15), (14, 30), (14, 45), (15, 0)  # 下午
        ]
        current_boundary = (current_time.hour, current_time.minute)
        update_15min = current_boundary in valid_15min_boundaries
        print(f"    15min: {'✓ 会更新' if update_15min else '✗ 不更新'} (边界: {current_boundary}, 有效: {current_boundary in valid_15min_boundaries})")
        
        # 60分钟周期：检查是否是整点或特殊边界
        valid_60min_boundaries = [(10, 0), (11, 30), (14, 0), (15, 0)]
        update_60min = current_boundary in valid_60min_boundaries
        print(f"    60min: {'✓ 会更新' if update_60min else '✗ 不更新'} (边界: {current_boundary}, 有效: {current_boundary in valid_60min_boundaries})")
        
        # 日线周期：只在15:00更新
        update_daily = current_time.hour == 15 and current_time.minute == 0
        print(f"    1day: {'✓ 会更新' if update_daily else '✗ 不更新'}")

def test_update_chart_data_logic():
    """测试update_chart_data函数的逻辑"""
    print(f"\n{'='*60}")
    print("测试update_chart_data函数逻辑:")
    
    # 模拟图表对象
    class MockChart:
        def __init__(self, timeframe):
            self.topbar = {'timeframe': MockValue(timeframe), 'symbol': MockValue('600895')}
    
    class MockValue:
        def __init__(self, value):
            self.value = value
    
    # 测试不同时间周期
    timeframes = ['1min', '5min', '15min', '60min', '1day']
    
    test_data_1130 = {
        'time': '2025-07-14 11:30',
        'open': 25.50,
        'high': 25.55,
        'low': 25.48,
        'close': 25.54,
        'volume': 1000
    }
    
    for timeframe in timeframes:
        print(f"\n📊 测试 {timeframe} 周期:")
        chart = MockChart(timeframe)
        
        # 模拟update_chart_data的逻辑
        current_timeframe = chart.topbar['timeframe'].value
        symbol = chart.topbar['symbol'].value
        
        print(f"  当前周期: {current_timeframe}")
        print(f"  股票代码: {symbol}")
        
        if current_timeframe == '1min':
            print(f"  → 1分钟周期：直接使用实时数据")
            chart_data = test_data_1130
            print(f"  → 会更新图表: ✓")
            
        elif current_timeframe == '5min':
            print(f"  → 5分钟周期：调用update_5min_realtime")
            # 模拟update_5min_realtime的返回
            current_time = datetime.strptime(test_data_1130['time'], '%Y-%m-%d %H:%M')
            if is_trading_time(current_time) and current_time.minute % 5 == 0:
                print(f"  → update_5min_realtime会返回数据: ✓")
                print(f"  → 会更新图表: ✓")
            else:
                print(f"  → update_5min_realtime会返回None: ✗")
                print(f"  → 不会更新图表: ✗")
                
        elif current_timeframe == '15min':
            print(f"  → 15分钟周期：调用update_15min_realtime")
            current_time = datetime.strptime(test_data_1130['time'], '%Y-%m-%d %H:%M')
            valid_boundaries = [
                (9, 45), (10, 0), (10, 15), (10, 30), (10, 45), (11, 0), (11, 15), (11, 30),
                (13, 15), (13, 30), (13, 45), (14, 0), (14, 15), (14, 30), (14, 45), (15, 0)
            ]
            current_boundary = (current_time.hour, current_time.minute)
            if is_trading_time(current_time) and current_boundary in valid_boundaries:
                print(f"  → update_15min_realtime会返回数据: ✓")
                print(f"  → 会更新图表: ✓")
            else:
                print(f"  → update_15min_realtime会返回None: ✗")
                print(f"  → 不会更新图表: ✗")
                
        elif current_timeframe == '60min':
            print(f"  → 60分钟周期：调用update_60min_realtime")
            current_time = datetime.strptime(test_data_1130['time'], '%Y-%m-%d %H:%M')
            valid_boundaries = [(10, 0), (11, 30), (14, 0), (15, 0)]
            current_boundary = (current_time.hour, current_time.minute)
            if is_trading_time(current_time) and current_boundary in valid_boundaries:
                print(f"  → update_60min_realtime会返回数据: ✓")
                print(f"  → 会更新图表: ✓")
            else:
                print(f"  → update_60min_realtime会返回None: ✗")
                print(f"  → 不会更新图表: ✗")
                
        elif current_timeframe == '1day':
            print(f"  → 日线周期：调用update_daily_realtime")
            current_time = datetime.strptime(test_data_1130['time'], '%Y-%m-%d %H:%M')
            if current_time.hour == 15 and current_time.minute == 0:
                print(f"  → update_daily_realtime会返回数据: ✓")
                print(f"  → 会更新图表: ✓")
            else:
                print(f"  → update_daily_realtime会返回None: ✗")
                print(f"  → 不会更新图表: ✗")

def test_data_update_thread_logic():
    """测试DataUpdateThread的逻辑"""
    print(f"\n{'='*60}")
    print("测试DataUpdateThread逻辑:")
    
    # 模拟11:30的情况
    time_str = '2025-07-14 11:30'
    dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
    
    print(f"模拟时间: {time_str}")
    
    # 检查交易时间
    if not is_trading_time(dt):
        print(f"✗ 非交易时间，跳过更新")
        return
    else:
        print(f"✓ 交易时间，继续处理")
    
    # 模拟数据更新
    print(f"✓ 更新1分钟缓存")
    print(f"✓ 发送data_updated信号")
    print(f"✓ 调用update_chart_data函数")

if __name__ == '__main__':
    test_realtime_update_conditions()
    test_update_chart_data_logic()
    test_data_update_thread_logic()
    
    print(f"\n{'='*60}")
    print("🔍 调试结论:")
    print()
    print("11:30时各周期的预期行为:")
    print("- 1min: ✓ 应该更新（每分钟都更新）")
    print("- 5min: ✓ 应该更新（11:30是5分钟边界）")
    print("- 15min: ✓ 应该更新（11:30是15分钟边界）")
    print("- 60min: ✓ 应该更新（11:30是60分钟边界）")
    print("- 1day: ✗ 不应该更新（只在15:00更新）")
    print()
    print("如果某些周期没有显示11:30的K线，可能的原因:")
    print("1. 缓存中没有足够的1分钟数据")
    print("2. 实时更新函数的数据合成逻辑有问题")
    print("3. 图表更新逻辑有问题")
    print("4. 时间边界检查逻辑有问题")
