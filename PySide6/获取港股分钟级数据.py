#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股分钟级数据获取工具
使用腾讯API接口获取港股02603.HK的1分钟级别实时K线数据
每30秒打印一次OHLC数据

参考：https://www.cnblogs.com/cfld/p/16130599.html
"""

import requests
import json
import time
import threading
from datetime import datetime, timedelta
import pandas as pd
import os
import logging
import akshare as ak
from concurrent.futures import ThreadPoolExecutor, as_completed
import asyncio
import aiohttp
import mplfinance as mpf
import numpy as np
import sys

# 导入xtquant
try:
    from xtquant import xtdata
    XTQUANT_AVAILABLE = True
    print("✅ xtquant导入成功")
except ImportError:
    XTQUANT_AVAILABLE = False
    print("❌ xtquant未安装，将使用腾讯API获取数据")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 腾讯API配置
HK_TENCENT_MINUTE_URL = "https://web.ifzq.gtimg.cn/appstock/app/minute/query?code={}"

# 请求头
HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/100.0.4896.75 "
        "Safari/537.36"
    ),
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Referer": "https://gu.qq.com/"
}

# 港股通股票代码字典 - 动态获取
HK_STOCKS = {}

def get_hk_ggt_stocks():
    """
    获取港股通成分股列表

    返回:
    dict: {股票代码: 股票名称}
    """
    try:
        print("📡 正在获取港股通成分股列表...")
        df = ak.stock_hk_ggt_components_em()

        print(f"📊 akshare返回数据: {len(df)} 只股票")
        print(f"📋 数据列名: {df.columns.tolist()}")

        # 转换为字典格式
        stocks_dict = {}
        for _, row in df.iterrows():
            code = str(row['代码']).zfill(5)  # 确保代码为5位数字
            name = row['名称']
            # 确保代码格式为 XXXXX.HK
            if not code.endswith('.HK'):
                code = code + '.HK'
            stocks_dict[code] = name

        print(f"✅ 成功获取 {len(stocks_dict)} 只港股通股票")

        # 显示前10只股票作为预览
        print("📈 前10只股票预览:")
        for i, (code, name) in enumerate(list(stocks_dict.items())[:10]):
            print(f"   {i+1:2d}. {code} - {name}")

        return stocks_dict

    except Exception as e:
        logger.error(f"获取港股通成分股失败: {e}")
        print(f"❌ 获取港股通数据失败: {e}")
        print("🔄 使用默认股票列表...")
        # 如果获取失败，返回默认的几只股票
        return {
            '00700.HK': '腾讯控股',
            '09988.HK': '阿里巴巴',
            '00941.HK': '中国移动',
            '03690.HK': '美团-W',
            '02318.HK': '中国平安'
        }

# 移除CSV保存功能，只打印数据


def is_hk_trading_time():
    """判断当前是否为港股交易时间段"""
    now = datetime.now()
    current_time = now.time()

    # 定义港股交易时间段
    morning_start = datetime.strptime("09:30", "%H:%M").time()
    morning_end = datetime.strptime("12:00", "%H:%M").time()
    afternoon_start = datetime.strptime("13:00", "%H:%M").time()
    afternoon_end = datetime.strptime("16:00", "%H:%M").time()

    # 判断是否在交易时间段内
    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session


def tencent_hk_minute(stock_code, latest_minute=True):
    """
    使用腾讯API获取港股1分钟K线数据

    参数:
    stock_code: 港股代码，如 '00700.HK'
    latest_minute: 是否只返回最新一分钟数据，默认True

    返回:
    dict或list: K线数据，包含OHLC和成交量
    """
    try:
        # 转换股票代码格式：00700.HK -> hk00700
        if stock_code.endswith('.HK'):
            prefix_code = "hk" + stock_code[:-3]
        else:
            prefix_code = "hk" + stock_code

        url = HK_TENCENT_MINUTE_URL.format(prefix_code)
        logger.info(f"请求URL: {url}")

        # 发送请求
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status()

        # 解析JSON数据
        data_json = response.json()
        logger.debug(f"API响应: {json.dumps(data_json, indent=2, ensure_ascii=False)}")

        # 检查数据结构
        if ("data" in data_json and
            prefix_code in data_json["data"] and
            "data" in data_json["data"][prefix_code] and
            "data" in data_json["data"][prefix_code]["data"] and
            data_json["data"][prefix_code]["data"]["data"]):

            # 获取日期和前收盘价
            date_str = data_json["data"][prefix_code]["data"]["date"]
            date = date_str[:4] + "-" + date_str[4:6] + "-" + date_str[6:]

            # 获取前收盘价作为第一个开盘价的参考
            pre_close = float(data_json["data"][prefix_code]["data"].get("preClose", 0))

            # 解析K线数据
            kline_list = []
            prev_volume = 0
            prev_close = pre_close  # 用于计算开盘价

            for i, item in enumerate(data_json["data"][prefix_code]["data"]["data"]):
                parts = item.split(" ")
                if len(parts) >= 3:
                    time_str = parts[0]
                    current_price = float(parts[1])
                    cumulative_volume = float(parts[2])

                    # 计算当前分钟的成交量（累计成交量的差值）
                    current_volume = cumulative_volume - prev_volume
                    prev_volume = cumulative_volume

                    # 格式化时间
                    time_formatted = time_str[:2] + ":" + time_str[2:] + ":00"
                    datetime_str = date + " " + time_formatted

                    # 构建OHLC数据
                    # 由于腾讯API只提供当前价格，我们需要从连续的价格数据中推算OHLC
                    if i == 0:
                        # 第一个数据点
                        open_price = prev_close if prev_close > 0 else current_price
                        high_price = max(open_price, current_price)
                        low_price = min(open_price, current_price)
                        close_price = current_price
                    else:
                        # 后续数据点，开盘价为前一分钟的收盘价
                        open_price = prev_close
                        # 由于只有当前价格，假设高低价就是当前价格
                        # 这是API限制，实际应用中可能需要更频繁的数据来构建准确的OHLC
                        high_price = max(open_price, current_price)
                        low_price = min(open_price, current_price)
                        close_price = current_price

                    kline_data = {
                        "datetime": datetime_str,
                        "time": datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S"),
                        "open": round(open_price, 3),
                        "high": round(high_price, 3),
                        "low": round(low_price, 3),
                        "close": round(close_price, 3),
                        "volume": max(0, current_volume),  # 确保成交量非负
                        "stock_code": stock_code,
                        "stock_type": "hk",
                        "source": "tencent"
                    }

                    kline_list.append(kline_data)
                    prev_close = close_price  # 更新前一个收盘价

            if latest_minute and kline_list:
                return kline_list[-1]
            else:
                return kline_list

        else:
            logger.warning(f"数据结构异常，无法解析K线数据: {stock_code}")
            return None if latest_minute else []

    except requests.exceptions.RequestException as e:
        logger.error(f"网络请求失败: {e}")
        return None if latest_minute else []
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析失败: {e}")
        return None if latest_minute else []
    except Exception as e:
        logger.error(f"获取港股数据失败 {stock_code}: {e}")
        return None if latest_minute else []


def get_hk_1min_kline(stock_code):
    """
    获取港股1分钟K线数据的统一接口

    参数:
    stock_code: 港股代码，如 '00700.HK'

    返回:
    dict: 最新的1分钟K线数据，失败返回None
    """
    if stock_code not in HK_STOCKS:
        logger.warning(f"不支持的港股代码: {stock_code}")
        return None

    return tencent_hk_minute(stock_code, latest_minute=True)


def get_single_stock_data(stock_code):
    """
    获取单个股票数据的包装函数，用于并发调用

    参数:
    stock_code: 股票代码

    返回:
    tuple: (股票代码, K线数据)
    """
    try:
        kline = tencent_hk_minute(stock_code, latest_minute=True)
        return stock_code, kline
    except Exception as e:
        logger.error(f"获取 {stock_code} 数据失败: {e}")
        return stock_code, None

def get_multiple_hk_klines_concurrent(stock_codes, max_workers=50):
    """
    并发获取多个港股的1分钟K线数据

    参数:
    stock_codes: 港股代码列表
    max_workers: 最大并发线程数，默认50

    返回:
    dict: {股票代码: K线数据}
    """
    results = {}

    # 使用线程池并发请求
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_stock = {
            executor.submit(get_single_stock_data, stock_code): stock_code
            for stock_code in stock_codes
        }

        # 收集结果
        for future in as_completed(future_to_stock):
            stock_code, kline = future.result()
            results[stock_code] = kline

    return results

def get_multiple_hk_klines(stock_codes):
    """
    批量获取多个港股的1分钟K线数据（兼容性函数）

    参数:
    stock_codes: 港股代码列表，如 ['00700.HK', '09988.HK']

    返回:
    dict: {股票代码: K线数据}
    """
    # 使用并发版本
    return get_multiple_hk_klines_concurrent(stock_codes)


def format_kline_data(kline):
    """
    格式化K线数据为可读字符串

    参数:
    kline: K线数据字典

    返回:
    str: 格式化的字符串
    """
    if not kline:
        return "无数据"

    return (f"时间: {kline['datetime']}, "
            f"开: {kline['open']:.3f}, "
            f"高: {kline['high']:.3f}, "
            f"低: {kline['low']:.3f}, "
            f"收: {kline['close']:.3f}, "
            f"量: {kline['volume']:,.0f}, "
            f"源: {kline['source']}")


def print_kline_data(stock_code, kline_data):
    """
    打印K线数据到控制台

    参数:
    stock_code: 股票代码
    kline_data: K线数据
    """
    if not kline_data:
        print(f"❌ {stock_code}: 无数据")
        return

    stock_name = HK_STOCKS.get(stock_code, stock_code)
    print(f"📊 {stock_name} ({stock_code}):")
    print(f"   时间: {kline_data['datetime']}")
    print(f"   开盘: {kline_data['open']:8.3f} HKD")
    print(f"   最高: {kline_data['high']:8.3f} HKD")
    print(f"   最低: {kline_data['low']:8.3f} HKD")
    print(f"   收盘: {kline_data['close']:8.3f} HKD")
    print(f"   成交量: {kline_data['volume']:,.0f}")
    print(f"   数据源: {kline_data['source']}")
    print()


class HKStockRealTimeMonitor:
    """港股实时数据监控器"""

    def __init__(self, stock_codes=None, update_interval=60):
        """
        初始化监控器

        参数:
        stock_codes: 要监控的股票代码列表，默认监控所有支持的港股
        update_interval: 更新间隔（秒），默认60秒
        """
        self.stock_codes = stock_codes or list(HK_STOCKS.keys())
        self.update_interval = update_interval
        self.running = False
        self.thread = None
        self.latest_data = {}

    def start(self):
        """开始监控"""
        if self.running:
            logger.warning("监控器已在运行中")
            return

        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        logger.info(f"开始监控港股数据，更新间隔: {self.update_interval}秒")

    def stop(self):
        """停止监控"""
        self.running = False
        if self.thread:
            self.thread.join()
        logger.info("港股数据监控已停止")

    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                print(f"\n🔄 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("=" * 60)

                # 获取所有股票的最新数据
                results = get_multiple_hk_klines(self.stock_codes)

                for stock_code, kline in results.items():
                    if kline:
                        self.latest_data[stock_code] = kline
                        print_kline_data(stock_code, kline)
                    else:
                        print(f"❌ {HK_STOCKS.get(stock_code, stock_code)} ({stock_code}): 获取数据失败")

                print("=" * 60)
                print(f"⏰ 下次更新: {self.update_interval}秒后")

                # 等待下次更新
                time.sleep(self.update_interval)

            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(5)  # 出错时短暂等待

    def get_latest_data(self):
        """获取最新数据"""
        return self.latest_data.copy()


def get_xtquant_data(stock_code, period='15m', count=2200):
    """
    使用xtquant获取港股K线数据

    Args:
        stock_code: 股票代码 (如 '06060.HK')
        period: 时间周期 ('15m', '60m')
        count: 获取的K线数量

    Returns:
        pd.DataFrame: K线数据，包含Open、High、Low、Close、Volume列
    """
    if not XTQUANT_AVAILABLE:
        print("❌ xtquant不可用，无法获取数据")
        return pd.DataFrame()

    try:
        print(f"📊 使用xtquant获取 {stock_code} {period} 周期数据...")

        # 获取市场数据
        data = xtdata.get_market_data(
            field_list=['time', 'open', 'high', 'low', 'close', 'volume'],
            stock_list=[stock_code],
            period=period,
            count=count,
            dividend_type='none'
        )

        if not data:
            print(f"❌ 未获取到任何数据")
            return pd.DataFrame()

        # 调试：打印数据结构
        print(f"🔍 调试信息:")
        print(f"   数据类型: {type(data)}")
        print(f"   数据键: {list(data.keys()) if isinstance(data, dict) else '非字典类型'}")

        # 检查时间数据
        if 'time' not in data:
            print(f"❌ 数据中缺少时间字段")
            return pd.DataFrame()

        time_data = data['time']
        print(f"   时间数据类型: {type(time_data)}")

        if isinstance(time_data, pd.DataFrame):
            print(f"   时间数据形状: {time_data.shape}")
            print(f"   时间数据索引: {time_data.index.tolist()}")
            if stock_code not in time_data.index:
                print(f"❌ 股票代码 {stock_code} 不在时间数据中")
                print(f"   可用的股票代码: {time_data.index.tolist()}")
                return pd.DataFrame()
        else:
            print(f"❌ 时间数据格式错误: {type(time_data)}")
            return pd.DataFrame()

        # 转换为DataFrame格式（修复数据转换逻辑）
        # xtquant返回的数据格式：行为股票代码，列为时间序列
        times = data['time'].loc[stock_code].dropna()
        opens = data['open'].loc[stock_code].dropna()
        highs = data['high'].loc[stock_code].dropna()
        lows = data['low'].loc[stock_code].dropna()
        closes = data['close'].loc[stock_code].dropna()
        volumes = data['volume'].loc[stock_code].dropna()

        print(f"   原始数据长度: times={len(times)}, opens={len(opens)}")

        # 将Series转换为列表，然后创建DataFrame
        times_list = times.tolist()
        opens_list = opens.tolist()
        highs_list = highs.tolist()
        lows_list = lows.tolist()
        closes_list = closes.tolist()
        volumes_list = volumes.tolist()

        # 确保数据长度一致
        min_length = min(len(times_list), len(opens_list), len(highs_list),
                        len(lows_list), len(closes_list), len(volumes_list))

        print(f"   实际使用长度: {min_length}")

        # 创建DataFrame，每一行代表一根K线
        df = pd.DataFrame({
            'Open': opens_list[:min_length],
            'High': highs_list[:min_length],
            'Low': lows_list[:min_length],
            'Close': closes_list[:min_length],
            'Volume': volumes_list[:min_length]
        })

        # 设置时间索引
        df.index = pd.to_datetime(times_list[:min_length], unit='ms')

        print(f"✅ 成功获取 {len(df)} 条 {period} 周期数据")
        return df

    except Exception as e:
        print(f"❌ xtquant数据获取失败: {e}")
        return pd.DataFrame()

def main():
    """主函数 - 港股分钟级数据获取工具"""
    print("🚀 港股分钟级数据获取工具")
    print("=" * 80)

    # 初始化港股通股票列表
    global HK_STOCKS
    HK_STOCKS = get_hk_ggt_stocks()

    if not HK_STOCKS:
        print("❌ 无法获取港股通股票列表，程序退出")
        return

    print(f"✅ 成功加载 {len(HK_STOCKS)} 只港股通股票")
    print("=" * 80)

    try:
        # 创建监控器，监控前5只股票作为示例
        sample_stocks = list(HK_STOCKS.keys())[:5]
        monitor = HKStockRealTimeMonitor(sample_stocks, update_interval=30)

        print(f"📊 开始监控以下股票:")
        for stock_code in sample_stocks:
            stock_name = HK_STOCKS.get(stock_code, stock_code)
            print(f"   {stock_name} ({stock_code})")

        print("=" * 80)

        # 开始监控
        monitor.start()

        # 保持程序运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n👋 程序退出")
        if 'monitor' in locals():
            monitor.stop()
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()