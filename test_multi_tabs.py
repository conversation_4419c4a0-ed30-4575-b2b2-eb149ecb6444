#!/usr/bin/env python3
"""
测试多标签页股票图表功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
import time

def test_multi_tabs():
    """测试多标签页功能"""
    print("开始测试多标签页功能...")
    
    # 导入主程序模块
    try:
        from PySide6 import pyside6_2_28优秀不卡实时数据版 as main_app
        print("✓ 主程序模块导入成功")
    except ImportError as e:
        print(f"✗ 主程序模块导入失败: {e}")
        return False
    
    # 检查标签页管理器是否存在
    if hasattr(main_app, 'tab_manager'):
        print("✓ 标签页管理器存在")
        
        # 检查默认标签页
        if len(main_app.tab_manager.tabs) > 0:
            print(f"✓ 默认标签页已创建，当前标签页数量: {len(main_app.tab_manager.tabs)}")
        else:
            print("✗ 默认标签页未创建")
            return False
            
        # 检查标签页控件
        if hasattr(main_app, 'chart_tab_widget'):
            print("✓ 标签页控件存在")
            print(f"  - 标签页数量: {main_app.chart_tab_widget.count()}")
            print(f"  - 可关闭: {main_app.chart_tab_widget.tabsClosable()}")
            print(f"  - 可移动: {main_app.chart_tab_widget.movable()}")
        else:
            print("✗ 标签页控件不存在")
            return False
            
        # 检查添加按钮
        corner_widget = main_app.chart_tab_widget.cornerWidget()
        if corner_widget:
            print("✓ 添加标签页按钮存在")
        else:
            print("✗ 添加标签页按钮不存在")
            
    else:
        print("✗ 标签页管理器不存在")
        return False
    
    print("✓ 多标签页功能测试通过")
    return True

if __name__ == "__main__":
    print("多标签页股票图表功能测试")
    print("=" * 50)
    
    # 注意：这个测试需要在主程序运行时进行
    print("请先运行主程序，然后测试以下功能：")
    print("1. 点击右上角的 '+' 按钮添加新标签页")
    print("2. 在弹出的对话框中输入新的股票代码（如：000001.SZ）")
    print("3. 验证新标签页是否创建成功")
    print("4. 验证新标签页是否显示正确的股票数据")
    print("5. 尝试关闭标签页（除了第一个）")
    print("6. 验证标签页切换功能")
    
    print("\n功能特点：")
    print("- 每个标签页都有独立的四分屏K线图")
    print("- 标签页标题显示股票代码")
    print("- 支持关闭标签页（至少保留一个）")
    print("- 支持拖拽重排标签页")
    print("- 每个标签页的数据和线程独立管理")
