#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Swing High和Swing Low点检测与绘图
从K线数据中检测swing点并进行可视化
使用xtdata作为数据源
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional
from datetime import datetime, timedelta
import pytz

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入xtdata
try:
    from xtquant import xtdata
    XTDATA_AVAILABLE = True
    logger.info("xtdata导入成功")
except ImportError:
    XTDATA_AVAILABLE = False
    logger.warning("xtdata未安装，请安装: pip install xtquant")

# 绘图相关导入
try:
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    import mplfinance as mpf
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    logger.warning("绘图库未安装，绘图功能将不可用。请安装: pip install matplotlib mplfinance")


class XtDataLoader:
    """xtdata数据加载器"""

    def __init__(self):
        """初始化数据加载器"""
        if not XTDATA_AVAILABLE:
            raise ImportError("xtdata未安装，请安装: pip install xtquant")

    def download_history_data(self, stock_code: str, periods: List[str] = None, days: int = 200) -> bool:
        """
        下载历史数据到本地缓存

        Args:
            stock_code (str): 股票代码，如 "600895.SH" 或 "00700.HK"
            periods (List[str]): 时间周期列表，默认 ['1m', '5m', '15m']
            days (int): 下载天数，默认200天

        Returns:
            bool: 下载是否成功
        """
        if periods is None:
            periods = ['1m', '5m', '15m']

        try:
            # 计算时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)

            start_time_str = start_time.strftime("%Y%m%d%H%M%S")
            end_time_str = end_time.strftime("%Y%m%d%H%M%S")

            logger.info(f"开始下载 {stock_code} 历史数据，时间范围: {start_time_str} - {end_time_str}")

            # 依次下载各个周期的数据
            for period in periods:
                logger.info(f"下载 {period} 周期数据...")
                xtdata.download_history_data(
                    stock_code=stock_code,
                    period=period,
                    start_time=start_time_str,
                    end_time=end_time_str
                )
                logger.info(f"{period} 周期数据下载完成")

            logger.info(f"所有周期历史数据下载完成: {periods}")
            return True

        except Exception as e:
            logger.error(f"下载历史数据失败: {e}")
            return False

    def get_kline_data(self, stock_code: str, period: str = '15m', count: int = 3022) -> pd.DataFrame:
        """
        获取K线数据

        Args:
            stock_code (str): 股票代码，如 "600895.SH" 或 "00700.HK"
            period (str): 时间周期，默认 '15m'
            count (int): 获取数据条数，默认3022条

        Returns:
            pd.DataFrame: K线数据，包含Open、High、Low、Close、Volume列，索引为时间
        """
        try:
            logger.info(f"获取 {stock_code} {period} 周期K线数据，数量: {count}")

            # 定义要获取的字段
            field_list = ['time', 'open', 'high', 'low', 'close', 'volume']

            # 获取市场数据
            data = xtdata.get_market_data(
                field_list=field_list,
                stock_list=[stock_code],
                period=period,
                count=count,
                dividend_type='none',
                fill_data=True
            )

            if not data:
                logger.error(f"未获取到任何数据: {stock_code}")
                return pd.DataFrame()

            # 转换数据格式
            df = self._convert_xtdata_to_dataframe(data, stock_code)

            if df.empty:
                logger.error(f"数据转换失败或数据为空: {stock_code}")
                return pd.DataFrame()

            logger.info(f"成功获取 {len(df)} 条K线数据")
            logger.info(f"时间范围: {df.index[0]} 到 {df.index[-1]}")

            return df

        except Exception as e:
            logger.error(f"获取K线数据失败: {e}")
            return pd.DataFrame()

    def _convert_xtdata_to_dataframe(self, data: dict, stock_code: str) -> pd.DataFrame:
        """
        将xtdata格式的数据转换为标准DataFrame格式

        Args:
            data (dict): xtdata返回的数据
            stock_code (str): 股票代码

        Returns:
            pd.DataFrame: 转换后的DataFrame
        """
        try:
            # 检查数据格式
            if 'time' not in data:
                logger.error("数据中缺少时间字段")
                return pd.DataFrame()

            # 获取时间数据
            time_data = data['time']
            if isinstance(time_data, pd.DataFrame):
                if stock_code in time_data.index:
                    times = time_data.loc[stock_code].dropna().tolist()
                else:
                    logger.error(f"股票代码 {stock_code} 不在时间数据中")
                    return pd.DataFrame()
            else:
                logger.error("时间数据格式不正确")
                return pd.DataFrame()

            if not times:
                logger.error("时间数据为空")
                return pd.DataFrame()

            # 构建DataFrame
            df_data = {}

            # 字段映射
            field_mapping = {
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume'
            }

            for xt_field, df_field in field_mapping.items():
                if xt_field in data:
                    field_data = data[xt_field]
                    if isinstance(field_data, pd.DataFrame) and stock_code in field_data.index:
                        values = field_data.loc[stock_code].dropna().tolist()
                        if len(values) == len(times):
                            df_data[df_field] = values
                        else:
                            logger.warning(f"{xt_field} 数据长度不匹配: {len(values)} vs {len(times)}")
                            df_data[df_field] = [0] * len(times)  # 填充默认值
                    else:
                        logger.warning(f"缺少 {xt_field} 数据")
                        df_data[df_field] = [0] * len(times)  # 填充默认值

            # 创建DataFrame - 修复时间戳转换问题
            # xtdata返回的时间戳可能是毫秒级别的，需要正确转换
            try:
                # 尝试将时间戳转换为正确的日期时间
                datetime_index = []
                for t in times:
                    if isinstance(t, (int, float)):
                        # 如果是数字，可能是时间戳（秒或毫秒）
                        if t > 1e10:  # 毫秒级时间戳
                            dt = pd.to_datetime(t, unit='ms')
                        else:  # 秒级时间戳
                            dt = pd.to_datetime(t, unit='s')
                    else:
                        # 如果是字符串或其他格式，直接转换
                        dt = pd.to_datetime(t)
                    datetime_index.append(dt)

                df = pd.DataFrame(df_data, index=datetime_index)
            except Exception as e:
                logger.warning(f"时间转换失败，使用原始时间: {e}")
                df = pd.DataFrame(df_data, index=pd.to_datetime(times))

            df.index.name = 'time'

            # 确保数据类型正确
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

            logger.info(f"数据转换成功: {len(df)} 条记录")
            return df

        except Exception as e:
            logger.error(f"数据转换失败: {e}")
            return pd.DataFrame()


class SwingPointAnalyzer:
    """Swing点分析器"""

    def __init__(self, swing_length: int = 10):
        """
        初始化分析器

        Args:
            swing_length (int): swing点检测的左右确认周期，默认10
        """
        self.swing_length = swing_length

    def find_swing_points(self, df: pd.DataFrame) -> Tuple[List[Dict], List[Dict]]:
        """
        检测swing high和swing low点，并过滤掉被后续价格穿过的无效点

        Swing High: 当前K线的最高价大于等于左右各swing_length个K线的最高价
        Swing Low: 当前K线的最低价小于等于左右各swing_length个K线的最低价

        Args:
            df (pd.DataFrame): 股票数据，包含High和Low列，索引为时间

        Returns:
            tuple: (swing_highs, swing_lows) 两个包含swing点信息的列表
        """
        swing_highs = []
        swing_lows = []

        # 确保数据足够长
        if len(df) < self.swing_length * 2 + 1:
            logger.warning(f"数据长度不足，需要至少{self.swing_length * 2 + 1}条数据")
            return swing_highs, swing_lows

        # 遍历可能的swing点（排除两端不足swing_length的部分）
        for i in range(self.swing_length, len(df) - self.swing_length):
            current_high = df.iloc[i]['High']
            current_low = df.iloc[i]['Low']
            current_time = df.index[i]

            # 检查swing high: 当前high必须大于等于左右各swing_length个K线的high
            is_swing_high = True
            for j in range(i - self.swing_length, i + self.swing_length + 1):
                if j != i and df.iloc[j]['High'] > current_high:
                    is_swing_high = False
                    break

            if is_swing_high:
                # 检查该阻力线是否被后续价格穿过
                is_valid_resistance = self._check_resistance_validity(df, i, current_high)
                if is_valid_resistance:
                    # 检查成交量是否满足条件（成交量 >= 20日均线 * 1.5）
                    is_volume_qualified = self._check_volume_qualification(df, i)
                    if is_volume_qualified:
                        swing_highs.append({
                            'index': i,
                            'time': current_time,
                            'price': current_high,
                            'type': 'resistance'
                        })

            # 检查swing low: 当前low必须小于等于左右各swing_length个K线的low
            is_swing_low = True
            for j in range(i - self.swing_length, i + self.swing_length + 1):
                if j != i and df.iloc[j]['Low'] < current_low:
                    is_swing_low = False
                    break

            if is_swing_low:
                # 检查该支撑线是否被后续价格穿过
                is_valid_support = self._check_support_validity(df, i, current_low)
                if is_valid_support:
                    # 检查成交量是否满足条件（成交量 >= 20日均线 * 1.5）
                    is_volume_qualified = self._check_volume_qualification(df, i)
                    if is_volume_qualified:
                        swing_lows.append({
                            'index': i,
                            'time': current_time,
                            'price': current_low,
                            'type': 'support'
                        })

        logger.info(f"检测到 {len(swing_highs)} 个符合条件的swing high点，{len(swing_lows)} 个符合条件的swing low点")
        logger.info(f"过滤条件: 成交量 >= 20日均线 * 1.5 且未被后续价格穿过")

        # 创建支撑阻力区域
        resistance_zones, support_zones = self._create_support_resistance_zones(df, swing_highs, swing_lows)

        # 将区域信息添加到swing点中
        for i, swing in enumerate(swing_highs):
            if i < len(resistance_zones):
                swing['zone'] = resistance_zones[i]

        for i, swing in enumerate(swing_lows):
            if i < len(support_zones):
                swing['zone'] = support_zones[i]

        # 识别FVG区域
        bullish_fvgs, bearish_fvgs = self._identify_fvg_zones(df)

        # 打印有效swing点的详细信息
        self._print_swing_points_details(df, swing_highs, swing_lows)

        # 打印FVG区域信息
        self._print_fvg_details(df, bullish_fvgs, bearish_fvgs)

        return swing_highs, swing_lows

    def _check_resistance_validity(self, df: pd.DataFrame, swing_index: int, resistance_price: float) -> bool:
        """
        检查阻力线是否有效（未被后续价格穿过）

        Args:
            df (pd.DataFrame): 股票数据
            swing_index (int): swing点的索引
            resistance_price (float): 阻力线价格

        Returns:
            bool: True表示阻力线有效，False表示被穿过
        """
        # 检查swing点之后的所有K线
        for i in range(swing_index + 1, len(df)):
            # 如果后续K线的最高价超过阻力线，则该阻力线无效
            if df.iloc[i]['High'] > resistance_price:
                logger.debug(f"阻力线 {resistance_price:.2f} 在索引 {i} 被穿过，最高价: {df.iloc[i]['High']:.2f}")
                return False

        return True

    def _check_support_validity(self, df: pd.DataFrame, swing_index: int, support_price: float) -> bool:
        """
        检查支撑线是否有效（未被后续价格穿过）

        Args:
            df (pd.DataFrame): 股票数据
            swing_index (int): swing点的索引
            support_price (float): 支撑线价格

        Returns:
            bool: True表示支撑线有效，False表示被穿过
        """
        # 检查swing点之后的所有K线
        for i in range(swing_index + 1, len(df)):
            # 如果后续K线的最低价低于支撑线，则该支撑线无效
            if df.iloc[i]['Low'] < support_price:
                logger.debug(f"支撑线 {support_price:.2f} 在索引 {i} 被穿过，最低价: {df.iloc[i]['Low']:.2f}")
                return False

        return True

    def _create_support_resistance_zones(self, df: pd.DataFrame, swing_highs: List[Dict], swing_lows: List[Dict], zone_range: int = 5) -> Tuple[List[Dict], List[Dict]]:
        """
        为swing点创建支撑阻力区域（矩形zone）

        Args:
            df (pd.DataFrame): 股票数据
            swing_highs (List[Dict]): swing high点列表
            swing_lows (List[Dict]): swing low点列表
            zone_range (int): 区域范围（前后多少根K线），默认5

        Returns:
            Tuple[List[Dict], List[Dict]]: (阻力区域列表, 支撑区域列表)
        """
        resistance_zones = []
        support_zones = []

        # 为每个swing high点创建阻力区域
        for swing in swing_highs:
            zone = self._create_zone_for_swing_point(df, swing, 'resistance', zone_range)
            if zone:
                resistance_zones.append(zone)

        # 为每个swing low点创建支撑区域
        for swing in swing_lows:
            zone = self._create_zone_for_swing_point(df, swing, 'support', zone_range)
            if zone:
                support_zones.append(zone)

        logger.info(f"创建了 {len(resistance_zones)} 个阻力区域，{len(support_zones)} 个支撑区域")
        return resistance_zones, support_zones

    def _create_zone_for_swing_point(self, df: pd.DataFrame, swing: Dict, zone_type: str, zone_range: int) -> Dict:
        """
        为单个swing点创建支撑阻力区域
        基于swing点价格和对应K线实体边界创建区域

        Args:
            df (pd.DataFrame): 股票数据
            swing (Dict): swing点信息
            zone_type (str): 区域类型 ('resistance' 或 'support')
            zone_range (int): 区域范围（此参数在新逻辑中不使用）

        Returns:
            Dict: 区域信息
        """
        try:
            swing_index = swing['index']
            swing_price = swing['price']

            # 获取swing点对应的K线数据
            swing_candle = df.iloc[swing_index]
            candle_open = swing_candle['Open']
            candle_close = swing_candle['Close']

            if zone_type == 'resistance':
                # 阻力区域：从swing高点价格到该K线实体上边界
                zone_high = swing_price  # swing高点价格作为上边界
                zone_low = max(candle_open, candle_close)  # K线实体上边界作为下边界

                # 确保区域有效（swing点应该是最高价，实体上边界应该低于swing点）
                if zone_low >= zone_high:
                    # 如果实体上边界等于或高于swing点，使用较小的区域
                    zone_low = min(candle_open, candle_close)  # 使用实体下边界

            else:  # support
                # 支撑区域：从swing低点价格到该K线实体下边界
                zone_low = swing_price  # swing低点价格作为下边界
                zone_high = min(candle_open, candle_close)  # K线实体下边界作为上边界

                # 确保区域有效（swing点应该是最低价，实体下边界应该高于swing点）
                if zone_high <= zone_low:
                    # 如果实体下边界等于或低于swing点，使用较大的区域
                    zone_high = max(candle_open, candle_close)  # 使用实体上边界

            # 计算区域的起始和结束时间
            zone_start_time = df.index[swing_index]  # 从swing点开始
            zone_end_time = df.index[-1]  # 延伸到数据结束

            zone_info = {
                'swing_point': swing,
                'zone_type': zone_type,
                'zone_high': zone_high,
                'zone_low': zone_low,
                'zone_start_time': zone_start_time,
                'zone_end_time': zone_end_time,
                'zone_start_index': swing_index,
                'zone_end_index': len(df) - 1,
                'zone_height': abs(zone_high - zone_low),
                'zone_center': (zone_high + zone_low) / 2,
                'candle_open': candle_open,
                'candle_close': candle_close,
                'candle_body_top': max(candle_open, candle_close),
                'candle_body_bottom': min(candle_open, candle_close)
            }

            logger.debug(f"{zone_type}区域: swing价格 {swing_price:.2f}, 实体范围 {min(candle_open, candle_close):.2f}-{max(candle_open, candle_close):.2f}, 区域范围 {zone_low:.2f}-{zone_high:.2f}")
            return zone_info

        except Exception as e:
            logger.warning(f"创建{zone_type}区域时出错: {e}")
            return None

    def _check_volume_qualification(self, df: pd.DataFrame, swing_index: int, volume_multiplier: float = 1.5) -> bool:
        """
        检查swing点的成交量是否满足条件（成交量 >= 20日均线 * 倍数）

        Args:
            df (pd.DataFrame): 股票数据
            swing_index (int): swing点的索引
            volume_multiplier (float): 成交量倍数，默认1.5倍

        Returns:
            bool: True表示成交量满足条件，False表示不满足
        """
        try:
            # 计算成交量20日均线
            volume_ma = self._calculate_volume_ma(df, period=20)

            # 获取当前swing点的成交量和20日均线
            current_volume = df.iloc[swing_index]['Volume']
            volume_ma_value = volume_ma.iloc[swing_index]

            # 如果20日均线数据不足，返回False
            if pd.isna(volume_ma_value) or volume_ma_value <= 0:
                logger.debug(f"索引 {swing_index}: 成交量20日均线数据不足，跳过")
                return False

            # 检查成交量是否达到20日均线的指定倍数
            volume_ratio = current_volume / volume_ma_value
            is_qualified = volume_ratio >= volume_multiplier

            if is_qualified:
                logger.debug(f"索引 {swing_index}: 成交量合格 - 当前: {current_volume:,.0f}, 20日均线: {volume_ma_value:,.0f}, 倍数: {volume_ratio:.2f}")
            else:
                logger.debug(f"索引 {swing_index}: 成交量不合格 - 当前: {current_volume:,.0f}, 20日均线: {volume_ma_value:,.0f}, 倍数: {volume_ratio:.2f}")

            return is_qualified

        except Exception as e:
            logger.warning(f"检查成交量资格时出错: {e}")
            return False

    def _identify_fvg_zones(self, df: pd.DataFrame, volume_multiplier: float = 2.0) -> Tuple[List[Dict], List[Dict]]:
        """
        识别FVG（Fair Value Gap）区域，并过滤被价格穿过70%的区域

        Args:
            df (pd.DataFrame): 股票数据
            volume_multiplier (float): 成交量倍数要求，默认2.0倍

        Returns:
            Tuple[List[Dict], List[Dict]]: (看涨FVG列表, 看跌FVG列表)
        """
        bullish_fvgs = []
        bearish_fvgs = []

        # 计算成交量20日均线
        volume_ma = self._calculate_volume_ma(df, period=20)

        # 需要至少3根K线来识别FVG
        if len(df) < 3:
            logger.warning("数据不足，无法识别FVG区域")
            return bullish_fvgs, bearish_fvgs

        # 遍历K线，寻找FVG模式
        for i in range(1, len(df) - 1):  # 中间K线的索引
            try:
                # 获取三根连续K线
                prev_candle = df.iloc[i - 1]  # 前一根K线
                curr_candle = df.iloc[i]      # 当前K线（中间）
                next_candle = df.iloc[i + 1]  # 后一根K线

                # 检查中间K线的成交量条件
                curr_volume = curr_candle['Volume']
                curr_volume_ma = volume_ma.iloc[i]

                # 如果成交量均线数据不足，跳过
                if pd.isna(curr_volume_ma) or curr_volume_ma <= 0:
                    continue

                # 检查成交量是否满足条件
                if curr_volume < curr_volume_ma * volume_multiplier:
                    continue

                # 正确的FVG识别逻辑：三根K线中，中间K线与前后两根K线高低点不重疊的部分

                # 获取三根K线的价格范围
                prev_high = prev_candle['High']
                prev_low = prev_candle['Low']
                curr_high = curr_candle['High']
                curr_low = curr_candle['Low']
                next_high = next_candle['High']
                next_low = next_candle['Low']

                # 识别看涨FVG（Bullish FVG）
                # 条件：前K线最高价 < 后K线最低价（存在价格缺口）
                if prev_high < next_low:
                    # 计算中间K线与前后K线不重疊的部分
                    # 中间K线的范围：curr_low 到 curr_high
                    # 前K线覆盖范围：prev_low 到 prev_high
                    # 后K线覆盖范围：next_low 到 next_high

                    # 中间K线中没有被前后K线覆盖的部分：
                    # 上边界：中间K线最高价（但不能超过缺口范围）
                    # 下边界：中间K线最低价（但不能低于缺口范围）
                    fvg_high = min(curr_high, next_low)  # 不能超过后K线最低价
                    fvg_low = max(curr_low, prev_high)   # 不能低于前K线最高价

                    # 只有当FVG区域存在时才记录（fvg_high > fvg_low）
                    if fvg_high > fvg_low:
                        fvg_info = {
                            'type': 'bullish',
                            'index': i,
                            'time': df.index[i],  # 中间K线时间
                            'prev_time': df.index[i-1],  # 前K线时间
                            'prev_high': prev_high,  # 前K线最高价
                            'prev_low': prev_low,    # 前K线最低价
                            'gap_high': fvg_high,  # 中间K线未被覆盖部分的上边界
                            'gap_low': fvg_low,    # 中间K线未被覆盖部分的下边界
                            'gap_size': fvg_high - fvg_low,
                            'curr_candle': {
                                'volume': curr_volume,
                                'volume_ma': curr_volume_ma,
                                'volume_ratio': curr_volume / curr_volume_ma
                            },
                            # 添加调试信息
                            'debug_info': {
                                'prev_range': f"{prev_low:.2f}-{prev_high:.2f}",
                                'curr_range': f"{curr_low:.2f}-{curr_high:.2f}",
                                'next_range': f"{next_low:.2f}-{next_high:.2f}",
                                'gap_range': f"{prev_high:.2f}-{next_low:.2f}",
                                'fvg_range': f"{fvg_low:.2f}-{fvg_high:.2f}"
                            }
                        }

                        # 检查是否被穿透70%
                        if self._check_fvg_validity(df, fvg_info, 'bullish'):
                            bullish_fvgs.append(fvg_info)

                # 识别看跌FVG（Bearish FVG）
                # 条件：前K线最低价 > 后K线最高价（存在价格缺口）
                elif prev_low > next_high:
                    # 计算中间K线与前后K线不重疊的部分
                    # 中间K线的范围：curr_low 到 curr_high
                    # 前K线覆盖范围：prev_low 到 prev_high
                    # 后K线覆盖范围：next_low 到 next_high

                    # 中间K线中没有被前后K线覆盖的部分：
                    # 上边界：中间K线最高价（但不能超过缺口范围）
                    # 下边界：中间K线最低价（但不能低于缺口范围）
                    fvg_high = min(curr_high, prev_low)  # 不能超过前K线最低价
                    fvg_low = max(curr_low, next_high)   # 不能低于后K线最高价

                    # 只有当FVG区域存在时才记录（fvg_high > fvg_low）
                    if fvg_high > fvg_low:
                        fvg_info = {
                            'type': 'bearish',
                            'index': i,
                            'time': df.index[i],  # 中间K线时间
                            'prev_time': df.index[i-1],  # 前K线时间
                            'prev_high': prev_high,  # 前K线最高价
                            'prev_low': prev_low,    # 前K线最低价
                            'gap_high': fvg_high,  # 中间K线未被覆盖部分的上边界
                            'gap_low': fvg_low,    # 中间K线未被覆盖部分的下边界
                            'gap_size': fvg_high - fvg_low,
                            'curr_candle': {
                                'volume': curr_volume,
                                'volume_ma': curr_volume_ma,
                                'volume_ratio': curr_volume / curr_volume_ma
                            },
                            # 添加调试信息
                            'debug_info': {
                                'prev_range': f"{prev_low:.2f}-{prev_high:.2f}",
                                'curr_range': f"{curr_low:.2f}-{curr_high:.2f}",
                                'next_range': f"{next_low:.2f}-{next_high:.2f}",
                                'gap_range': f"{next_high:.2f}-{prev_low:.2f}",
                                'fvg_range': f"{fvg_low:.2f}-{fvg_high:.2f}"
                            }
                        }

                        # 检查是否被穿透70%
                        if self._check_fvg_validity(df, fvg_info, 'bearish'):
                            bearish_fvgs.append(fvg_info)

            except Exception as e:
                logger.warning(f"处理FVG识别时出错，索引{i}: {e}")
                continue

        logger.info(f"识别到 {len(bullish_fvgs)} 个有效看涨FVG区域，{len(bearish_fvgs)} 个有效看跌FVG区域")
        return bullish_fvgs, bearish_fvgs

    def _check_fvg_validity(self, df: pd.DataFrame, fvg_info: Dict, fvg_type: str, penetration_threshold: float = 0.7) -> bool:
        """
        检查FVG区域是否被价格穿过70%

        Args:
            df (pd.DataFrame): 股票数据
            fvg_info (Dict): FVG信息
            fvg_type (str): FVG类型 ('bullish' 或 'bearish')
            penetration_threshold (float): 穿透阈值，默认0.7（70%）

        Returns:
            bool: True表示FVG有效，False表示被穿透
        """
        try:
            fvg_index = fvg_info['index']
            gap_high = fvg_info['gap_high']
            gap_low = fvg_info['gap_low']
            gap_size = gap_high - gap_low

            # 计算70%穿透的价格水平
            if fvg_type == 'bullish':
                penetration_level = gap_high - (gap_size * penetration_threshold)
            else:  # bearish
                penetration_level = gap_low + (gap_size * penetration_threshold)

            # 检查FVG之后的所有K线
            for i in range(fvg_index + 1, len(df)):
                candle = df.iloc[i]

                if fvg_type == 'bullish':
                    if candle['Low'] < penetration_level:
                        return False  # 被穿透
                else:  # bearish
                    if candle['High'] > penetration_level:
                        return False  # 被穿透

            # 添加穿透水平信息
            fvg_info['penetration_level'] = penetration_level
            return True

        except Exception as e:
            logger.warning(f"检查FVG有效性时出错: {e}")
            return False

    def _convert_to_china_time(self, dt: datetime) -> datetime:
        """
        将时间转换为中国时间（北京时间）

        Args:
            dt (datetime): 输入时间

        Returns:
            datetime: 转换后的北京时间
        """
        try:
            # 如果时间没有时区信息，假设为UTC
            if dt.tzinfo is None:
                dt = pytz.UTC.localize(dt)

            # 转换为北京时间
            china_tz = pytz.timezone('Asia/Shanghai')
            china_time = dt.astimezone(china_tz)

            return china_time.replace(tzinfo=None)  # 移除时区信息，只保留本地时间
        except Exception as e:
            logger.warning(f"时间转换失败: {e}, 使用原始时间")
            return dt

    def _is_trading_time(self, dt: datetime) -> bool:
        """
        检查是否为A股交易时间

        Args:
            dt (datetime): 时间

        Returns:
            bool: 是否为交易时间
        """
        # A股交易时间：9:30-11:30, 13:00-15:00
        time_only = dt.time()

        # 上午交易时间：9:30-11:30
        morning_start = datetime.strptime("09:30:00", "%H:%M:%S").time()
        morning_end = datetime.strptime("11:30:00", "%H:%M:%S").time()

        # 下午交易时间：13:00-15:00
        afternoon_start = datetime.strptime("13:00:00", "%H:%M:%S").time()
        afternoon_end = datetime.strptime("15:00:00", "%H:%M:%S").time()

        # 检查是否在交易时间内
        is_morning = morning_start <= time_only <= morning_end
        is_afternoon = afternoon_start <= time_only <= afternoon_end

        return is_morning or is_afternoon

    def _format_trading_time(self, dt: datetime) -> str:
        """
        格式化交易时间，如果不是交易时间则添加警告标记

        Args:
            dt (datetime): 时间

        Returns:
            str: 格式化的时间字符串
        """
        # 转换为北京时间
        china_time = self._convert_to_china_time(dt)

        # 格式化时间字符串
        time_str = china_time.strftime('%Y-%m-%d %H:%M:%S')

        # 检查是否为交易时间
        if not self._is_trading_time(china_time):
            time_str += " ⚠️"  # 添加警告标记

        return time_str

    def _calculate_volume_ma(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算成交量移动平均线，从第period+1根K线开始绘制

        Args:
            df (pd.DataFrame): 股票数据
            period (int): 移动平均周期，默认20

        Returns:
            pd.Series: 成交量移动平均线数据，前period个值为NaN
        """
        if len(df) < period:
            logger.warning(f"数据长度不足，无法计算{period}日成交量均线")
            return pd.Series(index=df.index, dtype=float)

        # 计算成交量移动平均
        volume_ma = df['Volume'].rolling(window=period, min_periods=period).mean()

        # 前period-1个值设为NaN，从第period个值开始有效
        volume_ma.iloc[:period-1] = np.nan

        logger.info(f"成交量{period}日均线计算完成，从第{period}根K线开始绘制")
        return volume_ma

    def _print_swing_points_details(self, df: pd.DataFrame, swing_highs: List[Dict], swing_lows: List[Dict]):
        """
        打印有效swing点的详细信息，包括日期、价格、成交量和成交量20日均线值

        Args:
            df (pd.DataFrame): 股票数据
            swing_highs (List[Dict]): swing high点列表
            swing_lows (List[Dict]): swing low点列表
        """
        # 计算成交量20日均线
        volume_ma = self._calculate_volume_ma(df, period=20)

        print("\n" + "="*140)
        print("📊 最终有效Swing点详细信息")
        print("📝 时间已转换为北京时间，⚠️ 标记表示非A股交易时间")
        print("📈 A股交易时间：9:30-11:30, 13:00-15:00")
        print("📊 过滤条件：成交量 >= 20日均线 * 1.5 且未被后续价格穿过")
        print("🔲 区域信息：基于swing点价格和K线实体边界创建矩形区域")
        print("="*140)

        if swing_highs:
            print(f"\n🔴 阻力位 (Swing High点) - 共{len(swing_highs)}个:")
            print("-" * 140)
            print(f"{'序号':<4} {'日期时间':<25} {'价格':<12} {'成交量':<15} {'成交量20日均线':<15} {'倍数':<8} {'实体范围':<15} {'区域范围':<15}")
            print("-" * 140)

            for i, swing in enumerate(swing_highs, 1):
                swing_index = swing['index']
                date_str = self._format_trading_time(swing['time'])
                price = swing['price']
                volume = df.iloc[swing_index]['Volume']
                volume_ma_value = volume_ma.iloc[swing_index]

                # 格式化成交量和均线值
                volume_str = f"{volume:,.0f}" if not pd.isna(volume) else "N/A"
                volume_ma_str = f"{volume_ma_value:,.0f}" if not pd.isna(volume_ma_value) else "N/A"

                # 计算成交量倍数
                if not pd.isna(volume_ma_value) and volume_ma_value > 0:
                    volume_ratio = volume / volume_ma_value
                    ratio_str = f"{volume_ratio:.2f}x"
                else:
                    ratio_str = "N/A"

                # 获取区域信息和实体信息
                if 'zone' in swing and swing['zone']:
                    zone = swing['zone']
                    zone_str = f"{zone['zone_low']:.2f}-{zone['zone_high']:.2f}"
                    body_str = f"{zone['candle_body_bottom']:.2f}-{zone['candle_body_top']:.2f}"
                else:
                    zone_str = "N/A"
                    body_str = "N/A"

                print(f"{i:<4} {date_str:<25} {price:<12.2f} {volume_str:<15} {volume_ma_str:<15} {ratio_str:<8} {body_str:<15} {zone_str:<15}")

        if swing_lows:
            print(f"\n🟢 支撑位 (Swing Low点) - 共{len(swing_lows)}个:")
            print("-" * 140)
            print(f"{'序号':<4} {'日期时间':<25} {'价格':<12} {'成交量':<15} {'成交量20日均线':<15} {'倍数':<8} {'实体范围':<15} {'区域范围':<15}")
            print("-" * 140)

            for i, swing in enumerate(swing_lows, 1):
                swing_index = swing['index']
                date_str = self._format_trading_time(swing['time'])
                price = swing['price']
                volume = df.iloc[swing_index]['Volume']
                volume_ma_value = volume_ma.iloc[swing_index]

                # 格式化成交量和均线值
                volume_str = f"{volume:,.0f}" if not pd.isna(volume) else "N/A"
                volume_ma_str = f"{volume_ma_value:,.0f}" if not pd.isna(volume_ma_value) else "N/A"

                # 计算成交量倍数
                if not pd.isna(volume_ma_value) and volume_ma_value > 0:
                    volume_ratio = volume / volume_ma_value
                    ratio_str = f"{volume_ratio:.2f}x"
                else:
                    ratio_str = "N/A"

                # 获取区域信息和实体信息
                if 'zone' in swing and swing['zone']:
                    zone = swing['zone']
                    zone_str = f"{zone['zone_low']:.2f}-{zone['zone_high']:.2f}"
                    body_str = f"{zone['candle_body_bottom']:.2f}-{zone['candle_body_top']:.2f}"
                else:
                    zone_str = "N/A"
                    body_str = "N/A"

                print(f"{i:<4} {date_str:<25} {price:<12.2f} {volume_str:<15} {volume_ma_str:<15} {ratio_str:<8} {body_str:<15} {zone_str:<15}")

        if not swing_highs and not swing_lows:
            print("\n❌ 未检测到有效的swing点")

        print("="*140)

    def _print_fvg_details(self, df: pd.DataFrame, bullish_fvgs: List[Dict], bearish_fvgs: List[Dict]):
        """
        打印FVG区域的详细信息
        """
        if not bullish_fvgs and not bearish_fvgs:
            return

        print("\n" + "="*200)
        print("📊 FVG（Fair Value Gap）区域详细信息 - 正确定义版本")
        print("📝 FVG正确定义：观察三根K线，中间K线与前后两根K线高低点不重疊的部分就是FVG")
        print("🟢 看涨FVG：前K线最高价 < 后K线最低价时，中间K线未被前后K线覆盖的部分（绿色支撑线）")
        print("🔴 看跌FVG：前K线最低价 > 后K线最高价时，中间K线未被前后K线覆盖的部分（红色阻力线）")
        print("🚫 过滤条件：中间K线成交量 >= 20日均线 * 2.0 且被后续价格穿过70%的FVG区域将被删除")
        print("📋 不要求三根K线同色：可以是混合阴阳线组合")
        print("💡 关键理解：FVG不是简单的价格缺口，而是中间K线中真正没有被前后K线交易过的价格区域")
        print("="*200)

        if bullish_fvgs:
            print(f"\n🟣 看涨FVG区域 - 共{len(bullish_fvgs)}个:")
            print("-" * 200)
            print(f"{'序号':<4} {'中间K线时间':<25} {'实际FVG范围':<20} {'大小':<8} {'前K线范围':<15} {'中K线范围':<15} {'后K线范围':<15} {'成交量':<12} {'倍数':<8} {'70%穿透':<10}")
            print("-" * 200)

            for i, fvg in enumerate(bullish_fvgs, 1):
                date_str = self._format_trading_time(fvg['time'])
                gap_range = f"{fvg['gap_low']:.2f}-{fvg['gap_high']:.2f}"
                gap_size = fvg['gap_size']
                volume = fvg['curr_candle']['volume']
                volume_ma = fvg['curr_candle']['volume_ma']
                volume_ratio = fvg['curr_candle']['volume_ratio']
                penetration_level = fvg.get('penetration_level', 0)

                # 获取调试信息
                debug_info = fvg.get('debug_info', {})
                prev_range = debug_info.get('prev_range', 'N/A')
                curr_range = debug_info.get('curr_range', 'N/A')
                next_range = debug_info.get('next_range', 'N/A')

                volume_str = f"{volume:,.0f}"
                ratio_str = f"{volume_ratio:.2f}x"
                penetration_str = f"{penetration_level:.2f}"

                print(f"{i:<4} {date_str:<25} {gap_range:<20} {gap_size:<8.2f} {prev_range:<15} {curr_range:<15} {next_range:<15} {volume_str:<12} {ratio_str:<8} {penetration_str:<10}")

                # 显示详细的FVG计算过程
                if debug_info:
                    gap_range = debug_info.get('gap_range', 'N/A')
                    fvg_range = debug_info.get('fvg_range', 'N/A')
                    print(f"     └─ 价格缺口: {gap_range} → 中间K线未覆盖部分: {fvg_range}")

        if bearish_fvgs:
            print(f"\n⚫ 看跌FVG区域 - 共{len(bearish_fvgs)}个:")
            print("-" * 200)
            print(f"{'序号':<4} {'中间K线时间':<25} {'实际FVG范围':<20} {'大小':<8} {'前K线范围':<15} {'中K线范围':<15} {'后K线范围':<15} {'成交量':<12} {'倍数':<8} {'70%穿透':<10}")
            print("-" * 200)

            for i, fvg in enumerate(bearish_fvgs, 1):
                date_str = self._format_trading_time(fvg['time'])
                gap_range = f"{fvg['gap_low']:.2f}-{fvg['gap_high']:.2f}"
                gap_size = fvg['gap_size']
                volume = fvg['curr_candle']['volume']
                volume_ma = fvg['curr_candle']['volume_ma']
                volume_ratio = fvg['curr_candle']['volume_ratio']
                penetration_level = fvg.get('penetration_level', 0)

                # 获取调试信息
                debug_info = fvg.get('debug_info', {})
                prev_range = debug_info.get('prev_range', 'N/A')
                curr_range = debug_info.get('curr_range', 'N/A')
                next_range = debug_info.get('next_range', 'N/A')

                volume_str = f"{volume:,.0f}"
                ratio_str = f"{volume_ratio:.2f}x"
                penetration_str = f"{penetration_level:.2f}"

                print(f"{i:<4} {date_str:<25} {gap_range:<20} {gap_size:<8.2f} {prev_range:<15} {curr_range:<15} {next_range:<15} {volume_str:<12} {ratio_str:<8} {penetration_str:<10}")

                # 显示详细的FVG计算过程
                if debug_info:
                    gap_range = debug_info.get('gap_range', 'N/A')
                    fvg_range = debug_info.get('fvg_range', 'N/A')
                    print(f"     └─ 价格缺口: {gap_range} → 中间K线未覆盖部分: {fvg_range}")

        print("="*200)

    def _add_fvg_lines(self, df: pd.DataFrame, addplot_data: List):
        """
        添加FVG区域的水平线到绘图数据中（全部实线）

        Args:
            df (pd.DataFrame): 股票数据
            addplot_data (List): 绘图数据列表
        """
        try:
            # 重新识别FVG区域
            bullish_fvgs, bearish_fvgs = self._identify_fvg_zones(df)

            # 绘制看涨FVG区域（紫色实线）
            for fvg in bullish_fvgs:
                fvg_index = fvg['index']
                gap_high = fvg['gap_high']  # 缺口上边界
                gap_low = fvg['gap_low']    # 缺口下边界

                # 绘制缺口上边界线（紫色实线）
                gap_high_line = pd.Series(index=df.index, dtype=float)
                for i in range(fvg_index, len(df)):
                    gap_high_line.iloc[i] = gap_high

                addplot_data.append(mpf.make_addplot(
                    gap_high_line,
                    type='line',
                    color='purple',
                    linestyle='-',
                    width=2,
                    alpha=0.7,
                    secondary_y=False
                ))

                # 绘制缺口下边界线（紫色实线）
                gap_low_line = pd.Series(index=df.index, dtype=float)
                for i in range(fvg_index, len(df)):
                    gap_low_line.iloc[i] = gap_low

                addplot_data.append(mpf.make_addplot(
                    gap_low_line,
                    type='line',
                    color='purple',
                    linestyle='-',
                    width=1,
                    alpha=0.5,
                    secondary_y=False
                ))

            # 绘制看跌FVG区域（黑色实线）
            for fvg in bearish_fvgs:
                fvg_index = fvg['index']
                gap_high = fvg['gap_high']  # 缺口上边界
                gap_low = fvg['gap_low']    # 缺口下边界

                # 绘制缺口上边界线（黑色实线）
                gap_high_line = pd.Series(index=df.index, dtype=float)
                for i in range(fvg_index, len(df)):
                    gap_high_line.iloc[i] = gap_high

                addplot_data.append(mpf.make_addplot(
                    gap_high_line,
                    type='line',
                    color='black',
                    linestyle='-',
                    width=2,
                    alpha=0.7,
                    secondary_y=False
                ))

                # 绘制缺口下边界线（黑色实线）
                gap_low_line = pd.Series(index=df.index, dtype=float)
                for i in range(fvg_index, len(df)):
                    gap_low_line.iloc[i] = gap_low

                addplot_data.append(mpf.make_addplot(
                    gap_low_line,
                    type='line',
                    color='black',
                    linestyle='-',
                    width=1,
                    alpha=0.5,
                    secondary_y=False
                ))

            if bullish_fvgs or bearish_fvgs:
                logger.info(f"FVG区域线已添加到图表: {len(bullish_fvgs)}个看涨FVG(紫色), {len(bearish_fvgs)}个看跌FVG(黑色)（全部实线）")

        except Exception as e:
            logger.warning(f"添加FVG区域线时出错: {e}")

    def plot_swing_points(self, df: pd.DataFrame, swing_highs: List[Dict], swing_lows: List[Dict]) -> Dict:
        """
        为swing点创建绘图数据

        Args:
            df (pd.DataFrame): 股票数据
            swing_highs (list): swing high点列表
            swing_lows (list): swing low点列表

        Returns:
            dict: 包含绘图数据的字典
        """
        plot_data = {
            'swing_points': {
                'swing_highs': swing_highs,
                'swing_lows': swing_lows,
                'total_swing_highs': len(swing_highs),
                'total_swing_lows': len(swing_lows)
            },
            'data_info': {
                'total_candles': len(df),
                'time_range': {
                    'start': df.index[0],
                    'end': df.index[-1]
                },
                'price_range': {
                    'high': df['High'].max(),
                    'low': df['Low'].min()
                }
            },
            'analysis_params': {
                'swing_length': self.swing_length
            }
        }

        logger.info(f"绘图数据准备完成: {len(swing_highs)}个swing high点, {len(swing_lows)}个swing low点")
        return plot_data

    def analyze_swing_points(self, df: pd.DataFrame) -> Dict:
        """
        完整的swing点分析API

        Args:
            df (pd.DataFrame): 股票数据，必须包含Open、High、Low、Close、Volume列，索引为时间

        Returns:
            dict: 包含所有swing点信息的字典
        """
        logger.info("开始swing点分析")

        # 验证数据格式
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必需的列: {missing_columns}")

        if df.empty:
            raise ValueError("数据为空")

        # 检测swing点
        swing_highs, swing_lows = self.find_swing_points(df)

        # 创建绘图数据
        result = self.plot_swing_points(df, swing_highs, swing_lows)

        logger.info(f"分析完成: {len(swing_highs)}个swing high点, {len(swing_lows)}个swing low点")
        return result

    def plot_swing_chart(self, df: pd.DataFrame, stock_code: str = "股票",
                        save_path: Optional[str] = None, show_plot: bool = True,
                        interactive: bool = True, show_volume_ma: bool = True) -> None:
        """
        绘制带有swing点标记和水平线的K线图，支持交互式放大缩小功能

        Args:
            df (pd.DataFrame): 股票数据，必须包含Open、High、Low、Close、Volume列，索引为时间
            stock_code (str): 股票代码，用于图表标题
            save_path (str, optional): 保存路径，如果为None则自动保存到同级目录
            show_plot (bool): 是否显示图表，默认True
            interactive (bool): 是否启用交互式功能（放大缩小、拖拽等），默认True
            show_volume_ma (bool): 是否显示成交量20日均线，默认True
        """
        if not PLOTTING_AVAILABLE:
            logger.error("绘图库未安装，无法绘制图表。请安装: pip install matplotlib mplfinance")
            return

        if df.empty:
            logger.error("数据为空，无法绘制K线图")
            return

        # 检测swing点
        swing_highs, swing_lows = self.find_swing_points(df)

        # 设置中文字体
        chinese_font_found = False
        try:
            # 尝试设置中文字体
            font_names = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            for font_name in font_names:
                try:
                    plt.rcParams['font.sans-serif'] = [font_name]
                    chinese_font_found = True
                    break
                except:
                    continue
        except:
            pass

        plt.rcParams['axes.unicode_minus'] = False

        # 设置K线图样式
        mc = mpf.make_marketcolors(
            up='red',      # 上涨为红色
            down='green',  # 下跌为绿色
            edge='inherit',
            wick={'up': 'red', 'down': 'green'},
            volume='in'
        )

        style = mpf.make_mpf_style(
            marketcolors=mc,
            gridstyle='-',
            y_on_right=True
        )

        # 创建图表标题
        if chinese_font_found:
            title = f"{stock_code} K线图 - Swing点分析 (swing_length={self.swing_length})"
            ylabel = '价格'
            ylabel_lower = '成交量'
        else:
            title = f"{stock_code} Candlestick Chart - Swing Points Analysis (swing_length={self.swing_length})"
            ylabel = 'Price'
            ylabel_lower = 'Volume'

        # 准备附加绘图数据
        addplot_data = []

        # 添加swing high点标记（红色三角向下）
        if swing_highs:
            swing_high_markers = pd.Series(index=df.index, dtype=float)
            for swing in swing_highs:
                swing_high_markers.iloc[swing['index']] = swing['price']

            addplot_data.append(mpf.make_addplot(
                swing_high_markers,
                type='scatter',
                markersize=100,
                marker='v',  # 向下三角
                color='red',
                alpha=0.8,
                secondary_y=False
            ))

            # 为每个swing high点添加阻力区域（矩形）
            for swing in swing_highs:
                if 'zone' in swing and swing['zone']:
                    zone = swing['zone']
                    swing_index = swing['index']

                    # 绘制区域上边界线（zone_high）
                    zone_high_line = pd.Series(index=df.index, dtype=float)
                    for i in range(swing_index, len(df)):
                        zone_high_line.iloc[i] = zone['zone_high']

                    addplot_data.append(mpf.make_addplot(
                        zone_high_line,
                        type='line',
                        color='red',
                        linestyle='-',
                        width=1,
                        alpha=0.8,
                        secondary_y=False
                    ))

                    # 绘制区域下边界线（zone_low）
                    zone_low_line = pd.Series(index=df.index, dtype=float)
                    for i in range(swing_index, len(df)):
                        zone_low_line.iloc[i] = zone['zone_low']

                    addplot_data.append(mpf.make_addplot(
                        zone_low_line,
                        type='line',
                        color='red',
                        linestyle='-',
                        width=1,
                        alpha=0.6,
                        secondary_y=False
                    ))
                else:
                    # 如果没有区域信息，绘制传统的单线
                    resistance_line = pd.Series(index=df.index, dtype=float)
                    swing_index = swing['index']
                    swing_price = swing['price']

                    for i in range(swing_index, len(df)):
                        resistance_line.iloc[i] = swing_price

                    addplot_data.append(mpf.make_addplot(
                        resistance_line,
                        type='line',
                        color='red',
                        linestyle='-',
                        width=1,
                        alpha=0.6,
                        secondary_y=False
                    ))

        # 添加swing low点标记（绿色三角向上）
        if swing_lows:
            swing_low_markers = pd.Series(index=df.index, dtype=float)
            for swing in swing_lows:
                swing_low_markers.iloc[swing['index']] = swing['price']

            addplot_data.append(mpf.make_addplot(
                swing_low_markers,
                type='scatter',
                markersize=100,
                marker='^',  # 向上三角
                color='green',
                alpha=0.8,
                secondary_y=False
            ))

            # 为每个swing low点添加支撑区域（矩形）
            for swing in swing_lows:
                if 'zone' in swing and swing['zone']:
                    zone = swing['zone']
                    swing_index = swing['index']

                    # 绘制区域上边界线（zone_high）
                    zone_high_line = pd.Series(index=df.index, dtype=float)
                    for i in range(swing_index, len(df)):
                        zone_high_line.iloc[i] = zone['zone_high']

                    addplot_data.append(mpf.make_addplot(
                        zone_high_line,
                        type='line',
                        color='green',
                        linestyle='-',
                        width=1,
                        alpha=0.6,
                        secondary_y=False
                    ))

                    # 绘制区域下边界线（zone_low）
                    zone_low_line = pd.Series(index=df.index, dtype=float)
                    for i in range(swing_index, len(df)):
                        zone_low_line.iloc[i] = zone['zone_low']

                    addplot_data.append(mpf.make_addplot(
                        zone_low_line,
                        type='line',
                        color='green',
                        linestyle='-',
                        width=1,
                        alpha=0.8,
                        secondary_y=False
                    ))
                else:
                    # 如果没有区域信息，绘制传统的单线
                    support_line = pd.Series(index=df.index, dtype=float)
                    swing_index = swing['index']
                    swing_price = swing['price']

                    for i in range(swing_index, len(df)):
                        support_line.iloc[i] = swing_price

                    addplot_data.append(mpf.make_addplot(
                        support_line,
                        type='line',
                        color='green',
                        linestyle='-',
                        width=1,
                        alpha=0.6,
                        secondary_y=False
                    ))

        # 添加成交量20日均线
        if show_volume_ma:
            volume_ma = self._calculate_volume_ma(df, period=20)
            if not volume_ma.isna().all():  # 确保有有效数据
                addplot_data.append(mpf.make_addplot(
                    volume_ma,
                    type='line',
                    color='orange',
                    linestyle='-',
                    width=2,
                    alpha=0.8,
                    panel=1,  # 显示在成交量面板
                    secondary_y=False
                ))
                logger.info("成交量20日均线已添加到图表")

        # 添加FVG区域线（全部实线）
        self._add_fvg_lines(df, addplot_data)

        # 绘制K线图
        plot_kwargs = {
            'type': 'candle',
            'style': style,
            'title': title,
            'ylabel': ylabel,
            'ylabel_lower': ylabel_lower,
            'volume': True,
            'figsize': (16, 10),
            'show_nontrading': False,
            'warn_too_much_data': len(df) + 100  # 避免数据量警告
        }

        # 添加swing点标记
        if addplot_data:
            plot_kwargs['addplot'] = addplot_data

        # 绘制图表
        if interactive and show_plot:
            # 交互式模式：只显示图表窗口，不保存文件
            # 移除savefig参数以避免自动保存
            plot_kwargs_interactive = plot_kwargs.copy()
            if 'savefig' in plot_kwargs_interactive:
                del plot_kwargs_interactive['savefig']

            # 使用交互式绘图
            fig, axes = mpf.plot(df, returnfig=True, **plot_kwargs_interactive)

            # 添加交互式功能
            self._add_interactive_features(fig, axes, df, stock_code, swing_highs, swing_lows)

            # 显示交互式图表窗口
            plt.show()

            # 显示统计信息
            logger.info(f"交互式图表已显示: {len(swing_highs)}个swing high点, {len(swing_lows)}个swing low点")

        else:
            # 非交互式模式：保存图片
            # 自动生成保存路径（如果未指定）
            if save_path is None:
                import os
                import datetime
                # 获取当前脚本所在目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
                # 生成文件名：股票代码_swing分析_时间戳.png
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{stock_code}_swing_analysis_{timestamp}.png"
                save_path = os.path.join(current_dir, filename)

            # 添加savefig参数
            plot_kwargs['savefig'] = save_path

            # 非交互式绘图（用于保存）
            mpf.plot(df, **plot_kwargs)

            # 显示统计信息
            logger.info(f"图表绘制完成: {len(swing_highs)}个swing high点, {len(swing_lows)}个swing low点")
            logger.info(f"图表已保存到: {save_path}")

            if show_plot:
                plt.show()

    def _add_interactive_features(self, fig, axes, df, stock_code, swing_highs, swing_lows):
        """
        为图表添加高级交互式功能，包括框选缩放、拖拽平移等

        Args:
            fig: matplotlib图形对象
            axes: matplotlib轴对象列表
            df: K线数据
            stock_code: 股票代码
            swing_highs: swing high点列表
            swing_lows: swing low点列表
        """
        try:
            # 确保使用交互式后端
            import matplotlib
            if matplotlib.get_backend() == 'Agg':
                matplotlib.use('TkAgg')  # 使用TkAgg后端支持交互

            plt.ion()  # 启用交互模式

            # 获取主要的价格轴和成交量轴
            if isinstance(axes, list):
                main_ax = axes[0]  # 价格轴
                volume_ax = axes[1] if len(axes) > 1 else None  # 成交量轴
            else:
                main_ax = axes
                volume_ax = None

            # 存储原始视图范围
            original_xlim = main_ax.get_xlim()
            original_ylim = main_ax.get_ylim()
            original_volume_ylim = volume_ax.get_ylim() if volume_ax else None

            # 交互状态变量
            self._interaction_state = {
                'is_panning': False,
                'pan_start': None,
                'last_xlim': None,
                'last_ylim': None
            }

            # 确保导航工具栏可用
            if hasattr(fig.canvas, 'toolbar') and fig.canvas.toolbar:
                fig.canvas.toolbar.update()
                logger.info("✅ matplotlib 原生导航工具栏已启用")
            else:
                logger.warning("⚠️ 导航工具栏不可用")

            # 鼠标按下事件 - 简化版，主要用于平移
            def on_mouse_press(event):
                if event.inaxes != main_ax:
                    return

                if event.button == 1 and not event.key:  # 普通左键：拖拽平移模式
                    self._interaction_state['is_panning'] = True
                    self._interaction_state['pan_start'] = (event.xdata, event.ydata)
                    self._interaction_state['last_xlim'] = main_ax.get_xlim()
                    self._interaction_state['last_ylim'] = main_ax.get_ylim()

            # 鼠标移动事件 - 简化版，只处理平移
            def on_mouse_move(event):
                if event.inaxes != main_ax:
                    return

                if self._interaction_state['is_panning'] and event.xdata and event.ydata:
                    # 拖拽平移
                    start_x, start_y = self._interaction_state['pan_start']
                    dx = event.xdata - start_x
                    dy = event.ydata - start_y

                    last_xlim = self._interaction_state['last_xlim']
                    last_ylim = self._interaction_state['last_ylim']

                    new_xlim = (last_xlim[0] - dx, last_xlim[1] - dx)
                    new_ylim = (last_ylim[0] - dy, last_ylim[1] - dy)

                    main_ax.set_xlim(new_xlim)
                    main_ax.set_ylim(new_ylim)

                    # 同步成交量轴的X轴
                    if volume_ax:
                        volume_ax.set_xlim(new_xlim)

                    fig.canvas.draw_idle()

            # 鼠标释放事件 - 简化版，只处理平移
            def on_mouse_release(event):
                if event.inaxes != main_ax:
                    return

                if self._interaction_state['is_panning'] and event.button == 1:
                    # 完成拖拽
                    self._interaction_state['is_panning'] = False
                    self._interaction_state['pan_start'] = None

            # 鼠标滚轮缩放功能
            def on_scroll(event):
                if event.inaxes not in [main_ax, volume_ax]:
                    return

                # 获取当前视图范围
                cur_xlim = main_ax.get_xlim()
                cur_ylim = main_ax.get_ylim()

                # 缩放因子
                scale_factor = 0.8 if event.button == 'up' else 1.25

                # 计算鼠标位置相对于轴的比例
                xdata = event.xdata
                ydata = event.ydata

                if xdata is not None:
                    # X轴缩放（以鼠标位置为中心）
                    x_left = xdata - (xdata - cur_xlim[0]) * scale_factor
                    x_right = xdata + (cur_xlim[1] - xdata) * scale_factor

                    main_ax.set_xlim([x_left, x_right])

                    # 同步成交量轴的X轴
                    if volume_ax:
                        volume_ax.set_xlim([x_left, x_right])

                # 如果鼠标在价格轴上，也缩放Y轴
                if event.inaxes == main_ax and ydata is not None:
                    y_bottom = ydata - (ydata - cur_ylim[0]) * scale_factor
                    y_top = ydata + (cur_ylim[1] - ydata) * scale_factor
                    main_ax.set_ylim([y_bottom, y_top])

                fig.canvas.draw_idle()

            # 绑定鼠标事件
            fig.canvas.mpl_connect('button_press_event', on_mouse_press)
            fig.canvas.mpl_connect('motion_notify_event', on_mouse_move)
            fig.canvas.mpl_connect('button_release_event', on_mouse_release)
            fig.canvas.mpl_connect('scroll_event', on_scroll)

            # 设置窗口标题
            if hasattr(fig.canvas, 'manager'):
                fig.canvas.manager.set_window_title(f"{stock_code} - 交互式Swing点分析 [可缩放]")

            # 添加详细的操作说明
            operation_text = "🎮 交互操作: 滚轮缩放 | 工具栏框选缩放 | 左键拖拽平移"
            data_text = f"📊 数据: {len(df)}根K线 | {len(swing_highs)}个阻力位 | {len(swing_lows)}个支撑位"

            if hasattr(fig, '_suptitle') and fig._suptitle:
                original_title = fig._suptitle.get_text()
                fig.suptitle(f"{original_title}\n{data_text}\n{operation_text}", fontsize=10)
            else:
                fig.suptitle(f"{stock_code} - 交互式Swing点分析\n{data_text}\n{operation_text}", fontsize=11)

            # 启用导航工具栏
            if hasattr(fig.canvas, 'toolbar'):
                fig.canvas.toolbar.update()

            # 确保窗口显示在前台
            if hasattr(fig.canvas, 'manager'):
                try:
                    fig.canvas.manager.window.wm_attributes('-topmost', 1)
                    fig.canvas.manager.window.wm_attributes('-topmost', 0)
                except:
                    pass  # 忽略不支持的窗口管理器

            logger.info("🎮 交互式图表已显示 - 使用matplotlib原生导航工具栏")
            print("\n" + "="*80)
            print("🎮 交互式K线图已打开，支持以下操作:")
            print("• 🔍 工具栏放大镜图标: 点击激活框选缩放模式")
            print("• 🏠 工具栏Home按钮: 恢复原始视图")
            print("• ← → 工具栏前进/后退按钮: 浏览缩放历史")
            print("• ✋ 工具栏平移按钮: 激活拖拽平移模式")
            print("• ⚙️ 工具栏配置按钮: 调整图表参数")
            print("• 💾 工具栏保存按钮: 保存图表为图片")
            print("• 鼠标滚轮: 以鼠标位置为中心缩放")
            print("• 左键拖拽: 平移视图查看不同时间段")
            print("\n📝 框选缩放使用方法:")
            print("  1. 点击工具栏的放大镜图标（带虚线方框的图标）")
            print("  2. 在图表上按住左键拖拽选择要放大的区域")
            print("  3. 释放鼠标完成缩放")
            print("  4. 点击Home按钮（房子图标）恢复原始视图")
            print("="*80)

        except Exception as e:
            logger.warning(f"添加交互式功能时出错: {e}, 将使用标准绘图模式")



    def analyze_and_plot(self, df: pd.DataFrame, stock_code: str = "股票",
                        save_path: Optional[str] = None, show_plot: bool = True,
                        interactive: bool = True, show_volume_ma: bool = True) -> Dict:
        """
        分析swing点并绘制图表的一体化方法

        Args:
            df (pd.DataFrame): 股票数据，必须包含Open、High、Low、Close、Volume列，索引为时间
            stock_code (str): 股票代码，用于图表标题
            save_path (str, optional): 保存路径，如果为None则自动保存到同级目录
            show_plot (bool): 是否显示图表，默认True
            interactive (bool): 是否启用交互式功能，默认True
            show_volume_ma (bool): 是否显示成交量20日均线，默认True

        Returns:
            dict: 包含所有swing点信息的字典
        """
        # 先进行分析
        result = self.analyze_swing_points(df)

        # 然后绘制图表
        self.plot_swing_chart(df, stock_code, save_path, show_plot, interactive, show_volume_ma)

        return result


def create_analyzer(swing_length: int = 10) -> SwingPointAnalyzer:
    """
    创建swing点分析器的便捷函数

    Args:
        swing_length (int): swing点检测的左右确认周期，默认10

    Returns:
        SwingPointAnalyzer: 分析器实例
    """
    return SwingPointAnalyzer(swing_length)


def analyze_swing_points(df: pd.DataFrame, swing_length: int = 10) -> Dict:
    """
    一键分析K线数据的swing点

    Args:
        df (pd.DataFrame): 股票数据，必须包含Open、High、Low、Close、Volume列，索引为时间
        swing_length (int): swing点检测的左右确认周期，默认10

    Returns:
        dict: 完整的swing点分析结果
    """
    analyzer = SwingPointAnalyzer(swing_length)
    return analyzer.analyze_swing_points(df)


def analyze_and_plot_swing_points(df: pd.DataFrame, stock_code: str = "股票",
                                 swing_length: int = 10, save_path: Optional[str] = None,
                                 show_plot: bool = True, interactive: bool = True,
                                 show_volume_ma: bool = True) -> Dict:
    """
    一键分析K线数据的swing点并绘制图表

    Args:
        df (pd.DataFrame): 股票数据，必须包含Open、High、Low、Close、Volume列，索引为时间
        stock_code (str): 股票代码，用于图表标题
        swing_length (int): swing点检测的左右确认周期，默认10
        save_path (str, optional): 保存路径，如果为None则自动保存到同级目录
        show_plot (bool): 是否显示图表，默认True
        interactive (bool): 是否启用交互式功能，默认True
        show_volume_ma (bool): 是否显示成交量20日均线，默认True

    Returns:
        dict: 完整的swing点分析结果
    """
    analyzer = SwingPointAnalyzer(swing_length)
    return analyzer.analyze_and_plot(df, stock_code, save_path, show_plot, interactive, show_volume_ma)


def analyze_swing_points_with_xtdata(stock_code: str, swing_length: int = 10,
                                    period: str = '15m', count: int = 3022,
                                    download_days: int = 200, show_plot: bool = True,
                                    save_path: Optional[str] = None, interactive: bool = True) -> Dict:
    """
    使用xtdata数据源一键分析股票的swing点并绘制图表

    Args:
        stock_code (str): 股票代码，如 "600895.SH" 或 "00700.HK"
        swing_length (int): swing点检测的左右确认周期，默认10
        period (str): 分析的时间周期，默认 '15m'
        count (int): 获取K线数据条数，默认3022条
        download_days (int): 下载历史数据天数，默认200天
        show_plot (bool): 是否显示图表，默认True
        save_path (str, optional): 保存路径，如果为None则自动保存到同级目录
        interactive (bool): 是否启用交互式功能，默认True

    Returns:
        dict: 完整的swing点分析结果，如果失败返回空字典
    """
    if not XTDATA_AVAILABLE:
        logger.error("xtdata未安装，无法使用此功能")
        return {}

    try:
        # 创建数据加载器
        data_loader = XtDataLoader()

        # 下载历史数据
        logger.info(f"开始下载 {stock_code} 的历史数据...")
        download_success = data_loader.download_history_data(
            stock_code=stock_code,
            periods=['1m', '5m', '15m'],
            days=download_days
        )

        if not download_success:
            logger.error("历史数据下载失败")
            return {}

        # 获取K线数据
        logger.info(f"获取 {stock_code} 的 {period} 周期K线数据...")
        df = data_loader.get_kline_data(
            stock_code=stock_code,
            period=period,
            count=count
        )

        if df.empty:
            logger.error("K线数据获取失败")
            return {}

        # 进行swing点分析
        logger.info("开始swing点分析...")
        analyzer = SwingPointAnalyzer(swing_length)
        result = analyzer.analyze_and_plot(df, stock_code, save_path, show_plot, interactive)

        logger.info("swing点分析完成")
        return result

    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        return {}


# 示例用法
if __name__ == "__main__":
    import sys
    import os

    # 添加当前目录到路径，以便导入kline_plotter
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))


    def demo_with_xtdata():
        """使用xtdata数据源演示API功能"""
        print("=== Swing点分析API演示 (使用xtdata数据源) ===")

        if not XTDATA_AVAILABLE:
            print("❌ xtdata未安装，无法运行演示")
            print("请安装: pip install xtquant")
            return

        try:
            # 股票代码 - 可以是A股或港股
            stock_code = "600895.SH"  # 浦发银行
            # stock_code = "00700.HK"  # 腾讯控股 (港股示例)

            print(f"正在使用xtdata获取 {stock_code} 的股票数据...")

            # 创建数据加载器
            data_loader = XtDataLoader()

            # 第一步：下载历史数据到本地缓存
            print("\n=== 第一步：下载历史数据 ===")
            download_success = data_loader.download_history_data(
                stock_code=stock_code,
                periods=['1m', '5m', '15m'],  # 下载1分钟、5分钟、15分钟数据
                days=200  # 下载200天的数据
            )

            if not download_success:
                print("❌ 历史数据下载失败")
                return

            print("✅ 历史数据下载完成")

            # 第二步：获取15分钟K线数据用于绘图
            print("\n=== 第二步：获取15分钟K线数据 ===")
            df = data_loader.get_kline_data(
                stock_code=stock_code,
                period='15m',
                count=3022  # 获取3022根15分钟K线
            )

            if df.empty:
                print("❌ K线数据获取失败")
                return

            print(f"✅ 数据获取成功: {len(df)}条15分钟K线数据")
            print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
            print(f"价格范围: {df['Low'].min():.2f} - {df['High'].max():.2f}")

            # 第三步：创建分析器并进行swing点分析
            print("\n=== 第三步：Swing点分析和绘图 ===")
            analyzer = create_analyzer(swing_length=10)  # 使用10周期的swing检测

            # 进行swing点分析并绘制图表
            print("正在进行swing点分析并绘制图表...")
            print("提示: 图表支持交互式操作 - 鼠标滚轮缩放, R键重置视图, H键显示帮助")
            result = analyzer.analyze_and_plot(df, stock_code, show_plot=True, interactive=True)

            # 显示分析结果摘要
            print(f"\n=== 分析结果摘要 ===")
            print(f"总K线数量: {result['data_info']['total_candles']}")
            print(f"Swing点: {result['swing_points']['total_swing_highs']}个swing high点, {result['swing_points']['total_swing_lows']}个swing low点")
            print(f"分析参数: swing_length={result['analysis_params']['swing_length']}")

            # 显示所有具体的swing点信息
            print(f"\n=== 所有Swing点详情 ===")
            if result['swing_points']['swing_highs']:
                print(f"全部Swing High点 (共{len(result['swing_points']['swing_highs'])}个):")
                for swing in result['swing_points']['swing_highs']:
                    print(f"  时间: {swing['time']}, 价格: {swing['price']:.2f}")

            if result['swing_points']['swing_lows']:
                print(f"全部Swing Low点 (共{len(result['swing_points']['swing_lows'])}个):")
                for swing in result['swing_points']['swing_lows']:
                    print(f"  时间: {swing['time']}, 价格: {swing['price']:.2f}")

            print(f"\n=== API调用示例 ===")
            print("# 使用xtdata数据源的完整流程:")
            print("from support_and_resistance_zone import XtDataLoader, create_analyzer")
            print("")
            print("# 1. 创建数据加载器")
            print("data_loader = XtDataLoader()")
            print("")
            print("# 2. 下载历史数据")
            print("data_loader.download_history_data('600895.SH', ['1m', '5m', '15m'], 200)")
            print("")
            print("# 3. 获取K线数据")
            print("df = data_loader.get_kline_data('600895.SH', '15m', 3022)")
            print("")
            print("# 4. 分析并绘图")
            print("analyzer = create_analyzer(swing_length=10)")
            print("result = analyzer.analyze_and_plot(df, '600895.SH')")

        except Exception as e:
            print(f"演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

    def demo_with_sample_data():
        """使用示例数据演示API功能（保留原有功能作为备选）"""
        print("=== Swing点分析API演示 (使用pytdx数据源) ===")

        try:
            # 尝试从kline_plotter获取真实数据
            from kline_plotter import KLinePlotter

            print("正在获取真实股票数据...")
            plotter = KLinePlotter()
            df, period_name = plotter.load_stock_data_from_pytdx("600895.SH", 6)
            filtered_df = plotter.filter_data_by_current_date(df, 6)

            print(f"数据获取成功: {len(filtered_df)}条{period_name}K线数据")
            print(f"时间范围: {filtered_df.index[0]} 到 {filtered_df.index[-1]}")

            # 创建分析器
            analyzer = create_analyzer(swing_length=8)

            # 进行swing点分析并绘制图表
            print("\n正在进行swing点分析并绘制图表...")
            print("提示: 图表支持交互式操作 - 鼠标滚轮缩放, R键重置视图, H键显示帮助")
            result = analyzer.analyze_and_plot(filtered_df, "600895.SH", show_plot=True, interactive=True)

            # 显示分析结果摘要
            print(f"\n=== 分析结果摘要 ===")
            print(f"Swing点: {result['swing_points']['total_swing_highs']}个swing high点, {result['swing_points']['total_swing_lows']}个swing low点")

            # 显示所有具体的swing点信息
            print(f"\n=== 所有Swing点详情 ===")
            if result['swing_points']['swing_highs']:
                print(f"全部Swing High点 (共{len(result['swing_points']['swing_highs'])}个):")
                for swing in result['swing_points']['swing_highs']:
                    print(f"  时间: {swing['time']}, 价格: {swing['price']:.2f}")

            if result['swing_points']['swing_lows']:
                print(f"全部Swing Low点 (共{len(result['swing_points']['swing_lows'])}个):")
                for swing in result['swing_points']['swing_lows']:
                    print(f"  时间: {swing['time']}, 价格: {swing['price']:.2f}")

            print(f"\n=== API调用示例 ===")
            print("# 基本用法（仅分析）:")
            print("from support_and_resistance_zone import analyze_swing_points")
            print("result = analyze_swing_points(df)")
            print("")
            print("# 分析并绘图:")
            print("from support_and_resistance_zone import analyze_and_plot_swing_points")
            print("result = analyze_and_plot_swing_points(df, '600895.SH')")
            print("")
            print("# 高级用法:")
            print("from support_and_resistance_zone import create_analyzer")
            print("analyzer = create_analyzer(swing_length=10)")
            print("result = analyzer.analyze_and_plot(df, '600895.SH')")

        except ImportError:
            print("无法导入kline_plotter，请确保文件在同一目录下")
        except Exception as e:
            print(f"演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


    # 运行演示 - 优先使用xtdata，如果不可用则使用pytdx
    if XTDATA_AVAILABLE:
        demo_with_xtdata()
    else:
        print("xtdata不可用，使用pytdx数据源...")
        demo_with_sample_data()