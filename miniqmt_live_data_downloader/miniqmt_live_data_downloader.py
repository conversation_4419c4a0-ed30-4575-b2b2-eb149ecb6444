# coding:utf-8
import time
import pandas as pd
from datetime import datetime, timedelta
import shutil
import os

from xtquant import xtdata

# https://blog.csdn.net/u010214511/article/details/134395748 【miniQMT实盘量化3】获取历史行情数据

# # 修改前: 这里传空列表表示返回所有字段
# field_list = []
# 修改后: 只返回指定字段数据
field_list = []

code = "600895.SH"

## 根据系统当前时间计算过去60个周期（分钟级数据）时间区间
now = datetime.now()

from datetime import time

def get_last_trading_day(today):
    one_day = timedelta(days=1)
    last_day = today - one_day
    while last_day.weekday() >= 5:  # Saturday=5, Sunday=6
        last_day -= one_day
    return last_day

# 定义当天交易时段（上证指数交易时间：上午9:30～11:30，下午13:00～15:00）
today = now.date()
morning_start = datetime(today.year, today.month, today.day, 9, 30)
morning_end = datetime(today.year, today.month, today.day, 11, 30)
afternoon_start = datetime(today.year, today.month, today.day, 13, 0)
afternoon_end = datetime(today.year, today.month, today.day, 15, 0)

# 判断当前时间处于哪个交易时段，并确定有效的结束时间（effective_end）和会话开始时间（session_start）
if now < morning_start:
    # 如果当前还未开盘，则使用上一交易日的下午交易收盘时段
    last_day = get_last_trading_day(today)
    session_start = datetime(last_day.year, last_day.month, last_day.day, 13, 0)
    session_end = datetime(last_day.year, last_day.month, last_day.day, 15, 0)
    effective_end = session_end
elif morning_start <= now < morning_end:
    session_start = morning_start
    session_end = morning_end
    effective_end = now  if now < morning_end else morning_end
elif morning_end <= now < afternoon_start:
    # 午休时段，使用上午交易收盘时间作为结束时间
    session_start = morning_start
    session_end = morning_end
    effective_end = morning_end
elif afternoon_start <= now < afternoon_end:
    session_start = afternoon_start
    session_end = afternoon_end
    effective_end = now  if now < afternoon_end else afternoon_end
else:
    session_start = afternoon_start
    session_end = afternoon_end
    effective_end = afternoon_end

# 计算"过去60个周期"的起始时间：不能早于交易时段的开始时间
if effective_end - timedelta(minutes=59) >= session_start:
    start_time_dt = effective_end - timedelta(minutes=59)
else:
    start_time_dt = session_start

end_time_dt = effective_end

start_time_str = start_time_dt.strftime("%Y%m%d%H%M%S")
end_time_str = end_time_dt.strftime("%Y%m%d%H%M%S")

# 下载历史数据 下载接口本身不返回数据（分钟级数据，根据系统时间获取过去60个周期数据）
xtdata.download_history_data(code, period='1m', start_time=start_time_str, end_time=end_time_str)

# 获取行情数据（分钟级数据，根据系统当前时间获取过去60个周期数据）
data = xtdata.get_market_data(field_list, [code], period='1m', start_time=start_time_str, end_time=end_time_str,
                              dividend_type='front')

# 将每个字段的最后一期（最后一列）的值抽取出来构造一个汇总记录
last_record = {}
for field, df in data.items():
    # 假设df为单行 DataFrame, 取该行最后一列的数据
    # 注意：这里假设行索引存在且只有一行
    if isinstance(df, pd.DataFrame) and not df.empty:
        last_value = df.iloc[0, -1]
        last_record[field] = last_value
    else:
        last_record[field] = None
print("Last record data (aggregated):")
print(last_record)
