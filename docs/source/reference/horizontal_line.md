# `HorizontalLine`


````{py:class} HorizontalLine(price: NUM, color: COLOR, width: int, style: LINE_STYLE, text: str, axis_label_visible: bool, func: callable= None)

The `HorizontalLine` object represents a `PriceLine` in Lightweight Charts.

Its instance should be accessed from the `horizontal_line` method.



```{py:method} update(price: NUM)

Updates the price of the horizontal line.
```


```{py:method} label(text: str)

Updates the label of the horizontal line.
```


```{py:method} delete()

Irreversibly deletes the horizontal line.
```
````