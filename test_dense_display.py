#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试K线密集显示效果的脚本
验证min_bar_spacing和fit()方法的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
import pandas as pd
from lightweight_charts.widgets import Qt<PERSON><PERSON>

def create_test_data():
    """创建测试用的K线数据"""
    import numpy as np
    from datetime import datetime, timedelta
    
    # 创建500根K线的测试数据
    start_time = datetime.now() - timedelta(days=100)
    times = []
    opens = []
    highs = []
    lows = []
    closes = []
    volumes = []
    
    base_price = 100.0
    for i in range(500):
        time_str = (start_time + timedelta(minutes=i*15)).strftime('%Y-%m-%d %H:%M')
        times.append(time_str)
        
        # 生成随机价格数据
        open_price = base_price + np.random.uniform(-2, 2)
        close_price = open_price + np.random.uniform(-3, 3)
        high_price = max(open_price, close_price) + np.random.uniform(0, 2)
        low_price = min(open_price, close_price) - np.random.uniform(0, 2)
        volume = np.random.randint(1000, 10000)
        
        opens.append(open_price)
        highs.append(high_price)
        lows.append(low_price)
        closes.append(close_price)
        volumes.append(volume)
        
        base_price = close_price  # 下一根K线的基准价格
    
    return pd.DataFrame({
        'time': times,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    })

def test_dense_display():
    """测试密集显示效果"""
    app = QApplication([])
    
    # 创建两个图表进行对比
    chart1 = QtChart()
    chart1.get_webview().setWindowTitle("普通显示 (min_bar_spacing=0.5)")

    chart2 = QtChart()
    chart2.get_webview().setWindowTitle("密集显示 (min_bar_spacing=0.01)")
    
    # 生成测试数据
    test_data = create_test_data()
    print(f"生成了 {len(test_data)} 根K线的测试数据")
    
    # 配置第一个图表（普通显示）
    chart1.time_scale(
        right_offset=0,
        min_bar_spacing=0.5,  # 普通间距
        visible=True,
        time_visible=True,
        seconds_visible=False,
        border_visible=True
    )
    
    # 配置第二个图表（密集显示）
    chart2.time_scale(
        right_offset=0,
        min_bar_spacing=0.01,  # 极密集间距
        visible=True,
        time_visible=True,
        seconds_visible=False,
        border_visible=True
    )
    
    # 设置K线颜色
    for chart in [chart1, chart2]:
        chart.candle_style(
            up_color='rgba(255, 82, 82, 100)',      # 红色 - 上涨
            down_color='rgba(54, 207, 113, 100)',   # 绿色 - 下跌
            border_up_color='rgba(255, 82, 82, 100)',
            border_down_color='rgba(54, 207, 113, 100)',
            wick_up_color='rgba(255, 82, 82, 100)',
            wick_down_color='rgba(54, 207, 113, 100)'
        )
    
    # 设置数据
    chart1.set(test_data)
    chart2.set(test_data)
    
    # 延迟调用fit()方法
    def apply_fit():
        try:
            print("应用fit()方法到密集显示图表...")
            chart2.fit()
            print("fit()方法应用完成")
        except Exception as e:
            print(f"fit()方法应用失败: {e}")
    
    QTimer.singleShot(1000, apply_fit)
    
    # 显示图表
    chart1.get_webview().show()
    chart2.get_webview().show()

    # 调整窗口位置
    chart1.get_webview().move(100, 100)
    chart2.get_webview().move(600, 100)
    
    print("测试图表已显示:")
    print("- 左侧图表: 普通显示 (min_bar_spacing=0.5)")
    print("- 右侧图表: 密集显示 (min_bar_spacing=0.01) + fit()")
    print("请对比两个图表的K线密度差异")
    
    app.exec()

if __name__ == "__main__":
    test_dense_display()
