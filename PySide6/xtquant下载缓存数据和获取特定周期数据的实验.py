#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
xtquant下载缓存数据和获取特定周期数据的实验
通过xtdata.download_history_data以及后get_market_data，
先后下载港股通股票06060.HK的7月25日一整天的1min的K线，以及5minK线以及15min的K线和60min的K线
严格按照先1min的K线，后5min、15min、60min的顺序
最后也按照这个顺序打印一整天的1min、5min、15min、60min的K线的数据
"""

import sys
import os
import time
from datetime import datetime, timedelta
import pandas as pd
import csv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入xtquant
try:
    from xtquant import xtdata
    XTQUANT_AVAILABLE = True
    print("✅ xtquant导入成功")
except ImportError:
    XTQUANT_AVAILABLE = False
    print("❌ xtquant未安装，请先安装: pip install xtquant")
    sys.exit(1)

# 配置参数
STOCK_CODE = "02228.HK"  #
DOWNLOAD_DATE = "20250725"  # 2025年7月25日 (仅下载这一天的数据)
GET_START_DATE = "20250724"  # 2025年7月24日 (获取数据的开始日期)
GET_END_DATE = "20250725"    # 2025年7月25日 (获取数据的结束日期)
TIMEFRAMES = ["1m", "5m", "15m", "60m"]  # 严格按照顺序

# 定义要获取的字段
FIELD_LIST = [
    'time',      # 时间戳
    'open',      # 开盘价
    'high',      # 最高价
    'low',       # 最低价
    'close',     # 收盘价
    'volume',    # 成交量
    'amount'     # 成交额
]

def check_xtquant_connection():
    """
    检查xtquant软件连接状态
    """
    try:
        # 尝试获取一个简单的数据来测试连接
        test_data = xtdata.get_market_data(
            field_list=['close'],
            stock_list=[STOCK_CODE],
            period='1d',
            count=1,
            dividend_type='none'
        )

        if test_data and 'close' in test_data:
            print("✅ xtquant连接正常")
            return True
        else:
            print("❌ xtquant软件未启动或连接失败")
            return False
    except Exception as e:
        print(f"❌ xtquant连接测试失败: {e}")
        return False

def download_history_data(stock_code, period, download_date):
    """
    下载指定单日的历史数据到本地缓存

    参数:
    stock_code: 股票代码
    period: 时间周期 (1m, 5m, 15m, 60m)
    download_date: 下载日期 (YYYYMMDD格式)
    """
    try:
        # 构造时间范围 - 港股交易时间（仅单天）
        start_time = f"{download_date}093000"  # 当天09:30:00 (港股开盘)
        end_time = f"{download_date}160000"    # 当天16:00:00 (港股收盘)

        print(f"📥 开始下载 {stock_code} {period} 周期历史数据...")
        print(f"   时间范围: {start_time} - {end_time} (港股交易时间)")
        print(f"   下载日期: {download_date[:4]}-{download_date[4:6]}-{download_date[6:8]} (仅单天)")

        # 下载历史数据到本地缓存
        xtdata.download_history_data(
            stock_code=stock_code,
            period=period,
            start_time=start_time,
            end_time=end_time
        )

        print(f"✅ {period} 周期历史数据下载完成")
        return True

    except Exception as e:
        print(f"❌ {period} 周期历史数据下载失败: {e}")
        return False

def get_market_data(stock_code, period, get_start_date, get_end_date):
    """
    从本地缓存获取市场数据（可能跨越多天，即使只下载了部分天数）

    参数:
    stock_code: 股票代码
    period: 时间周期 (1m, 5m, 15m, 60m)
    get_start_date: 获取开始日期 (YYYYMMDD格式)
    get_end_date: 获取结束日期 (YYYYMMDD格式)
    """
    try:
        # 构造时间范围 - 港股交易时间
        start_time = f"{get_start_date}093000"
        end_time = f"{get_end_date}160000"

        print(f"📊 开始获取 {stock_code} {period} 周期市场数据...")
        print(f"   时间范围: {start_time} - {end_time}")
        print(f"   获取范围: {get_start_date[:4]}-{get_start_date[4:6]}-{get_start_date[6:8]} 到 {get_end_date[:4]}-{get_end_date[4:6]}-{get_end_date[6:8]} (可能包含未下载的日期)")

        # 方法1: 使用时间范围获取数据
        data = xtdata.get_market_data(
            field_list=FIELD_LIST,
            stock_list=[stock_code],
            period=period,
            start_time=start_time,
            end_time=end_time,
            count=-1,  # -1表示获取所有数据
            dividend_type='none',
            fill_data=True
        )

        # 如果方法1没有数据，尝试方法2: 使用count参数
        if not data or all(df.empty for df in data.values() if isinstance(df, pd.DataFrame)):
            print(f"   方法1无数据，尝试方法2: 使用count参数...")
            data = xtdata.get_market_data(
                field_list=FIELD_LIST,
                stock_list=[stock_code],
                period=period,
                count=500,  # 获取最近500条数据
                dividend_type='none',
                fill_data=True
            )

        # 如果方法2还是没有数据，尝试方法3: 使用get_market_data_ex
        if not data or all(df.empty for df in data.values() if isinstance(df, pd.DataFrame)):
            print(f"   方法2无数据，尝试方法3: 使用get_market_data_ex...")
            data = xtdata.get_market_data_ex(
                field_list=FIELD_LIST,
                stock_list=[stock_code],
                period=period,
                start_time=start_time,
                end_time=end_time,
                count=-1,
                dividend_type='none',
                fill_data=True
            )

        if not data:
            print(f"❌ {period} 周期数据为空")
            return None

        print(f"✅ {period} 周期市场数据获取完成")
        return data

    except Exception as e:
        print(f"❌ {period} 周期市场数据获取失败: {e}")
        return None

def process_and_print_data(data, period, stock_code):
    """
    处理并打印K线数据

    参数:
    data: 从xtdata获取的原始数据
    period: 时间周期
    stock_code: 股票代码
    """
    if not data:
        print(f"❌ {period} 周期无数据可打印")
        return

    try:
        print(f"\n{'='*80}")
        print(f"📈 {stock_code} - {period} 周期 K线数据 ({GET_START_DATE[:4]}年{GET_START_DATE[4:6]}月{GET_START_DATE[6:8]}日 - {GET_END_DATE[:4]}年{GET_END_DATE[4:6]}月{GET_END_DATE[6:8]}日)")
        print(f"{'='*80}")

        # 调试：打印原始数据结构
        print(f"🔍 调试信息:")
        print(f"   数据类型: {type(data)}")
        print(f"   数据键: {list(data.keys()) if isinstance(data, dict) else '非字典类型'}")

        # 检查数据结构
        if not isinstance(data, dict):
            print(f"❌ 数据格式错误: {type(data)}")
            return

        # 调试每个字段的详细信息
        for key, value in data.items():
            print(f"   字段 '{key}': 类型={type(value)}")
            if isinstance(value, pd.DataFrame):
                print(f"     DataFrame形状: {value.shape}")
                print(f"     DataFrame索引: {value.index.tolist()}")
                print(f"     DataFrame列: {value.columns.tolist()[:10]}...")  # 显示前10列
                if not value.empty:
                    print(f"     DataFrame样本数据:")
                    print(f"       {value.head()}")
            elif isinstance(value, (list, tuple)):
                print(f"     列表长度: {len(value)}")
                if len(value) > 0:
                    print(f"     前几个元素: {value[:3]}")

        # 处理不同的数据结构
        times = []
        main_df = None

        # 检查是否有时间字段（get_market_data格式）
        if 'time' in data:
            print("✅ 找到 'time' 字段 (get_market_data格式)")
            time_data = data['time']
            if isinstance(time_data, pd.DataFrame):
                if stock_code in time_data.index:
                    times = time_data.loc[stock_code].dropna().tolist()
                    print(f"   获取到 {len(times)} 个时间点")
                else:
                    print(f"   股票代码 {stock_code} 不在时间数据中")
        # 检查是否是get_market_data_ex格式（股票代码作为键）
        elif stock_code in data:
            print("✅ 找到股票代码字段 (get_market_data_ex格式)")
            main_df = data[stock_code]
            if isinstance(main_df, pd.DataFrame):
                print(f"   DataFrame形状: {main_df.shape}")
                if not main_df.empty and 'time' in main_df.columns:
                    times = main_df['time'].tolist()
                    print(f"   从DataFrame获取到 {len(times)} 个时间点")
                else:
                    print(f"   DataFrame为空或缺少time列")
        else:
            print("❌ 无法识别数据格式")
            return

        if not times:
            print(f"❌ {period} 周期无时间数据")
            print(f"🔍 {GET_START_DATE[:4]}-{GET_START_DATE[4:6]}-{GET_START_DATE[6:8]} 到 {GET_END_DATE[:4]}-{GET_END_DATE[4:6]}-{GET_END_DATE[6:8]} 期间可能没有交易数据")
            print(f"💡 注意：只下载了 {DOWNLOAD_DATE[:4]}-{DOWNLOAD_DATE[4:6]}-{DOWNLOAD_DATE[6:8]} 的数据，但尝试获取两天数据")
            return

        # 获取OHLCV数据
        ohlcv_data = {}

        if main_df is not None:
            # get_market_data_ex格式：从主DataFrame获取数据
            print("📊 从主DataFrame获取OHLCV数据...")
            for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                if field in main_df.columns:
                    ohlcv_data[field] = main_df[field].tolist()
                    print(f"   字段 {field}: {len(ohlcv_data[field])} 个数据点")
                else:
                    ohlcv_data[field] = []
                    print(f"   字段 {field}: 不存在")
        else:
            # get_market_data格式：从分离的字段获取数据
            print("📊 从分离字段获取OHLCV数据...")
            for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                if field in data:
                    field_data = data[field]
                    if isinstance(field_data, pd.DataFrame):
                        if stock_code in field_data.index:
                            ohlcv_data[field] = field_data.loc[stock_code].dropna().tolist()
                            print(f"   字段 {field}: 获取到 {len(ohlcv_data[field])} 个数据点")
                        else:
                            # 尝试使用第一个可用的股票代码
                            if not field_data.empty and len(field_data.index) > 0:
                                actual_code = field_data.index[0]
                                ohlcv_data[field] = field_data.loc[actual_code].dropna().tolist()
                                print(f"   字段 {field}: 使用代码 {actual_code} 获取到 {len(ohlcv_data[field])} 个数据点")
                            else:
                                ohlcv_data[field] = []
                                print(f"   字段 {field}: 无数据")
                    else:
                        ohlcv_data[field] = []
                        print(f"   字段 {field}: 数据格式错误 {type(field_data)}")
                else:
                    ohlcv_data[field] = []
                    print(f"   字段 {field}: 字段不存在")

        # 确保所有数据长度一致
        data_length = len(times)
        for field in ohlcv_data:
            if len(ohlcv_data[field]) != data_length:
                print(f"⚠️  {field} 数据长度不匹配: {len(ohlcv_data[field])} vs {data_length}")
                # 补齐或截断数据
                if len(ohlcv_data[field]) < data_length:
                    ohlcv_data[field].extend([0] * (data_length - len(ohlcv_data[field])))
                else:
                    ohlcv_data[field] = ohlcv_data[field][:data_length]

        print(f"📊 数据统计:")
        print(f"   总K线数量: {data_length}")
        print(f"   时间范围: {times[0] if times else '无'} - {times[-1] if times else '无'}")
        print(f"   数据字段: {list(ohlcv_data.keys())}")

        # 打印详细K线数据
        print(f"\n📋 详细K线数据:")
        print(f"{'序号':<4} {'时间':<19} {'开盘':<8} {'最高':<8} {'最低':<8} {'收盘':<8} {'成交量':<12} {'成交额':<15}")
        print("-" * 90)

        for i, timestamp in enumerate(times):
            # 转换时间戳
            try:
                if isinstance(timestamp, (int, float)):
                    dt = datetime.fromtimestamp(timestamp / 1000)  # 假设是毫秒时间戳
                    time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    time_str = str(timestamp)
            except:
                time_str = str(timestamp)

            # 获取OHLCV数据
            open_price = ohlcv_data['open'][i] if i < len(ohlcv_data['open']) else 0
            high_price = ohlcv_data['high'][i] if i < len(ohlcv_data['high']) else 0
            low_price = ohlcv_data['low'][i] if i < len(ohlcv_data['low']) else 0
            close_price = ohlcv_data['close'][i] if i < len(ohlcv_data['close']) else 0
            volume = ohlcv_data['volume'][i] if i < len(ohlcv_data['volume']) else 0
            amount = ohlcv_data['amount'][i] if i < len(ohlcv_data['amount']) else 0

            print(f"{i+1:<4} {time_str:<19} {open_price:<8.3f} {high_price:<8.3f} {low_price:<8.3f} {close_price:<8.3f} {volume:<12.0f} {amount:<15.0f}")

        print(f"\n✅ {period} 周期数据打印完成")

        # 保存数据到CSV文件
        save_to_csv(times, ohlcv_data, period, stock_code)

    except Exception as e:
        print(f"❌ 处理 {period} 周期数据时出错: {e}")

def save_to_csv(times, ohlcv_data, period, stock_code):
    """
    将K线数据保存到CSV文件

    参数:
    times: 时间列表
    ohlcv_data: OHLCV数据字典
    period: 时间周期
    stock_code: 股票代码
    """
    try:
        # 构造文件名
        start_date_str = GET_START_DATE[:4] + GET_START_DATE[4:6] + GET_START_DATE[6:8]  # YYYYMMDD
        end_date_str = GET_END_DATE[:4] + GET_END_DATE[4:6] + GET_END_DATE[6:8]  # YYYYMMDD
        download_date_str = DOWNLOAD_DATE[:4] + DOWNLOAD_DATE[4:6] + DOWNLOAD_DATE[6:8]  # YYYYMMDD
        filename = f"{stock_code}_{period}_{start_date_str}_to_{end_date_str}_download_{download_date_str}.csv"
        filepath = os.path.join(os.path.dirname(__file__), filename)

        print(f"💾 保存 {period} 周期数据到文件: {filename}")

        # 准备CSV数据
        csv_data = []

        # 添加表头
        headers = ['序号', '时间戳', '时间', '开盘', '最高', '最低', '收盘', '成交量', '成交额']
        csv_data.append(headers)

        # 添加数据行
        for i, timestamp in enumerate(times):
            # 转换时间戳
            try:
                if isinstance(timestamp, (int, float)):
                    dt = datetime.fromtimestamp(timestamp / 1000)  # 假设是毫秒时间戳
                    time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    time_str = str(timestamp)
            except:
                time_str = str(timestamp)

            # 获取OHLCV数据
            open_price = ohlcv_data['open'][i] if i < len(ohlcv_data['open']) else 0
            high_price = ohlcv_data['high'][i] if i < len(ohlcv_data['high']) else 0
            low_price = ohlcv_data['low'][i] if i < len(ohlcv_data['low']) else 0
            close_price = ohlcv_data['close'][i] if i < len(ohlcv_data['close']) else 0
            volume = ohlcv_data['volume'][i] if i < len(ohlcv_data['volume']) else 0
            amount = ohlcv_data['amount'][i] if i < len(ohlcv_data['amount']) else 0

            # 添加数据行
            row = [
                i + 1,  # 序号
                timestamp,  # 原始时间戳
                time_str,  # 格式化时间
                f"{open_price:.3f}",  # 开盘价
                f"{high_price:.3f}",  # 最高价
                f"{low_price:.3f}",   # 最低价
                f"{close_price:.3f}", # 收盘价
                f"{volume:.0f}",      # 成交量
                f"{amount:.0f}"       # 成交额
            ]
            csv_data.append(row)

        # 写入CSV文件
        with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(csv_data)

        print(f"✅ {period} 周期数据已保存到: {filepath}")
        print(f"   文件包含 {len(csv_data)-1} 条K线数据\n")

    except Exception as e:
        print(f"❌ 保存 {period} 周期CSV文件失败: {e}\n")

def main():
    """
    主函数 - 按照严格顺序下载和获取数据
    """
    print("🚀 xtquant下载缓存数据和获取特定周期数据的实验 (测试模式)")
    print("=" * 80)
    print(f"📥 下载日期: {DOWNLOAD_DATE[:4]}年{DOWNLOAD_DATE[4:6]}月{DOWNLOAD_DATE[6:8]}日 (仅下载单天)")
    print(f"📊 获取范围: {GET_START_DATE[:4]}年{GET_START_DATE[4:6]}月{GET_START_DATE[6:8]}日 - {GET_END_DATE[:4]}年{GET_END_DATE[4:6]}月{GET_END_DATE[6:8]}日 (尝试获取两天)")
    print(f"📈 股票代码: {STOCK_CODE}")
    print(f"⏰ 时间周期: {' -> '.join(TIMEFRAMES)}")
    print("=" * 80)

    # 检查xtquant连接
    if not check_xtquant_connection():
        print("❌ xtquant连接失败，程序退出")
        return

    # 存储所有数据
    all_data = {}

    print("\n🔄 第一阶段: 按顺序下载历史数据到本地缓存")
    print("-" * 60)

    # 第一阶段：按顺序下载历史数据（仅单天）
    for period in TIMEFRAMES:
        print(f"\n📥 下载 {period} 周期数据...")
        success = download_history_data(STOCK_CODE, period, DOWNLOAD_DATE)
        if not success:
            print(f"❌ {period} 周期数据下载失败，跳过")
        time.sleep(1)  # 避免请求过快

    print("\n🔄 第二阶段: 按顺序获取市场数据")
    print("-" * 60)

    # 第二阶段：按顺序获取市场数据（尝试获取两天）
    for period in TIMEFRAMES:
        print(f"\n📊 获取 {period} 周期数据...")
        data = get_market_data(STOCK_CODE, period, GET_START_DATE, GET_END_DATE)
        all_data[period] = data
        time.sleep(1)  # 避免请求过快

    print("\n🔄 第三阶段: 按顺序打印K线数据")
    print("-" * 60)

    # 第三阶段：按顺序打印数据
    for period in TIMEFRAMES:
        process_and_print_data(all_data[period], period, STOCK_CODE)

    print("🎉 实验完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()