import pandas as pd
from PySide6.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QVBoxLayout, QHBoxLayout,
                               QWidget, QStackedWidget, QPushButton, QLabel, QMessageBox,
                               QGridLayout, QSizePolicy, QTabWidget, QTabBar)
from PySide6.QtCore import QThread, Signal, Qt, QTimer
from PySide6.QtGui import QMouseEvent
import time

from lightweight_charts.widgets import QtChart
from datetime import datetime, timedelta
from pytdx.hq import TdxHq_API

# 导入SWING和FVG支撑阻力分析器
try:
    from support_and_resistance_zone import SwingPointAnalyzer
    SWING_ANALYZER_AVAILABLE = True
    print("SWING和FVG分析器导入成功")
except ImportError as e:
    SWING_ANALYZER_AVAILABLE = False
    print(f"SWING和FVG分析器导入失败: {e}")

# 全局SWING分析器实例
swing_analyzer = None
if SWING_ANALYZER_AVAILABLE:
    swing_analyzer = SwingPointAnalyzer(swing_length=10)  # 使用10周期的swing检测

# 导入xtquant用于港股数据
try:
    from xtquant import xtdata
    XTQUANT_AVAILABLE = True
    # print("xtquant导入成功，支持港股数据")
except ImportError:
    XTQUANT_AVAILABLE = False
    # print("xtquant未安装，仅支持A股数据")

# 导入港股交易时间检查函数
def is_hk_trading_time():
    """判断当前是否为港股交易时间段"""
    from datetime import datetime
    now = datetime.now()
    current_time = now.time()

    # 定义港股交易时间段
    morning_start = datetime.strptime("09:30", "%H:%M").time()
    morning_end = datetime.strptime("12:00", "%H:%M").time()
    afternoon_start = datetime.strptime("13:00", "%H:%M").time()
    afternoon_end = datetime.strptime("16:00", "%H:%M").time()

    # 判断是否在交易时间段内
    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session

# xtquant连接状态
XTQUANT_CONNECTED = False
XTQUANT_LAST_CHECK = 0

# 删除了时间戳和日志函数，保持最简

# SWING和FVG支撑阻力线缓存
swing_fvg_lines_cache = {}

def convert_chart_data_to_analyzer_format(df):
    """
    将图表数据格式转换为SwingPointAnalyzer所需的格式

    Args:
        df (pd.DataFrame): 图表数据，包含time, open, high, low, close, volume列

    Returns:
        pd.DataFrame: 转换后的数据，包含Open, High, Low, Close, Volume列，索引为时间
    """
    if df is None or df.empty:
        return pd.DataFrame()

    try:
        # 创建副本避免修改原数据
        analyzer_df = df.copy()

        # 重命名列以匹配分析器要求
        column_mapping = {
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        }

        analyzer_df = analyzer_df.rename(columns=column_mapping)

        # 确保时间列为索引
        if 'time' in analyzer_df.columns:
            analyzer_df['time'] = pd.to_datetime(analyzer_df['time'])
            analyzer_df = analyzer_df.set_index('time')

        # 确保数据类型正确
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if col in analyzer_df.columns:
                analyzer_df[col] = pd.to_numeric(analyzer_df[col], errors='coerce').fillna(0)

        return analyzer_df

    except Exception as e:
        print(f"数据格式转换失败: {e}")
        return pd.DataFrame()

def apply_swing_fvg_analysis_to_15min_chart(chart, symbol, df):
    """
    对15分钟和60分钟图表应用SWING和FVG支撑阻力分析

    Args:
        chart: 图表对象
        symbol (str): 股票代码
        df (pd.DataFrame): K线数据
    """
    if not SWING_ANALYZER_AVAILABLE or swing_analyzer is None:
        print(f"SWING和FVG分析器不可用，跳过分析: {symbol}")
        return

    try:
        # 转换数据格式
        analyzer_df = convert_chart_data_to_analyzer_format(df)
        if analyzer_df.empty:
            print(f"15分钟图表数据转换失败: {symbol}")
            return

        # 检查数据量是否足够进行分析
        if len(analyzer_df) < 50:  # 至少需要50根K线进行有效分析
            print(f"数据量不足，跳过SWING和FVG分析: {symbol} (数据量: {len(analyzer_df)})")
            return

        print(f"开始对{symbol}的图表进行SWING和FVG分析... (数据量: {len(analyzer_df)})")

        # 执行swing点分析
        swing_highs, swing_lows = swing_analyzer.find_swing_points(analyzer_df)

        # 识别FVG区域
        bullish_fvgs, bearish_fvgs = swing_analyzer._identify_fvg_zones(analyzer_df)

        # 清除之前的支撑阻力线
        cache_key = f"{symbol}_15min_{getattr(chart, 'chart_id', 'default')}"
        clear_previous_swing_fvg_lines(chart, cache_key)

        # 绘制SWING支撑阻力线
        draw_swing_lines(chart, symbol, swing_highs, swing_lows, analyzer_df, cache_key)

        # 绘制FVG区域线
        draw_fvg_lines(chart, symbol, bullish_fvgs, bearish_fvgs, analyzer_df, cache_key)

        print(f"SWING和FVG分析完成: {symbol} - {len(swing_highs)}个阻力位, {len(swing_lows)}个支撑位, {len(bullish_fvgs)}个看涨FVG, {len(bearish_fvgs)}个看跌FVG")

    except Exception as e:
        print(f"SWING和FVG分析失败: {symbol} - {e}")
        import traceback
        traceback.print_exc()


def clear_previous_swing_fvg_lines(chart, cache_key):
    """清除之前绘制的SWING和FVG线条"""
    global swing_fvg_lines_cache

    if cache_key in swing_fvg_lines_cache:
        try:
            for line in swing_fvg_lines_cache[cache_key]:
                if hasattr(line, 'delete'):
                    line.delete()
            swing_fvg_lines_cache[cache_key].clear()
        except Exception as e:
            print(f"清除SWING和FVG线条失败: {e}")

def clear_all_swing_fvg_lines_for_symbol(symbol):
    """清除指定股票的所有SWING和FVG线条缓存"""
    global swing_fvg_lines_cache

    try:
        # 找到所有与该股票相关的缓存键
        keys_to_remove = []
        for cache_key in swing_fvg_lines_cache.keys():
            if cache_key.startswith(f"{symbol}_"):
                keys_to_remove.append(cache_key)

        # 清除线条并删除缓存项
        for cache_key in keys_to_remove:
            try:
                for line in swing_fvg_lines_cache[cache_key]:
                    if hasattr(line, 'delete'):
                        line.delete()
                del swing_fvg_lines_cache[cache_key]
                print(f"已清除缓存: {cache_key}")
            except Exception as e:
                print(f"清除缓存失败: {cache_key} - {e}")

        print(f"已清除股票 {symbol} 的所有SWING和FVG线条缓存，共 {len(keys_to_remove)} 项")

    except Exception as e:
        print(f"清除股票SWING和FVG线条缓存失败: {symbol} - {e}")

def clear_all_charts_swing_fvg_lines(charts):
    """清除所有图表的SWING和FVG线条"""
    global swing_fvg_lines_cache

    try:
        # 清除所有线条
        for cache_key, lines in swing_fvg_lines_cache.items():
            try:
                for line in lines:
                    if hasattr(line, 'delete'):
                        line.delete()
            except Exception as e:
                print(f"清除线条失败: {cache_key} - {e}")

        # 清空整个缓存
        swing_fvg_lines_cache.clear()
        print("已清除所有图表的SWING和FVG线条缓存")

    except Exception as e:
        print(f"清除所有SWING和FVG线条缓存失败: {e}")

def draw_swing_lines(chart, symbol, swing_highs, swing_lows, analyzer_df, cache_key):
    """绘制SWING支撑阻力线"""
    global swing_fvg_lines_cache

    if cache_key not in swing_fvg_lines_cache:
        swing_fvg_lines_cache[cache_key] = []

    try:
        # 绘制阻力线（红色）- SWING HIGH点
        for swing in swing_highs:
            swing_time = swing['time']
            swing_price = swing['price']

            # 使用ray_line从swing点开始绘制到图表右侧
            resistance_line = chart.ray_line(
                start_time=swing_time,
                value=swing_price,
                color='rgba(255, 0, 0, 0.8)',  # 红色阻力线
                width=2,
                style='solid',
                text=f"15min阻力 {swing_price:.2f}"
            )
            swing_fvg_lines_cache[cache_key].append(resistance_line)

        # 绘制支撑线（绿色）- SWING LOW点
        for swing in swing_lows:
            swing_time = swing['time']
            swing_price = swing['price']

            # 使用ray_line从swing点开始绘制到图表右侧
            support_line = chart.ray_line(
                start_time=swing_time,
                value=swing_price,
                color='rgba(0, 255, 0, 0.8)',  # 绿色支撑线
                width=2,
                style='solid',
                text=f"15min支撑 {swing_price:.2f}"
            )
            swing_fvg_lines_cache[cache_key].append(support_line)

        print(f"SWING线条绘制完成: {len(swing_highs)}条阻力线, {len(swing_lows)}条支撑线")

    except Exception as e:
        print(f"绘制SWING线条失败: {e}")

def draw_fvg_lines(chart, symbol, bullish_fvgs, bearish_fvgs, analyzer_df, cache_key):
    """绘制FVG区域线条"""
    global swing_fvg_lines_cache

    if cache_key not in swing_fvg_lines_cache:
        swing_fvg_lines_cache[cache_key] = []

    try:
        # 绘制看涨FVG区域（绿色）
        for fvg in bullish_fvgs:
            # 看涨FVG：线条应该从前K线的最高价开始绘制
            prev_time = fvg['prev_time']
            prev_high = fvg['prev_high']
            gap_high = fvg['gap_high']
            gap_low = fvg['gap_low']

            # 绘制FVG上边界线（绿色实线）- 在FVG上边界价格水平绘制，从前K线最高价位置开始
            fvg_high_line = chart.ray_line(
                start_time=prev_time,
                value=gap_high,  # 在FVG上边界价格水平绘制线条
                color='rgba(0, 128, 0, 0.8)',  # 绿色
                width=1,
                style='solid',
                text=f"看涨FVG上 {gap_high:.2f}"
            )
            swing_fvg_lines_cache[cache_key].append(fvg_high_line)

            # 绘制FVG下边界线（绿色实线）- 在FVG下边界价格水平绘制，从前K线最高价位置开始
            fvg_low_line = chart.ray_line(
                start_time=prev_time,
                value=gap_low,  # 在FVG下边界价格水平绘制线条
                color='rgba(0, 128, 0, 0.8)',  # 绿色，与上边界线相同透明度
                width=1,
                style='solid',  # 改为实线
                text=f"看涨FVG下 {gap_low:.2f}"
            )
            swing_fvg_lines_cache[cache_key].append(fvg_low_line)

        # 绘制看跌FVG区域（红色）
        for fvg in bearish_fvgs:
            # 看跌FVG：线条应该从前K线的最低价开始绘制
            prev_time = fvg['prev_time']
            prev_low = fvg['prev_low']
            gap_high = fvg['gap_high']
            gap_low = fvg['gap_low']

            # 绘制FVG上边界线（红色实线）- 在FVG上边界价格水平绘制，从前K线最低价位置开始
            fvg_high_line = chart.ray_line(
                start_time=prev_time,
                value=gap_high,  # 在FVG上边界价格水平绘制线条
                color='rgba(255, 0, 0, 0.8)',  # 红色
                width=1,
                style='solid',
                text=f"看跌FVG上 {gap_high:.2f}"
            )
            swing_fvg_lines_cache[cache_key].append(fvg_high_line)

            # 绘制FVG下边界线（红色实线）- 在FVG下边界价格水平绘制，从前K线最低价位置开始
            fvg_low_line = chart.ray_line(
                start_time=prev_time,
                value=gap_low,  # 在FVG下边界价格水平绘制线条
                color='rgba(255, 0, 0, 0.8)',  # 红色，与上边界线相同透明度
                width=1,
                style='solid',  # 改为实线
                text=f"看跌FVG下 {gap_low:.2f}"
            )
            swing_fvg_lines_cache[cache_key].append(fvg_low_line)

        print(f"FVG线条绘制完成: {len(bullish_fvgs)}个看涨FVG, {len(bearish_fvgs)}个看跌FVG")

    except Exception as e:
        print(f"绘制FVG线条失败: {e}")

# ============================================================================
# 实时数据更新策略优化说明：
#
# A股实时更新策略：
# 启动时：全量获取各周期K线数据
# - 1分钟、5分钟、15分钟、60分钟、日线：通过pytdx API全量获取历史数据
#
# 实时更新：增量获取
# - 1分钟：只获取最新的1根1分钟K线增量数据（直接从API获取）
# - 其他周期：基于最新的1分钟数据计算并更新最新的一根K线的绘制
#   * 5分钟：基于1分钟数据合成5分钟K线
#   * 15分钟：基于1分钟数据合成15分钟K线
#   * 60分钟：基于1分钟数据合成60分钟K线
#   * 日线：基于1分钟数据合成日K线
#
# 港股实时更新策略（新增）：
# 启动时：全量获取各周期K线数据
# - 1分钟、5分钟、15分钟、60分钟、日线：通过xtquant API全量获取历史数据
#
# 实时更新：多周期独立更新（30秒轮询）
# - 每30秒轮询一次，获取1min、5min、15min、60min四个周期的最新K线数据
# - 更新逻辑：先xtdata.download_history_data，后get_market_data
# - 更新顺序：先下载1min、5min，下载完成之后下载15min和60min
# - 保证最后一根K线在不同周期上也是每30秒更新一次
#
# 优势：
# 1. 减少API调用次数，提高性能
# 2. 确保数据一致性，所有周期都基于同一份1分钟数据（A股）
# 3. 降低网络延迟和服务器压力
# 4. 支持实时更新所有周期的K线图表
# 5. 只有1分钟周期从API获取实时数据，其他周期完全基于1分钟数据计算（A股）
# 6. 实时更新时只更新最新的一根K线，提高效率
# 7. 港股支持多周期独立实时更新，确保数据准确性
# ============================================================================


class DoubleClickableWidget(QWidget):
    """可双击的图表容器Widget"""
    double_clicked = Signal(int)  # 发送图表索引信号

    def __init__(self, chart_index, parent=None):
        super().__init__(parent)
        self.chart_index = chart_index
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)

    def mouseDoubleClickEvent(self, event: QMouseEvent):
        if event.button() == Qt.LeftButton:
            self.double_clicked.emit(self.chart_index)
        super().mouseDoubleClickEvent(event)

    def add_webview(self, webview):
        """添加webview到容器"""
        self.layout.addWidget(webview)


class ChartTab(QWidget):
    """单个标签页的图表组件，包含四分屏图表"""

    def __init__(self, symbol=None, parent=None):
        super().__init__(parent)
        self.symbol = symbol or "00700.HK"  # 默认股票代码
        self.charts = []
        self.chart_containers = []
        self.is_single_view = False
        self.current_single_chart = None

        # 默认时间周期配置：top-left 15min, top-right 1min, bottom-left 60min, bottom-right 5min
        self.default_timeframes = ['15min', '1min', '60min', '5min']
        self.timeframe_options = ('1min', '5min', '15min', '60min', '1day')

        self.setup_ui()
        self.create_charts()

    def setup_ui(self):
        """设置UI布局"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)

        # 创建四分屏网格布局
        self.grid_layout = QGridLayout()
        self.grid_layout.setContentsMargins(0, 0, 0, 0)
        self.grid_layout.setSpacing(1)

        # 创建单图表布局
        self.single_layout = QVBoxLayout()
        self.single_layout.setContentsMargins(0, 0, 0, 0)

        # 默认显示四分屏布局
        self.layout.addLayout(self.grid_layout)

    def create_charts(self):
        """创建四个独立的图表"""
        # 为当前标签页生成唯一标识
        import time
        self.tab_id = f"tab_{int(time.time() * 1000)}"  # 使用时间戳作为唯一标识

        for i in range(4):
            chart = QtChart(self)

            # 为每个图表添加唯一标识
            chart.chart_id = f"{self.tab_id}_chart_{i}"

            # 为每个图表添加独立的控制栏
            chart.topbar.textbox('symbol', self.symbol)
            chart.topbar.switcher('timeframe', self.timeframe_options, default=self.default_timeframes[i],
                                func=lambda c, idx=i: self.on_timeframe_selection_multi(c, idx))

            # 绑定搜索事件
            chart.events.search += lambda c, s, idx=i: self.on_search_multi(c, s, idx)

            # 设置图表大小策略
            webview = chart.get_webview()
            webview.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            # 添加水印
            chart.watermark(str(i + 1))

            # 创建可双击的容器
            container = DoubleClickableWidget(i, self)
            container.add_webview(webview)
            container.double_clicked.connect(lambda chart_idx: self.toggle_chart_view(chart_idx))

            # 将容器添加到网格布局中
            row = i // 2  # 0, 0, 1, 1
            col = i % 2   # 0, 1, 0, 1
            self.grid_layout.addWidget(container, row, col)

            self.charts.append(chart)
            self.chart_containers.append(container)

        # 配置所有图表
        self.configure_charts()

    def configure_charts(self):
        """配置所有图表的样式和设置"""
        for i, chart in enumerate(self.charts):
            try:
                # 基本K线颜色：红涨绿跌
                chart.candle_style(
                    up_color='rgba(255, 82, 82, 100)',
                    down_color='rgba(54, 207, 113, 100)'
                )

                # 确保图表基本配置
                chart.time_scale(
                    right_offset=80,  # 适中的右侧偏移，平衡视觉体验和性能
                    min_bar_spacing=0.2,  # 减小最小K线间距，允许更大范围的缩小
                    visible=True,
                    time_visible=True,
                    seconds_visible=False
                )

                chart.price_scale(
                    auto_scale=True,
                    scale_margin_top=0.1,
                    scale_margin_bottom=0.1
                )

                # 设置当前价格线为白色
                chart.run_script(f'''
                {chart.id}.series.applyOptions({{
                    priceLineColor: '#FFFFFF',
                    lastValueVisible: true,
                    priceLineVisible: true
                }})''')

            except Exception as config_error:
                print(f"图表{i+1}配置失败: {config_error}")

    def toggle_chart_view(self, chart_index):
        """切换图表视图：四分屏 <-> 单图表"""
        if not self.is_single_view:
            # 从四分屏切换到单图表视图
            self.is_single_view = True
            self.current_single_chart = chart_index

            # 移除网格布局
            self.layout.removeItem(self.grid_layout)

            # 从网格布局中移除选中的容器
            selected_container = self.chart_containers[chart_index]
            self.grid_layout.removeWidget(selected_container)

            # 将选中的容器添加到单图表布局
            self.single_layout.addWidget(selected_container)
            self.layout.addLayout(self.single_layout)
        else:
            # 从单图表视图切换回四分屏
            self.is_single_view = False

            # 移除单图表布局
            self.layout.removeItem(self.single_layout)

            # 从单图表布局中移除容器
            selected_container = self.chart_containers[self.current_single_chart]
            self.single_layout.removeWidget(selected_container)

            # 将容器重新添加到网格布局的原位置
            row = self.current_single_chart // 2
            col = self.current_single_chart % 2
            self.grid_layout.addWidget(selected_container, row, col)

            # 重新添加网格布局
            self.layout.addLayout(self.grid_layout)

            self.current_single_chart = None

    def get_current_charts(self):
        """获取当前显示的图表列表"""
        if self.is_single_view and self.current_single_chart is not None:
            return [self.charts[self.current_single_chart]]
        else:
            return self.charts

    def on_search_multi(self, chart, searched_string, chart_index):
        """四分屏模式下的搜索函数 - 先预加载所有数据，再更新UI"""
        print(f"开始搜索股票: {searched_string}")

        # 首先检查股票代码是否有效
        stock_info = parse_stock_code(searched_string, show_warning=True)
        if stock_info.get('market') == -999:  # 无效代码标记
            print(f"股票代码无效，停止搜索: {searched_string}")
            return

        # 更新标签页的股票代码
        old_symbol = self.symbol
        self.symbol = searched_string

        # 更新标签页标题
        self.update_tab_title()

        # 清除错误标记，允许新的搜索
        global current_error_symbol
        current_error_symbol = None

        # 清除旧股票的所有SWING和FVG线条缓存
        if old_symbol and old_symbol != searched_string:
            print(f"DEBUG: 清除旧股票的SWING和FVG线条缓存: {old_symbol}")
            clear_all_swing_fvg_lines_for_symbol(old_symbol)



        # 停止所有现有的搜索和更新线程
        for i, target_chart in enumerate(self.charts):
            # 停止现有的搜索线程（如果存在）
            if hasattr(target_chart, 'search_thread') and target_chart.search_thread.isRunning():
                target_chart.search_thread.terminate()
                target_chart.search_thread.wait(2000)

            # 停止现有的更新线程
            if hasattr(target_chart, 'update_thread'):
                target_chart.update_thread.stop()
                target_chart.update_thread.wait(2000)

            # 更新图表的股票代码输入框
            target_chart.topbar['symbol'].set(searched_string)

        # 获取所有图表的时间周期
        timeframes = [chart.topbar['timeframe'].value for chart in self.charts]
        print(f"需要加载的周期: {timeframes}")

        # 停止现有的预加载线程（如果存在）
        if hasattr(self, 'preload_thread') and self.preload_thread.isRunning():
            self.preload_thread.terminate()
            self.preload_thread.wait(2000)

        # 创建并启动预加载线程
        self.preload_thread = AllDataPreloadThread(searched_string, timeframes)

        # 连接预加载完成信号
        self.preload_thread.all_data_loaded.connect(
            lambda symbol, all_data: self.on_all_data_preloaded(symbol, all_data)
        )

        # 连接预加载失败信号
        self.preload_thread.preload_failed.connect(
            lambda symbol, error: self.on_preload_failed(symbol, error)
        )

        self.preload_thread.start()
        print(f"预加载线程已启动: {searched_string}")



    def on_all_data_preloaded(self, symbol, all_data):
        """处理所有数据预加载完成的回调"""
        try:
            print(f"DEBUG: 所有数据预加载完成: {symbol}, 可用周期: {list(all_data.keys())}")

            # 详细检查预加载的数据
            for timeframe, data in all_data.items():
                if data is not None and len(data) > 0:
                    print(f"DEBUG: {timeframe} 数据详情: {len(data)}条, 列{list(data.columns)}")
                else:
                    print(f"WARNING: {timeframe} 数据异常: {data}")

            # 按顺序更新每个图表
            for i, chart in enumerate(self.charts):
                current_timeframe = chart.topbar['timeframe'].value
                print(f"DEBUG: 处理图表 {i+1}, 周期: {current_timeframe}")

                if current_timeframe in all_data:
                    data = all_data[current_timeframe]
                    print(f"DEBUG: 更新图表 {i+1} ({current_timeframe}): {len(data)}条数据")

                    # 直接调用数据加载处理函数
                    try:
                        self.update_single_chart(chart, symbol, current_timeframe, data)
                    except Exception as chart_error:
                        print(f"ERROR: 单个图表更新失败: {symbol} {current_timeframe} - {chart_error}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"ERROR: 图表 {i+1} ({current_timeframe}) 没有可用数据")
                    print(f"DEBUG: 可用周期: {list(all_data.keys())}")

            print(f"DEBUG: 所有图表更新完成: {symbol}")

            # 清理旧缓存条目
            cleanup_old_cache_entries()

            # 强制垃圾回收
            import gc
            gc.collect()



        except Exception as e:
            print(f"ERROR: 数据预加载回调失败: {symbol} - {e}")
            import traceback
            traceback.print_exc()

    def on_preload_failed(self, symbol, error_message):
        """处理预加载失败的回调"""
        print(f"ERROR: 数据预加载失败: {symbol} - {error_message}")

        # 添加详细的错误分析
        try:
            stock_info = parse_stock_code(symbol, show_warning=False)
            print(f"DEBUG: 股票信息解析结果: {stock_info}")

            if stock_info and stock_info.get('is_hk', False):
                print(f"DEBUG: 港股数据获取失败，检查xtquant连接状态...")
                is_connected, conn_error = check_xtquant_connection()
                print(f"DEBUG: xtquant连接状态: {is_connected}, 错误: {conn_error}")

                # 如果是港股且xtquant连接失败，显示专门的港股错误弹窗
                if not is_connected:
                    self.show_hk_connection_error(symbol, conn_error)
                    return
            else:
                print(f"DEBUG: A股数据获取失败，检查pytdx连接状态...")
                conn_status = ensure_connection()
                print(f"DEBUG: pytdx连接状态: {conn_status}")

        except Exception as debug_error:
            print(f"DEBUG: 错误分析过程中出现异常: {debug_error}")

        # 显示错误对话框
        global current_error_symbol
        if current_error_symbol != symbol:
            current_error_symbol = symbol
            on_search_failed(self.charts[0], symbol, error_message)

    def show_hk_connection_error(self, symbol, conn_error):
        """显示港股连接错误弹窗"""
        try:
            from PySide6.QtWidgets import QMessageBox
            from PySide6.QtCore import QTimer

            # 使用QTimer确保在主线程中显示弹窗
            QTimer.singleShot(0, lambda: self._show_hk_error_dialog(symbol, conn_error))

        except Exception as e:
            print(f"ERROR: 显示港股错误弹窗失败: {e}")

    def _show_hk_error_dialog(self, symbol, conn_error):
        """实际显示港股错误对话框"""
        try:
            from PySide6.QtWidgets import QMessageBox

            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setWindowTitle("港股数据获取失败")
            msg_box.setText(f"无法获取港股数据: {symbol}")

            # 根据错误类型提供不同的详细信息
            if "未启动" in conn_error or "未连接" in conn_error:
                detailed_info = f"{conn_error}\n\n解决方法：\n" \
                               f"1. 启动xtquant客户端软件\n" \
                               f"2. 确保xtquant正常登录\n" \
                               f"3. 重新尝试加载港股数据\n\n" \
                               f"注意：港股数据需要xtquant软件支持"
            elif "未安装" in conn_error:
                detailed_info = f"{conn_error}\n\n解决方法：\n" \
                               f"1. 安装xtquant软件包：pip install xtquant\n" \
                               f"2. 下载并安装xtquant客户端\n" \
                               f"3. 重新启动程序\n\n" \
                               f"或者：切换到A股代码进行分析"
            else:
                detailed_info = f"{conn_error}\n\n请检查：\n" \
                               f"• xtquant软件是否正常运行\n" \
                               f"• 网络连接是否正常\n" \
                               f"• 港股代码是否正确\n\n" \
                               f"建议：可以尝试A股代码进行测试"

            msg_box.setInformativeText(detailed_info)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.setDefaultButton(QMessageBox.Ok)

            # 显示对话框
            msg_box.exec()

        except Exception as e:
            print(f"ERROR: 港股错误对话框显示失败: {e}")

    def update_single_chart(self, chart, symbol, timeframe, data):
        """更新单个图表的数据"""
        try:
            # 详细数据验证和调试信息
            print(f"DEBUG: 开始更新图表 {symbol} {timeframe}")

            if data is None:
                print(f"ERROR: {symbol} {timeframe} 数据为None")
                return

            if len(data) == 0:
                print(f"ERROR: {symbol} {timeframe} 数据为空DataFrame")
                return

            # 检查数据格式
            required_columns = ['time', 'open', 'high', 'low', 'close']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                print(f"ERROR: {symbol} {timeframe} 缺少必需列: {missing_columns}")
                print(f"DEBUG: 实际列名: {list(data.columns)}")
                return

            # 简化的数据检查（删除频繁的调试输出）
            # print(f"DEBUG: {symbol} {timeframe} - {len(data)}条数据，时间范围: {data['time'].iloc[0]} 到 {data['time'].iloc[-1]}")

            # 设置K线数据（增加重试机制）
            success = False
            for attempt in range(3):
                try:
                    print(f"DEBUG: 尝试设置数据 (第{attempt+1}次)...")
                    chart.set(data)
                    print(f"DEBUG: chart.set() 调用成功")

                    chart.fit()
                    print(f"DEBUG: chart.fit() 调用成功")

                    success = True
                    print(f"SUCCESS: 图表数据设置成功: {symbol} {timeframe} (尝试{attempt+1})")
                    break
                except Exception as set_error:
                    print(f"ERROR: 图表数据设置失败 (尝试{attempt+1}): {set_error}")

                    if attempt < 2:
                        import time
                        time.sleep(0.1 * (attempt + 1))

            if not success:
                print(f"FATAL: 所有尝试都失败: {symbol} {timeframe}")
                print(f"DEBUG: 最终数据状态: {data.shape if data is not None else 'None'}")
                return
            else:
                print(f"DEBUG: 图表更新完成: {symbol} {timeframe}")

            # 对15分钟和60分钟图表应用SWING和FVG支撑阻力分析
            if timeframe in ['15min', '60min']:
                print(f"DEBUG: 开始对{timeframe}图表应用SWING和FVG分析: {symbol}")
                try:
                    apply_swing_fvg_analysis_to_15min_chart(chart, symbol, data)
                except Exception as analysis_error:
                    print(f"ERROR: {timeframe}SWING和FVG分析失败: {symbol} - {analysis_error}")
                    import traceback
                    traceback.print_exc()

            # 启动实时数据更新
            start_update_thread(chart, symbol)

        except Exception as e:
            print(f"单个图表更新失败: {symbol} {timeframe} - {e}")

    def on_timeframe_selection_multi(self, chart, chart_index):
        """四分屏模式下的时间周期选择函数"""
        timeframe = chart.topbar['timeframe'].value
        symbol = chart.topbar['symbol'].value

        # 调用原有的时间周期选择逻辑
        on_timeframe_selection(chart)

    def initialize_charts(self):
        """初始化图表数据"""
        try:
            print(f"开始初始化标签页数据: {self.symbol}, 图表数量: {len(self.charts)}")

            # 确保所有图表都已创建
            if not self.charts or len(self.charts) != 4:
                print(f"警告: 图表未完全创建，当前数量: {len(self.charts) if self.charts else 0}")
                return

            # 使用与添加新tab相同的初始化逻辑
            self.on_search_multi(self.charts[0], self.symbol, 0)
            print(f"标签页初始化完成: {self.symbol}")
        except Exception as e:
            print(f"标签页初始化失败: {e}")
            import traceback
            traceback.print_exc()



    def update_tab_title(self):
        """更新标签页标题"""
        # 查找当前标签页在父级TabManager中的索引
        parent_widget = self.parent()
        while parent_widget:
            if isinstance(parent_widget, TabManager):
                tab_widget = parent_widget.tab_widget
                index = tab_widget.indexOf(self)
                if index >= 0:
                    tab_widget.setTabText(index, self.symbol)
                break
            parent_widget = parent_widget.parent()

    def cleanup(self):
        """清理资源"""
        print(f"DEBUG: 开始清理标签页资源: {self.symbol}")

        # 清理预加载线程
        if hasattr(self, 'preload_thread') and self.preload_thread.isRunning():
            print(f"DEBUG: 停止预加载线程: {self.symbol}")
            self.preload_thread.terminate()
            if not self.preload_thread.wait(3000):  # 等待3秒
                print(f"WARNING: 预加载线程强制终止: {self.symbol}")

        for i, chart in enumerate(self.charts):
            # 清理更新线程
            if hasattr(chart, 'update_thread'):
                print(f"DEBUG: 停止图表{i+1}更新线程: {self.symbol}")
                chart.update_thread.stop()
                if not chart.update_thread.wait(3000):  # 等待3秒
                    print(f"WARNING: 图表{i+1}更新线程强制终止: {self.symbol}")

            # 清理搜索线程
            if hasattr(chart, 'search_thread') and chart.search_thread.isRunning():
                print(f"DEBUG: 停止图表{i+1}搜索线程: {self.symbol}")
                chart.search_thread.terminate()
                if not chart.search_thread.wait(3000):  # 等待3秒
                    print(f"WARNING: 图表{i+1}搜索线程强制终止: {self.symbol}")

        print(f"DEBUG: 标签页资源清理完成: {self.symbol}")


class TabManager(QWidget):
    """标签页管理器"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.default_symbol = "00700.HK"  # 默认股票代码
        self.tab_counter = 1  # 标签页计数器

        self.setup_ui()
        # 不在构造函数中创建第一个标签页，等待外部调用

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)  # 允许关闭标签页
        self.tab_widget.tabCloseRequested.connect(self.close_tab)
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # 创建添加按钮
        self.add_button = QPushButton("+")
        self.add_button.setFixedSize(30, 25)
        self.add_button.clicked.connect(self.add_new_tab)

        # 将添加按钮设置为标签栏的角落控件（右侧）
        self.tab_widget.setCornerWidget(self.add_button, Qt.TopRightCorner)

        layout.addWidget(self.tab_widget)

    def create_first_tab(self):
        """创建第一个标签页 - 使用与添加新tab完全相同的逻辑"""
        print("开始创建第一个标签页...")
        self.add_new_tab()
        print("第一个标签页创建完成")

    def add_new_tab(self):
        """添加新标签页"""
        print(f"开始创建标签页 #{self.tab_counter}")

        # 创建新的图表标签页
        chart_tab = ChartTab(self.default_symbol, self)

        # 添加到标签页控件
        tab_title = f"{self.default_symbol}"
        index = self.tab_widget.addTab(chart_tab, tab_title)

        # 切换到新标签页
        self.tab_widget.setCurrentIndex(index)

        # 延迟初始化图表数据 - 增加延迟时间确保UI完全就绪
        QTimer.singleShot(300, chart_tab.initialize_charts)



        self.tab_counter += 1

        print(f"标签页创建完成: {tab_title}, 索引: {index}")

    def close_tab(self, index):
        """关闭标签页"""
        if self.tab_widget.count() <= 1:
            # 至少保留一个标签页
            return

        # 获取要关闭的标签页
        chart_tab = self.tab_widget.widget(index)

        # 清理资源
        if chart_tab:
            chart_tab.cleanup()

        # 移除标签页
        self.tab_widget.removeTab(index)

        print(f"关闭标签页: {index}")

    def get_current_tab(self):
        """获取当前活动的标签页"""
        return self.tab_widget.currentWidget()

    def get_current_charts(self):
        """获取当前标签页的图表列表"""
        current_tab = self.get_current_tab()
        if current_tab:
            return current_tab.get_current_charts()
        return []

    def cleanup_all(self):
        """清理所有标签页资源"""
        print(f"DEBUG: 开始清理所有标签页资源，共{self.tab_widget.count()}个标签页")

        for i in range(self.tab_widget.count()):
            chart_tab = self.tab_widget.widget(i)
            if chart_tab:
                print(f"DEBUG: 清理第{i+1}个标签页")
                chart_tab.cleanup()

        print(f"DEBUG: 所有标签页资源清理完成")

    def on_tab_changed(self, index):
        """标签页切换时的回调函数"""
        try:
            # 标签页切换处理逻辑
            pass
        except Exception as e:
            pass


app = QApplication([])
window = QMainWindow()

# 创建主布局
main_widget = QWidget()
main_layout = QHBoxLayout()
main_widget.setLayout(main_layout)

# 创建左侧按钮面板
button_panel = QWidget()
button_layout = QVBoxLayout()
button_panel.setLayout(button_layout)
button_panel.setFixedWidth(120)  # 固定宽度

# 创建右侧内容区域（使用QStackedWidget）
stacked_widget = QStackedWidget()

# 创建多标签页K线图页面
tab_manager = TabManager()

# 为了保持兼容性，创建全局变量引用
charts = []  # 这将在需要时从当前标签页获取
default_symbol = "00700.HK"  # 默认A股，也可以使用港股如 "09988.HK"


def get_current_charts():
    """获取当前显示的图表列表"""
    return tab_manager.get_current_charts()

#####################################################################
# 以下新增函数用于获取历史数据，不同 timeframe 调用不同的逻辑
#####################################################################

# 常量定义：不同时间周期的数据获取范围（便于统一调整）
# A股数据根数配置
MINUTE_CYCLE_COUNT = 480
FIVE_MINUTE_CYCLE_COUNT = 480
FIFTEEN_MINUTE_CYCLE_COUNT = 3022  # 更新为3022根15分钟K线，支持更长历史数据回溯
SIXTY_MINUTE_CYCLE_COUNT = 800
DAILY_CYCLE_COUNT = 150

# 港股数据根数配置
HK_MINUTE_CYCLE_COUNT = 680
HK_FIVE_MINUTE_CYCLE_COUNT = 340      # 5天
HK_FIFTEEN_MINUTE_CYCLE_COUNT = 3022  # 200天
HK_SIXTY_MINUTE_CYCLE_COUNT = 1134    # 200天
HK_DAILY_CYCLE_COUNT = 200

# API单次请求限制
MAX_BARS_PER_REQUEST = 800  # pytdx API单次最大请求数量

# PyTdx API period参数正确映射（重新修复）
# A股标准行情API参数:
# period=7: 1分钟K线
# period=0: 5分钟K线
# period=1: 15分钟K线
# period=3: 60分钟K线
# period=9: 日线K线



# 全局缓存，用于存储已获取的历史数据，格式为： { "symbol_timeframe": DataFrame }
history_cache = {}



# 缓存清理机制（移除大小限制）
def cleanup_old_cache_entries():
    """清理旧的缓存条目，防止内存泄漏"""
    try:
        # 不限制缓存大小，只进行垃圾回收
        import gc
        collected = gc.collect()
        if collected > 0:
            print(f"DEBUG: 垃圾回收清理了 {collected} 个对象")

    except Exception as e:
        print(f"ERROR: 缓存清理失败: {e}")

# 错误对话框显示状态，避免重复显示
current_error_symbol = None

# 港股xtquant数据缓存
hk_kline_cache = {}  # {stock: {timeframe: DataFrame}}
hk_last_update_times = {}  # {stock: {timeframe: last_time}}

# 港股历史数据下载记录
hk_download_history = {}  # {stock_code: {period: last_download_time}} 记录下载时间避免重复下载

DEBUG_DATA_COMPARISON = False  # 禁用数据格式对比调试（减少输出）

# 清除所有缓存，确保使用最新的数据

# 清除所有缓存
history_cache.clear()

# 程序启动时的连接将在函数定义后进行

# 初始化pytdx API - A股标准行情API
try:
    api = TdxHq_API(
        multithread=True,      # 启用多线程支持
        heartbeat=True,        # 启用心跳包
        auto_retry=True,       # 启用自动重试
        raise_exception=False  # 不抛出异常，返回None
    )
except Exception as e:
    api = TdxHq_API()



is_connected = False
last_connect_time = 0
stock_codes = {}  # 股票代码缓存


def connect_tdx():
    """连接通达信服务器"""
    global api, is_connected, last_connect_time
    try:
        # 优先使用测试过的可用服务器
        servers = [
            ('180.153.18.170', 7709),  # 上海电信主站Z1 (最快)
            ('180.153.18.172', 80),    # 上海电信主站Z80 (最快)
            ('218.6.170.47', 7709),    # 上证云成都电信一
            ('123.125.108.14', 7709),  # 上证云北京联通一
        ]

        # 添加更多备用服务器
        backup_servers = [
            ('*************', 7709),   # 长城国瑞电信1
            ('*************', 7709),   # 长城国瑞电信2
            ('*************', 7709),   # 长城国瑞网通
            ('**************', 7709),  # 上海电信主站Z2
            ('***************', 7709), # 北京联通主站Z1
            ('***************', 7709), # 北京联通主站Z2
        ]
        servers.extend(backup_servers)


        for i, (host, port) in enumerate(servers):
            try:

                # 设置连接超时（增加到10秒）
                if api.connect(host, port, time_out=2):
                    is_connected = True

                    # 测试连接是否正常工作
                    try:
                        test_data = api.get_security_count(0)  # 测试获取深圳市场股票数量
                        if test_data and test_data > 0:
                            last_connect_time = time.time()
                            return True
                        else:
                            api.disconnect()
                            is_connected = False
                    except Exception as test_e:
                        api.disconnect()
                        is_connected = False

            except Exception as e:
                continue

        return False
    except Exception as e:
        return False





def ensure_connection():
    """确保连接可用，如果断开则重连"""
    global api, is_connected, last_connect_time
    current_time = time.time()

    # 如果未连接或距离上次连接超过30分钟，尝试重连（减少频繁重连）
    if not is_connected or (current_time - last_connect_time) > 1800:

        # 测试当前连接
        if is_connected:
            try:
                test_result = api.get_security_count(0)
                if test_result and test_result > 0:
                    last_connect_time = current_time
                    return True
                else:
                    is_connected = False
            except Exception as e:
                is_connected = False

        # 重新连接
        if connect_tdx():
            last_connect_time = current_time
            return True
        else:
            return False

    return True


def is_likely_hk_code(symbol):
    """
    判断是否可能是港股代码（更严格的判断）
    """
    # 移除可能的前缀
    clean = symbol.upper()
    if ':' in clean:
        clean = clean.split(':')[-1]
    if '.' in clean:
        clean = clean.split('.')[0]

    # 检查是否为数字
    if not clean.isdigit():
        return False

    # 港股代码判断 - 严格要求5位数字
    length = len(clean)

    # 只有5位数字才是港股代码
    if length == 5:
        return True

    # 其他长度都不是港股代码
    return False


def is_valid_stock_code(symbol):
    """
    检查股票代码是否有效（A股或港股格式）
    """
    # 移除可能的后缀
    clean = symbol.upper()
    if '.' in clean:
        clean = clean.split('.')[0]

    # 检查是否为纯数字
    if not clean.isdigit():
        return False

    length = len(clean)

    # A股代码格式检查
    if length == 6:
        # 6位数字，检查是否符合A股规则
        if clean.startswith('6'):  # 上海A股
            return True
        elif clean.startswith(('0', '3')):  # 深圳A股、创业板
            return True
        elif clean.startswith(('4', '8')):  # 北交所
            return True
        else:
            return False

    # 港股代码格式检查 - 严格要求5位数字
    elif length == 5:
        # 只有5位数字才是有效的港股代码
        return True

    # 其他长度的数字代码都认为无效
    else:
        return False


def parse_stock_code(symbol, show_warning=True):
    """
    解析股票代码，自动判断市场，支持A股和港股
    自动为5位数字代码补全.HK后缀
    返回: {'code': 股票代码, 'market': 市场代码, 'name': 股票名称, 'exchange': 交易所, 'is_hk': 是否港股}
    如果代码无效且show_warning=True，会显示警告弹窗
    """
    global stock_codes

    # 移除可能的前缀和后缀
    clean_symbol = symbol.upper()
    original_symbol = clean_symbol  # 保存原始输入

    if ':' in clean_symbol:
        clean_symbol = clean_symbol.split(':')[-1]

    # 首先检查代码是否有效
    if not is_valid_stock_code(clean_symbol):
        if show_warning:
            QTimer.singleShot(0, lambda: show_invalid_stock_code_warning(symbol))
        # 返回一个默认结果，避免程序崩溃
        return {
            'code': symbol,
            'market': -999,  # 特殊标记表示无效代码
            'name': f"无效代码 {symbol}",
            'exchange': 'INVALID',
            'is_hk': False
        }

    # 检查是否为港股
    is_hk_stock = False
    result = None  # 初始化result变量

    if '.HK' in clean_symbol:
        # 明确的港股代码格式：如 09988.HK
        # print(f"DEBUG: 识别为明确港股代码 (.HK后缀)")
        is_hk_stock = True
        clean_symbol = clean_symbol.replace('.HK', '')
        # 港股代码通常是5位数字，前面补0
        if len(clean_symbol) < 5:
            clean_symbol = clean_symbol.zfill(5)

        result = {
            'code': f"{clean_symbol}.HK",  # 保持.HK后缀用于xtquant
            'market': -1,  # 港股使用特殊标识
            'name': f"港股 {clean_symbol}",
            'exchange': 'HKEX',
            'is_hk': True
        }
    elif is_likely_hk_code(clean_symbol):
        # 自动识别并补全港股代码
        # print(f"DEBUG: 可能是港股代码，进行进一步检查")
        if '.' in clean_symbol:
            clean_symbol = clean_symbol.split('.')[0]

        # 补全为5位数字
        if clean_symbol.isdigit() and 1 <= len(clean_symbol) <= 5:
            # print(f"DEBUG: 确认为港股代码，补全为5位数字")
            clean_symbol = clean_symbol.zfill(5)
            is_hk_stock = True

            result = {
                'code': f"{clean_symbol}.HK",  # 自动补全.HK后缀
                'market': -1,  # 港股使用特殊标识
                'name': f"港股 {clean_symbol}",
                'exchange': 'HKEX',
                'is_hk': True
            }
        else:
            # 不是有效的港股代码，按A股处理
            # print(f"DEBUG: 不是有效的港股代码，按A股处理")
            is_hk_stock = False
    else:
        # print(f"DEBUG: 不是港股代码，按A股处理")
        is_hk_stock = False

    if not is_hk_stock:
        # A股处理逻辑
        # print(f"DEBUG: 处理A股代码")
        if '.' in clean_symbol:
            clean_symbol = clean_symbol.split('.')[0]

        # 判断A股市场
        if clean_symbol.startswith('6'):
            # 上海市场：6开头
            market = 1
            exchange = 'SSE'
            stock_name = f"沪股 {clean_symbol}"
            # print(f"DEBUG: 识别为上海市场")
        elif clean_symbol.startswith(('0', '3')):
            # 深圳市场：0开头（主板、中小板）、3开头（创业板）
            market = 0
            exchange = 'SZSE'
            stock_name = f"深股 {clean_symbol}"
            # print(f"DEBUG: 识别为深圳市场")
        elif clean_symbol.startswith('8') or clean_symbol.startswith('4'):
            # 北交所：8开头（精选层）、4开头（创新层）
            market = 0  # 暂时归类到深圳市场
            exchange = 'BSE'
            stock_name = f"北交所 {clean_symbol}"
            # print(f"DEBUG: 识别为北交所")
        else:
            # 默认深圳市场
            market = 0
            exchange = 'SZSE'
            stock_name = f"深股 {clean_symbol}"
            # print(f"DEBUG: 默认为深圳市场")

        result = {
            'code': clean_symbol,
            'market': market,
            'name': stock_name,
            'exchange': exchange,
            'is_hk': False
        }

    # 验证result是否正确创建
    if result is None:
        print(f"ERROR: 股票代码解析失败，result为None: {symbol}")
        # 创建一个默认的A股结果
        result = {
            'code': clean_symbol,
            'market': 0,
            'name': f"未知 {clean_symbol}",
            'exchange': 'UNKNOWN',
            'is_hk': False
        }

    # print(f"DEBUG: 解析结果: {result}")

    # 缓存结果
    stock_codes[symbol] = result

    return result


def fetch_data_in_batches(symbol, period, total_count, time_format='%Y-%m-%d %H:%M', show_warning=True):
    """
    分批获取数据的通用函数

    Args:
        symbol: 股票代码
        period: pytdx period参数 (需要根据实际测试确定正确的对应关系)
        total_count: 总共需要获取的数据量
        time_format: 时间格式化字符串

    Returns:
        DataFrame: 合并后的数据
    """
    global api, stock_codes

    try:
        # 获取或解析股票信息
        if symbol not in stock_codes:
            stock_info = parse_stock_code(symbol, show_warning=False)
            if not stock_info:
                return pd.DataFrame()
        else:
            stock_info = stock_codes[symbol]

        market = stock_info['market']
        clean_symbol = stock_info['code']

        # 使用标准行情API
        if not ensure_connection():
            return pd.DataFrame()
        current_api = api

        # 计算需要分几批获取
        batches = (total_count + MAX_BARS_PER_REQUEST - 1) // MAX_BARS_PER_REQUEST

        all_bars = []
        filtered_count = 0  # 统计被过滤的数据量

        for batch_idx in range(batches):
            start_pos = batch_idx * MAX_BARS_PER_REQUEST
            count = min(MAX_BARS_PER_REQUEST, total_count - start_pos)


            # 获取这一批数据
            data = current_api.get_security_bars(period, market, clean_symbol, start_pos, count)

            if not data:
                continue




            # 处理这一批数据
            for i, bar in enumerate(data):
                try:
                    # 检查必需字段
                    required_fields = ['datetime', 'open', 'high', 'low', 'close', 'vol']
                    missing_fields = [field for field in required_fields if field not in bar]
                    if missing_fields:
                        continue

                    datetime_value = bar['datetime']
                    if isinstance(datetime_value, str):
                        dt = datetime.strptime(datetime_value, '%Y-%m-%d %H:%M')
                        time_str = dt.strftime(time_format)
                    elif hasattr(datetime_value, 'strftime'):
                        dt = datetime_value
                        time_str = datetime_value.strftime(time_format)
                    else:
                        time_str = str(datetime_value)
                        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M')

                    # 检查是否在交易时间内
                    if time_format == '%Y-%m-%d %H:%M':  # 分钟级数据
                        # A股交易时间验证
                        if not is_trading_time(dt):
                            # 调试：记录被过滤的非交易时间数据
                            filtered_count += 1
                            continue  # 跳过非交易时间的数据

                    bar_data = {
                        'time': time_str,
                        'open': float(bar['open']),
                        'high': float(bar['high']),
                        'low': float(bar['low']),
                        'close': float(bar['close']),
                        'volume': int(bar['vol'])
                    }
                    all_bars.append(bar_data)
                except Exception as e:
                    continue

        df = pd.DataFrame(all_bars)
        if len(df) > 0:
            # 按时间排序并去重
            df_before_dedup = len(df)
            df = df.drop_duplicates(subset=['time']).sort_values('time').reset_index(drop=True)

            # 限制到请求的数据量
            if len(df) > total_count:
                df = df.tail(total_count).reset_index(drop=True)

            first_time = df.iloc[0]['time']
            last_time = df.iloc[-1]['time']

            # 调试：对比pytdx数据格式
            # 根据period推断时间周期用于调试
            period_map = {7: '1min', 1: '15min', 3: '60min', 9: '1day', 0: '5min'}
            timeframe = period_map.get(period, f'period_{period}')
            debug_data_format_comparison(symbol, timeframe, df, 'pytdx')
        else:
            # 数据为空，可能是股票代码不存在
            print(f"A股数据为空: {symbol}")
            if show_warning:
                QTimer.singleShot(0, lambda: show_invalid_stock_code_warning(symbol))

        return df

    except Exception as e:
        return pd.DataFrame()


def fetch_minute_data(symbol, show_warning=True):
    """获取1分钟级历史数据，支持A股和港股，返回一个按分钟排列的 DataFrame（共 MINUTE_CYCLE_COUNT 个周期）"""
    global api, stock_codes

    try:
        # 获取或解析股票信息
        if symbol not in stock_codes:
            stock_info = parse_stock_code(symbol, show_warning=False)
            if not stock_info:
                return pd.DataFrame()
        else:
            stock_info = stock_codes[symbol]

        # 检查是否为港股，如果是则使用港股数据获取
        if stock_info.get('is_hk', False):
            return fetch_hk_minute_data(symbol, show_warning)

        market = stock_info['market']
        clean_symbol = stock_info['code']

        # 使用标准行情API
        if not ensure_connection():
            return pd.DataFrame()
        current_api = api
        period = 7  # A股1分钟K线period=7

        # 获取1分钟K线数据
        data = current_api.get_security_bars(period, market, clean_symbol, 0, MINUTE_CYCLE_COUNT)

        if not data:
            print(f"A股1分钟数据为空: {symbol}")
            if show_warning:
                QTimer.singleShot(0, lambda: show_invalid_stock_code_warning(symbol))
            return pd.DataFrame()

        # 转换为DataFrame格式
        bars = []
        for i, bar in enumerate(data):
            try:
                # 检查必需字段
                required_fields = ['datetime', 'open', 'high', 'low', 'close', 'vol']
                missing_fields = [field for field in required_fields if field not in bar]
                if missing_fields:
                    continue

                datetime_value = bar['datetime']
                if isinstance(datetime_value, str):
                    # pytdx返回格式: '2025-06-18 14:58' 或 '2025-06-18 15:00'
                    dt = datetime.strptime(datetime_value, '%Y-%m-%d %H:%M')
                    time_str = dt.strftime('%Y-%m-%d %H:%M')
                elif hasattr(datetime_value, 'strftime'):
                    dt = datetime_value
                    time_str = datetime_value.strftime('%Y-%m-%d %H:%M')
                else:
                    time_str = str(datetime_value)
                    dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M')

                # 检查是否在交易时间内
                if not is_trading_time(dt):
                    continue  # 跳过非交易时间的数据

                bar_data = {
                    'time': time_str,
                    'open': float(bar['open']),
                    'high': float(bar['high']),
                    'low': float(bar['low']),
                    'close': float(bar['close']),
                    'volume': int(bar['vol'])
                }
                bars.append(bar_data)
            except Exception as e:
                continue

        df = pd.DataFrame(bars)
        return df

    except Exception as e:
        return pd.DataFrame()


def fetch_5min_data(symbol, show_warning=True):
    """获取5分钟级历史数据，支持A股和港股，返回一个按5分钟排列的 DataFrame（共 FIVE_MINUTE_CYCLE_COUNT 个周期）"""
    global api, stock_codes

    try:
        # 获取或解析股票信息
        if symbol not in stock_codes:
            stock_info = parse_stock_code(symbol, show_warning=False)
            if not stock_info:
                return pd.DataFrame()
        else:
            stock_info = stock_codes[symbol]

        # 检查是否为港股，如果是则使用港股数据获取
        if stock_info.get('is_hk', False):
            return fetch_hk_5min_data(symbol, show_warning)

        market = stock_info['market']
        clean_symbol = stock_info['code']

        # 使用标准行情API
        if not ensure_connection():
            return pd.DataFrame()
        current_api = api
        period = 0  # A股5分钟K线period=0

        # 获取5分钟K线数据
        data = current_api.get_security_bars(period, market, clean_symbol, 0, FIVE_MINUTE_CYCLE_COUNT)

        if not data:
            print(f"A股5分钟数据为空: {symbol}")
            if show_warning:
                QTimer.singleShot(0, lambda: show_invalid_stock_code_warning(symbol))
            return pd.DataFrame()

        # 转换为DataFrame格式
        bars = []
        for i, bar in enumerate(data):
            try:
                # 检查必需字段
                required_fields = ['datetime', 'open', 'high', 'low', 'close', 'vol']
                missing_fields = [field for field in required_fields if field not in bar]
                if missing_fields:
                    continue

                datetime_value = bar['datetime']
                if isinstance(datetime_value, str):
                    # pytdx返回格式: '2025-06-18 14:55' 或 '2025-06-18 15:00'
                    dt = datetime.strptime(datetime_value, '%Y-%m-%d %H:%M')
                    time_str = dt.strftime('%Y-%m-%d %H:%M')
                elif hasattr(datetime_value, 'strftime'):
                    dt = datetime_value
                    time_str = datetime_value.strftime('%Y-%m-%d %H:%M')
                else:
                    time_str = str(datetime_value)
                    dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M')

                # 检查是否在交易时间内
                if not is_trading_time(dt):
                    continue  # 跳过非交易时间的数据

                    # 检查是否是有效的5分钟边界（仅对A股数据）
                    if not is_valid_5min_boundary(dt):
                        continue  # 跳过无效的5分钟边界时间

                bar_data = {
                    'time': time_str,
                    'open': float(bar['open']),
                    'high': float(bar['high']),
                    'low': float(bar['low']),
                    'close': float(bar['close']),
                    'volume': int(bar['vol'])
                }
                bars.append(bar_data)
            except Exception as e:
                continue

        df = pd.DataFrame(bars)
        if len(df) > 0:
            # 调试：打印前几条数据的时间范围
            first_time = df.iloc[0]['time']
            last_time = df.iloc[-1]['time']

        return df

    except Exception as e:
        return pd.DataFrame()


def fetch_15min_data(symbol, show_warning=True):
    """获取15分钟级历史数据，支持A股和港股，直接使用pytdx的15分钟数据接口"""

    # 根据股票类型选择正确的period参数
    global stock_codes
    if symbol not in stock_codes:
        stock_info = parse_stock_code(symbol, show_warning=False)
        if not stock_info:
            return pd.DataFrame()
    else:
        stock_info = stock_codes[symbol]

    # 检查是否为港股，如果是则使用港股数据获取
    if stock_info.get('is_hk', False):
        return fetch_hk_15min_data(symbol, show_warning)

    # A股使用标准行情API参数
    period = 1  # A股15分钟K线period=1

    # 获取15分钟K线数据
    df = fetch_data_in_batches(symbol, period, FIFTEEN_MINUTE_CYCLE_COUNT, '%Y-%m-%d %H:%M', show_warning)

    # 强制限制到指定数量，确保不超过设定值
    if len(df) > FIFTEEN_MINUTE_CYCLE_COUNT:
        df = df.tail(FIFTEEN_MINUTE_CYCLE_COUNT).reset_index(drop=True)


    return df




def fetch_60min_data(symbol, show_warning=True):
    """获取60分钟数据"""
    # 检查是否为港股
    stock_info = parse_stock_code(symbol, show_warning=False)
    if stock_info and stock_info.get('is_hk', False):
        return fetch_hk_60min_data(symbol, show_warning)

    return fetch_data_in_batches(symbol, 3, SIXTY_MINUTE_CYCLE_COUNT, '%Y-%m-%d %H:%M', show_warning)





def fetch_daily_data(symbol, show_warning=True):
    """获取日线数据"""
    # 检查是否为港股
    stock_info = parse_stock_code(symbol, show_warning=False)
    if stock_info and stock_info.get('is_hk', False):
        return fetch_hk_daily_data(symbol, show_warning)

    return fetch_data_in_batches(symbol, 9, DAILY_CYCLE_COUNT, '%Y-%m-%d', show_warning)


# ============================================================================
# 港股数据获取函数（使用xtquant API）
# ============================================================================










def check_xtquant_connection():
    """
    检查xtquant软件连接状态
    返回: (is_connected, error_message)
    """
    global XTQUANT_CONNECTED, XTQUANT_LAST_CHECK

    if not XTQUANT_AVAILABLE:
        return False, "xtquant未安装，请先安装: pip install xtquant"

    current_time = time.time()
    # 每60秒检查一次连接状态，避免频繁检查
    if current_time - XTQUANT_LAST_CHECK < 60 and XTQUANT_CONNECTED:
        return True, ""

    try:
        # 尝试获取一个简单的tick数据来测试连接
        test_data = xtdata.get_full_tick(['09988.HK'])

        if test_data and '09988.HK' in test_data:
            XTQUANT_CONNECTED = True
            XTQUANT_LAST_CHECK = current_time
            return True, ""
        else:
            XTQUANT_CONNECTED = False
            return False, "xtquant软件未启动或连接失败"

    except Exception as e:
        XTQUANT_CONNECTED = False
        error_msg = str(e)

        # 根据错误类型提供更友好的提示
        if "连接" in error_msg or "connection" in error_msg.lower():
            return False, "xtquant软件未启动，请先启动xtquant客户端"
        elif "登录" in error_msg or "login" in error_msg.lower():
            return False, "xtquant软件未登录，请先登录xtquant客户端"
        elif "网络" in error_msg or "network" in error_msg.lower():
            return False, "网络连接异常，请检查网络设置"
        else:
            return False, f"xtquant连接异常: {error_msg}"


def show_xtquant_warning(parent, error_message):
    """
    显示xtquant警告对话框
    """
    msg_box = QMessageBox(parent)
    msg_box.setIcon(QMessageBox.Warning)
    msg_box.setWindowTitle("港股数据获取失败")
    msg_box.setText("无法获取港股数据")
    msg_box.setInformativeText(error_message)

    # 根据错误类型提供不同的详细信息
    if "未安装" in error_message:
        msg_box.setDetailedText(
            "解决方案：\n"
            "1. 打开命令行（CMD或终端）\n"
            "2. 执行命令：pip install xtquant\n"
            "3. 重启程序"
        )
    elif "未启动" in error_message:
        msg_box.setDetailedText(
            "解决方案：\n"
            "1. 启动xtquant客户端软件\n"
            "2. 确保软件正常运行\n"
            "3. 重新尝试获取港股数据"
        )
    elif "未登录" in error_message:
        msg_box.setDetailedText(
            "解决方案：\n"
            "1. 在xtquant客户端中登录账户\n"
            "2. 确保登录状态正常\n"
            "3. 重新尝试获取港股数据"
        )
    else:
        msg_box.setDetailedText(
            "解决方案：\n"
            "1. 检查xtquant客户端是否正常运行\n"
            "2. 检查网络连接\n"
            "3. 重启xtquant客户端\n"
            "4. 如问题持续，请联系技术支持"
        )

    msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Retry)
    msg_box.setDefaultButton(QMessageBox.Retry)

    return msg_box.exec_()


def show_invalid_stock_code_warning(stock_code):
    """
    显示无效股票代码警告对话框
    """
    try:
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle("股票代码错误")
        msg_box.setText(f"股票代码不存在: {stock_code}")

        # 根据代码格式提供不同的建议
        if stock_code.endswith('.HK'):
            # 港股代码
            detailed_info = f"港股代码 {stock_code} 不存在或已停牌\n\n" \
                           f"请检查：\n" \
                           f"• 代码拼写是否正确\n" \
                           f"• 股票是否正常交易\n" \
                           f"• 是否为有效的港股代码\n\n" \
                           f"港股代码示例（5位数字格式）：\n" \
                           f"• 腾讯控股：00700.HK\n" \
                           f"• 阿里巴巴：09988.HK\n" \
                           f"• 美团：03690.HK"
        else:
            # 其他代码
            detailed_info = f"股票代码 {stock_code} 格式不正确或不存在\n\n" \
                           f"请使用正确的股票代码格式：\n\n" \
                           f"A股代码示例：\n" \
                           f"• 上海：600000（浦发银行）\n" \
                           f"• 深圳：000001（平安银行）\n" \
                           f"• 创业板：300001（特锐德）\n\n" \
                           f"港股代码示例（必须输入5位数字）：\n" \
                           f"• 腾讯：00700（必须输入完整5位）\n" \
                           f"• 阿里：09988（必须输入完整5位）\n" \
                           f"• 美团：03690（必须输入完整5位）"

        msg_box.setInformativeText(detailed_info)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.setDefaultButton(QMessageBox.Ok)
        msg_box.exec()

    except Exception as e:
        print(f"ERROR: 显示股票代码错误弹窗失败: {e}")


def debug_data_format_comparison(symbol, timeframe, df, data_source):
    """
    调试数据格式对比函数
    对比xtquant和pytdx数据格式的差异

    Args:
        symbol: 股票代码
        timeframe: 时间周期
        df: K线数据DataFrame
        data_source: 数据源 ('xtquant' 或 'pytdx')
    """
    if not DEBUG_DATA_COMPARISON:
        return

    print(f"\n{'='*60}")
    print(f"数据格式调试 - {data_source.upper()}")
    print(f"股票: {symbol}, 周期: {timeframe}")
    print(f"{'='*60}")

    if df.empty:
        print("❌ 数据为空")
        return

    # 基础信息
    print(f"📊 基础信息:")
    print(f"  数据量: {len(df)} 条")
    print(f"  列名: {list(df.columns)}")
    print(f"  数据类型:")
    for col in df.columns:
        print(f"    {col}: {df[col].dtype}")

    # 时间格式分析
    print(f"\n⏰ 时间格式分析:")
    if 'time' in df.columns:
        time_samples = df['time'].head(3).tolist()
        print(f"  前3个时间样本: {time_samples}")
        print(f"  时间类型: {type(df['time'].iloc[0])}")

        # 尝试转换为datetime
        try:
            dt_converted = pd.to_datetime(df['time'])
            print(f"  datetime转换: ✅ 成功")
            print(f"  转换后类型: {dt_converted.dtype}")
            print(f"  时间范围: {dt_converted.min()} 到 {dt_converted.max()}")
        except Exception as e:
            print(f"  datetime转换: ❌ 失败 - {e}")
    else:
        print("  ❌ 缺少time列")

    # 价格数据分析
    print(f"\n💰 价格数据分析:")
    price_cols = ['open', 'high', 'low', 'close']
    for col in price_cols:
        if col in df.columns:
            col_data = df[col]
            print(f"  {col}:")
            print(f"    范围: {col_data.min():.4f} - {col_data.max():.4f}")
            print(f"    最新: {col_data.iloc[-1]:.4f}")
            print(f"    空值: {col_data.isnull().sum()}")
            print(f"    零值: {(col_data == 0).sum()}")
            print(f"    负值: {(col_data < 0).sum()}")
        else:
            print(f"  {col}: ❌ 缺失")

    # 成交量分析
    print(f"\n📈 成交量分析:")
    if 'volume' in df.columns:
        vol_data = df['volume']
        print(f"  范围: {vol_data.min()} - {vol_data.max()}")
        print(f"  平均: {vol_data.mean():.0f}")
        print(f"  最新: {vol_data.iloc[-1]}")
        print(f"  空值: {vol_data.isnull().sum()}")
        print(f"  零值: {(vol_data == 0).sum()}")
    else:
        print(f"  ❌ 缺少volume列")

    # 数据完整性检查
    print(f"\n🔍 数据完整性检查:")
    total_nulls = df.isnull().sum().sum()
    print(f"  总空值数: {total_nulls}")

    # 检查OHLC逻辑关系
    if all(col in df.columns for col in price_cols):
        # High >= max(Open, Close)
        high_valid = (df['high'] >= df[['open', 'close']].max(axis=1)).all()
        # Low <= min(Open, Close)
        low_valid = (df['low'] <= df[['open', 'close']].min(axis=1)).all()
        print(f"  OHLC逻辑检查:")
        print(f"    High >= max(Open,Close): {'✅' if high_valid else '❌'}")
        print(f"    Low <= min(Open,Close): {'✅' if low_valid else '❌'}")

    # 时间连续性检查（仅检查前10条）
    print(f"\n⏱️ 时间连续性检查 (前10条):")
    if 'time' in df.columns and len(df) > 1:
        try:
            dt_series = pd.to_datetime(df['time'])
            time_diffs = dt_series.diff().dropna()
            if len(time_diffs) > 0:
                print(f"  时间间隔样本: {time_diffs.head(5).tolist()}")
                most_common_diff = time_diffs.mode()
                if len(most_common_diff) > 0:
                    print(f"  最常见间隔: {most_common_diff.iloc[0]}")
        except Exception as e:
            print(f"  时间连续性检查失败: {e}")

    # 数据准备检查
    print(f"\n🎯 数据准备检查:")
    required_cols = ['time', 'open', 'high', 'low', 'close', 'volume']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"  ❌ 缺少必需列: {missing_cols}")
    else:
        print(f"  ✅ 所有必需列都存在")

    # 数据量检查
    min_required = 50
    if len(df) >= min_required:
        print(f"  ✅ 数据量充足 ({len(df)} >= {min_required})")
    else:
        print(f"  ⚠️ 数据量不足 ({len(df)} < {min_required})")

    print(f"{'='*60}\n")


def is_hk_trading_time():
    """
    判断当前是否为港股交易时间段
    港股交易时间段：
    - 早市：9:30 AM - 12:00 PM
    - 午市：1:00 PM - 4:00 PM
    """
    now = datetime.now()
    current_time = now.time()

    # 定义港股交易时间段
    morning_start = datetime.strptime("09:30", "%H:%M").time()
    morning_end = datetime.strptime("12:00", "%H:%M").time()
    afternoon_start = datetime.strptime("13:00", "%H:%M").time()
    afternoon_end = datetime.strptime("16:00", "%H:%M").time()

    # 判断是否在交易时间段内
    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session


def is_valid_hk_kline_time(time_str, period):
    """
    验证港股K线时间是否有效
    禁止16:30等收盘后的K线出现

    参数:
    time_str: 时间字符串，格式如 '2025-07-25 16:30' 或 '20250725163000'
    period: 周期 ('1m', '5m', '15m', '60m')

    返回:
    bool: True表示时间有效，False表示应该过滤掉
    """
    try:
        # 解析时间字符串
        if isinstance(time_str, str):
            if len(time_str) == 14:  # 格式：20250725163000
                dt = pd.to_datetime(time_str, format='%Y%m%d%H%M%S')
            elif '-' in time_str and ':' in time_str:  # 格式：2025-07-25 16:30
                dt = pd.to_datetime(time_str)
            else:
                dt = pd.to_datetime(time_str)
        else:
            dt = pd.to_datetime(time_str)

        # 获取时间部分
        time_part = dt.time()
        hour = dt.hour
        minute = dt.minute

        # 定义港股交易时间段
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("12:00", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("16:00", "%H:%M").time()

        # 检查是否在交易时间段内
        is_morning_session = morning_start <= time_part <= morning_end
        is_afternoon_session = afternoon_start <= time_part <= afternoon_end

        is_valid = is_morning_session or is_afternoon_session

        # 特别检查：禁止16:30等收盘后时间
        if hour == 16 and minute > 0:
            print(f"WARNING: [港股时间过滤] 检测到收盘后K线时间: {time_str} {period}, 已过滤")
            return False

        # 特别检查：禁止12:00-13:00午休时间
        if hour == 12 and minute > 0:
            print(f"WARNING: [港股时间过滤] 检测到午休时间K线: {time_str} {period}, 已过滤")
            return False

        if hour == 12 and minute == 0:
            # 12:00是允许的（午市结束）
            pass
        elif 12 < hour < 13:
            print(f"WARNING: [港股时间过滤] 检测到午休时间K线: {time_str} {period}, 已过滤")
            return False

        # 特别检查：禁止9:30之前的时间
        if hour < 9 or (hour == 9 and minute < 30):
            print(f"WARNING: [港股时间过滤] 检测到开盘前K线时间: {time_str} {period}, 已过滤")
            return False

        # 特别检查：禁止16:00之后的时间
        if hour > 16:
            print(f"WARNING: [港股时间过滤] 检测到收盘后K线时间: {time_str} {period}, 已过滤")
            return False

        if not is_valid:
            print(f"DEBUG: [港股时间过滤] K线时间不在交易时间段内: {time_str} {period}, 已过滤")

        return is_valid

    except Exception as e:
        print(f"ERROR: [港股时间过滤] 时间解析失败: {time_str} {period} - {e}")
        return False








def fetch_hk_kline_data(stock_code, period, count, show_warning=True):
    """
    从xtquant获取港股K线数据

    参数:
    stock_code: 港股代码，如 '09988.HK'
    period: 周期 ('1m', '5m', '15m', '60m', '1d')
    count: 获取数量
    show_warning: 是否显示警告对话框

    返回:
    DataFrame: K线数据
    """
    if not XTQUANT_AVAILABLE:
        if show_warning:
            # 在主线程中显示警告
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, "xtquant未安装，请先安装: pip install xtquant"))
        return pd.DataFrame()

    # 检查xtquant连接状态
    is_connected, error_msg = check_xtquant_connection()
    if not is_connected:
        print(f"xtquant连接检查失败: {error_msg}")
        if show_warning:
            # 在主线程中显示警告
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, error_msg))
        return pd.DataFrame()

    try:
        print(f"DEBUG: 获取港股数据 {stock_code} {period} count={count}")

        # 首先确保历史数据已下载到本地
        print(f"DEBUG: 检查并下载港股历史数据: {stock_code} {period}")

        # xtquant机制：15min和60min数据需要先有1min数据作为基础
        if period in ['15m', '60m']:
            print(f"DEBUG: {period}周期需要先确保1min数据存在")
            # 先下载1min数据
            download_1min_success = download_hk_history_data(stock_code, '1m')
            if download_1min_success:
                print(f"DEBUG: 1min基础数据下载成功，现在下载{period}数据")
            else:
                print(f"WARNING: 1min基础数据下载失败，可能影响{period}数据获取")

        download_success = download_hk_history_data(stock_code, period)
        if not download_success:
            print(f"WARNING: 港股历史数据下载失败，尝试直接获取: {stock_code} {period}")

        # 使用count参数获取数据
        kline_data = xtdata.get_market_data(
            field_list=[],  # 获取所有字段
            stock_list=[stock_code],
            period=period,
            count=count,  # 严格按照传入的count参数
            dividend_type='none'
        )
        print(f"DEBUG: 使用count参数获取数据成功: {count}")

        print(f"DEBUG: xtdata.get_market_data 调用完成")

        if not kline_data or 'time' not in kline_data:
            print(f"港股K线数据为空或格式错误: {stock_code}")
            print(f"DEBUG: kline_data类型: {type(kline_data)}")
            if kline_data:
                print(f"DEBUG: kline_data字段: {list(kline_data.keys()) if hasattr(kline_data, 'keys') else 'N/A'}")

            if show_warning:
                QTimer.singleShot(0, lambda: show_invalid_stock_code_warning(stock_code))
            return pd.DataFrame()

        times = kline_data['time'].columns.tolist()
        if not times or stock_code not in kline_data['time'].index:
            print(f"港股代码不存在或无数据: {stock_code}")
            print(f"DEBUG: 可用时间点数量: {len(times)}")
            print(f"DEBUG: 数据索引: {list(kline_data['time'].index) if hasattr(kline_data['time'], 'index') else 'N/A'}")

            if show_warning:
                QTimer.singleShot(0, lambda: show_invalid_stock_code_warning(stock_code))
            return pd.DataFrame()

        print(f"DEBUG: 成功获取港股数据，时间点数量: {len(times)}")
        if len(times) > 0:
            print(f"DEBUG: 数据时间范围: {times[0]} 到 {times[-1]}")

        # 转换为DataFrame格式
        bars = []
        for time_str in times:
            try:
                # 转换时间格式
                if isinstance(time_str, str) and len(time_str) == 14:
                    # 格式：20231201093000 -> 2023-12-01 09:30
                    time_obj = datetime.strptime(time_str, '%Y%m%d%H%M%S')
                    if period == '1d':
                        formatted_time = time_obj.strftime('%Y-%m-%d')
                    else:
                        formatted_time = time_obj.strftime('%Y-%m-%d %H:%M')
                else:
                    formatted_time = str(time_str)

                bar_data = {
                    'time': formatted_time,
                    'open': float(kline_data['open'].loc[stock_code, time_str]),
                    'high': float(kline_data['high'].loc[stock_code, time_str]),
                    'low': float(kline_data['low'].loc[stock_code, time_str]),
                    'close': float(kline_data['close'].loc[stock_code, time_str]),
                    'volume': int(kline_data['volume'].loc[stock_code, time_str])
                }
                bars.append(bar_data)
            except Exception as e:
                continue

        df = pd.DataFrame(bars)
        if len(df) > 0:
            # 按时间排序
            df = df.sort_values('time').reset_index(drop=True)

            # 调试：对比xtquant数据格式
            debug_data_format_comparison(stock_code, period, df, 'xtquant')

        return df

    except Exception as e:
        error_msg = f"获取港股K线数据失败: {e}"
        print(error_msg)

        # 根据错误类型判断是否需要显示警告
        if show_warning and ("连接" in str(e) or "登录" in str(e) or "网络" in str(e)):
            error_str = str(e)
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, f"港股数据获取异常: {error_str}"))

        return pd.DataFrame()


def download_hk_history_data(stock_code, period):
    """
    下载港股历史数据到本地缓存（按照用户要求：先xtdata.download_history_data）
    带缓存检查，避免重复下载

    参数:
    stock_code: 港股代码，如 '09988.HK'
    period: 周期 ('1m', '5m', '15m', '60m', '1d')
    """
    if not XTQUANT_AVAILABLE:
        print(f"ERROR: [港股下载] xtquant不可用，无法下载历史数据: {stock_code} {period}")
        return False

    from datetime import datetime, timedelta
    global hk_download_history

    print(f"DEBUG: [港股下载] 开始下载历史数据: {stock_code} {period}")

    # 检查是否最近已经下载过（避免频繁重复下载）
    current_time = datetime.now()
    if stock_code in hk_download_history:
        if period in hk_download_history[stock_code]:
            last_download = hk_download_history[stock_code][period]
            # 实时更新时缩短缓存时间：如果5分钟内已经下载过，跳过下载
            cache_seconds = 300  # 5分钟缓存，确保实时数据更新
            if (current_time - last_download).total_seconds() < cache_seconds:
                print(f"DEBUG: [港股下载] 数据最近已下载，跳过: {stock_code} {period} (缓存{cache_seconds}秒)")
                return True

    try:
        end_time = datetime.now()

        # 根据周期和配置的根数设置合理的时间范围
        if period == '1m':
            # 1分钟数据：680根，设置2天确保足够
            start_time = end_time - timedelta(days=200)
        elif period == '5m':
            # 5分钟数据：340根，设置5天确保足够
            start_time = end_time - timedelta(days=200)
        elif period == '15m':
            # 15分钟数据：3022根,设置200天
            start_time = end_time - timedelta(days=200)
        elif period == '60m':
            # 60分钟数据：1134根，设置300天确保足够
            start_time = end_time - timedelta(days=200)
        elif period == '1d':
            # 日线数据：设置200天确保足够
            start_time = end_time - timedelta(days=200)
        else:
            start_time = end_time - timedelta(days=200)

        start_time_str = start_time.strftime('%Y%m%d')
        end_time_str = end_time.strftime('%Y%m%d')

        print(f"DEBUG: [港股下载] 开始下载历史数据 {stock_code} {period} 从 {start_time_str} 到 {end_time_str}")

        # 下载历史数据到本地缓存（按照用户要求使用xtdata.download_history_data）
        xtdata.download_history_data(
            stock_code=stock_code,
            period=period,
            start_time=start_time_str,
            end_time=end_time_str,
            incrementally=True  # 增量下载
        )

        print(f"DEBUG: [港股下载] 历史数据下载完成 {stock_code} {period}")

        # 记录下载时间
        if stock_code not in hk_download_history:
            hk_download_history[stock_code] = {}
        hk_download_history[stock_code][period] = current_time

        return True

    except Exception as e:
        print(f"ERROR: [港股下载] 下载历史数据失败 {stock_code} {period}: {e}")
        import traceback
        traceback.print_exc()
        return False


def download_hk_history_data_force(stock_code, period):
    """
    强制下载港股历史数据到本地缓存（用于实时更新，不检查缓存）

    参数:
    stock_code: 港股代码，如 '09988.HK'
    period: 周期 ('1m', '5m', '15m', '60m', '1d')
    """
    if not XTQUANT_AVAILABLE:
        print(f"ERROR: [港股强制下载] xtquant不可用，无法下载历史数据: {stock_code} {period}")
        return False

    from datetime import datetime, timedelta
    global hk_download_history

    print(f"DEBUG: [港股强制下载] 强制下载最新历史数据: {stock_code} {period}")

    try:
        end_time = datetime.now()

        # 实时更新只需要下载当天的数据
        start_time = end_time.replace(hour=0, minute=0, second=0, microsecond=0)

        start_time_str = start_time.strftime('%Y%m%d')
        end_time_str = end_time.strftime('%Y%m%d')

        print(f"DEBUG: [港股强制下载] 下载当天数据 {stock_code} {period} 从 {start_time_str} 到 {end_time_str}")

        # 强制下载历史数据到本地缓存（按照用户要求使用xtdata.download_history_data）
        xtdata.download_history_data(
            stock_code=stock_code,
            period=period,
            start_time=start_time_str,
            end_time=end_time_str,
            incrementally=True  # 增量下载
        )

        print(f"DEBUG: [港股强制下载] 强制下载完成 {stock_code} {period}")

        # 记录下载时间
        current_time = datetime.now()
        if stock_code not in hk_download_history:
            hk_download_history[stock_code] = {}
        hk_download_history[stock_code][period] = current_time

        return True

    except Exception as e:
        print(f"ERROR: [港股强制下载] 强制下载失败 {stock_code} {period}: {e}")
        import traceback
        traceback.print_exc()
        return False





def fetch_hk_realtime_data_two_bars(stock_code, period):
    """
    获取港股实时数据（最新两根K线）- 严格按照用户要求：先xtdata.download_history_data，后get_market_data

    改进：每30秒更新当前最新的一根K线以及它之前的一根K线，确保数据完整性

    参数:
    stock_code: 港股代码，如 '09988.HK'
    period: 周期 ('1m', '5m', '15m', '60m')

    返回:
    list: 最新的两根K线数据 [前一根K线, 当前K线]，失败返回None
    """
    if not XTQUANT_AVAILABLE:
        print(f"ERROR: [港股实时] xtquant不可用，无法获取港股实时数据: {stock_code} {period}")
        return None

    try:
        print(f"DEBUG: [港股实时] 开始获取最新两根K线数据: {stock_code} {period}")

        # 第一步：先下载历史数据到本地缓存（按照用户要求）
        print(f"DEBUG: [港股实时] 第一步：下载历史数据 {stock_code} {period}")
        download_success = download_hk_history_data_force(stock_code, period)  # 强制下载最新数据
        if not download_success:
            print(f"WARNING: [港股实时] 历史数据下载失败: {stock_code} {period}")
            # 即使下载失败，也尝试获取数据

        # 第二步：从本地缓存获取最新数据（按照用户要求）
        print(f"DEBUG: [港股实时] 第二步：获取市场数据 {stock_code} {period}")
        kline_data = xtdata.get_market_data(
            field_list=[],  # 获取所有字段
            stock_list=[stock_code],
            period=period,
            count=3,  # 获取最近3条数据，确保能获取到最新的两根
            dividend_type='none'
        )

        if not kline_data or 'time' not in kline_data:
            print(f"WARNING: [港股实时] 市场数据获取失败: {stock_code} {period}")
            return None

        # 获取K线数据
        times = kline_data['time'].columns.tolist()
        if not times or stock_code not in kline_data['time'].index:
            print(f"WARNING: [港股实时] 市场数据为空: {stock_code} {period}")
            return None

        if len(times) < 2:
            print(f"WARNING: [港股实时] 数据不足，只有{len(times)}根K线: {stock_code} {period}")
            return None

        # 获取最新的两根K线数据
        latest_time = times[-1]  # 最新时间
        previous_time = times[-2]  # 前一个时间

        # 构造前一根K线数据
        previous_time_str = pd.to_datetime(previous_time).strftime('%Y-%m-%d %H:%M')
        previous_data = {
            'time': previous_time_str,
            'open': float(kline_data['open'].loc[stock_code, previous_time]),
            'high': float(kline_data['high'].loc[stock_code, previous_time]),
            'low': float(kline_data['low'].loc[stock_code, previous_time]),
            'close': float(kline_data['close'].loc[stock_code, previous_time]),
            'volume': int(kline_data['volume'].loc[stock_code, previous_time])
        }

        # 构造当前K线数据
        current_time_str = pd.to_datetime(latest_time).strftime('%Y-%m-%d %H:%M')
        current_data = {
            'time': current_time_str,
            'open': float(kline_data['open'].loc[stock_code, latest_time]),
            'high': float(kline_data['high'].loc[stock_code, latest_time]),
            'low': float(kline_data['low'].loc[stock_code, latest_time]),
            'close': float(kline_data['close'].loc[stock_code, latest_time]),
            'volume': int(kline_data['volume'].loc[stock_code, latest_time])
        }

        # 验证K线时间是否有效（禁止16:30等收盘后时间）
        if not is_valid_hk_kline_time(previous_time_str, period):
            print(f"WARNING: [港股实时] 前一根K线时间无效，已过滤: {previous_time_str}")
            previous_data = None

        if not is_valid_hk_kline_time(current_time_str, period):
            print(f"WARNING: [港股实时] 当前K线时间无效，已过滤: {current_time_str}")
            current_data = None

        # 如果两根K线都无效，返回None
        if previous_data is None and current_data is None:
            print(f"WARNING: [港股实时] 两根K线时间都无效: {stock_code} {period}")
            return None

        # 如果只有一根K线有效，只返回有效的那根
        valid_bars = []
        if previous_data is not None:
            valid_bars.append(previous_data)
        if current_data is not None:
            valid_bars.append(current_data)

        print(f"DEBUG: [港股实时] 获取K线成功: {stock_code} {period}")
        if previous_data:
            print(f"DEBUG: [港股实时] 前一根: {previous_data['time']}")
        if current_data:
            print(f"DEBUG: [港股实时] 当前: {current_data['time']}")

        return valid_bars

    except Exception as e:
        print(f"ERROR: [港股实时] 实时数据获取异常: {stock_code} {period} - {e}")
        import traceback
        traceback.print_exc()
        return None


def fetch_hk_realtime_data(stock_code, period):
    """
    获取港股实时数据（单根K线，保持向后兼容）- 严格按照用户要求：先xtdata.download_history_data，后get_market_data

    参数:
    stock_code: 港股代码，如 '09988.HK'
    period: 周期 ('1m', '5m', '15m', '60m')

    返回:
    dict: 最新的K线数据，失败返回None
    """
    if not XTQUANT_AVAILABLE:
        print(f"ERROR: [港股实时] xtquant不可用，无法获取港股实时数据: {stock_code} {period}")
        return None

    try:
        print(f"DEBUG: [港股实时] 开始获取实时数据: {stock_code} {period}")

        # 第一步：先下载历史数据到本地缓存（按照用户要求）
        print(f"DEBUG: [港股实时] 第一步：下载历史数据 {stock_code} {period}")
        download_success = download_hk_history_data_force(stock_code, period)  # 强制下载最新数据
        if not download_success:
            print(f"WARNING: [港股实时] 历史数据下载失败: {stock_code} {period}")
            # 即使下载失败，也尝试获取数据

        # 第二步：从本地缓存获取最新数据（按照用户要求）
        print(f"DEBUG: [港股实时] 第二步：获取市场数据 {stock_code} {period}")
        kline_data = xtdata.get_market_data(
            field_list=[],  # 获取所有字段
            stock_list=[stock_code],
            period=period,
            count=2,  # 获取最近2条数据，确保能获取到最新的
            dividend_type='none'
        )

        if not kline_data or 'time' not in kline_data:
            print(f"WARNING: [港股实时] 市场数据获取失败: {stock_code} {period}")
            return None

        # 获取最新的K线数据
        times = kline_data['time'].columns.tolist()
        if not times or stock_code not in kline_data['time'].index:
            print(f"WARNING: [港股实时] 市场数据为空: {stock_code} {period}")
            return None

        # 获取最新时间的数据
        latest_time = times[-1]
        latest_time_str = pd.to_datetime(latest_time).strftime('%Y-%m-%d %H:%M')

        # 验证K线时间是否有效（禁止16:30等收盘后时间）
        if not is_valid_hk_kline_time(latest_time_str, period):
            print(f"WARNING: [港股实时] K线时间无效，已过滤: {latest_time_str} {period}")
            return None

        # 构造返回数据
        latest_data = {
            'time': latest_time_str,
            'open': float(kline_data['open'].loc[stock_code, latest_time]),
            'high': float(kline_data['high'].loc[stock_code, latest_time]),
            'low': float(kline_data['low'].loc[stock_code, latest_time]),
            'close': float(kline_data['close'].loc[stock_code, latest_time]),
            'volume': int(kline_data['volume'].loc[stock_code, latest_time])
        }

        print(f"DEBUG: [港股实时] 实时数据获取成功: {stock_code} {period} {latest_data['time']}")
        return latest_data

    except Exception as e:
        print(f"ERROR: [港股实时] 实时数据获取异常: {stock_code} {period} - {e}")
        import traceback
        traceback.print_exc()
        return None


def fetch_hk_minute_data(symbol, show_warning=True):
    """获取港股1分钟K线数据"""
    stock_info = parse_stock_code(symbol, show_warning=False)
    if not stock_info or not stock_info.get('is_hk', False):
        return pd.DataFrame()

    stock_code = stock_info['code']

    # 检查xtquant连接状态
    is_connected, error_msg = check_xtquant_connection()
    if not is_connected:
        if show_warning:
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, error_msg))
        return pd.DataFrame()

    try:
        # 获取历史1分钟数据
        historical_data = fetch_hk_kline_data(stock_code, '1m', HK_MINUTE_CYCLE_COUNT, show_warning)
        return historical_data

    except Exception as e:
        print(f"获取港股1分钟数据失败: {symbol}, {e}")
        if show_warning:
            error_str = str(e)
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, f"港股数据获取异常: {error_str}"))
        return pd.DataFrame()


def fetch_hk_5min_data(symbol, show_warning=True):
    """获取港股5分钟K线数据"""
    stock_info = parse_stock_code(symbol, show_warning=False)
    if not stock_info or not stock_info.get('is_hk', False):
        return pd.DataFrame()

    stock_code = stock_info['code']

    # 检查xtquant连接状态
    is_connected, error_msg = check_xtquant_connection()
    if not is_connected:
        if show_warning:
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, error_msg))
        return pd.DataFrame()

    try:
        # 直接获取5分钟历史数据
        historical_data = fetch_hk_kline_data(stock_code, '5m', HK_FIVE_MINUTE_CYCLE_COUNT, show_warning)
        return historical_data

    except Exception as e:
        print(f"获取港股5分钟数据失败: {symbol}, {e}")
        if show_warning:
            error_str = str(e)
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, f"港股数据获取异常: {error_str}"))
        return pd.DataFrame()


def fetch_hk_15min_data(symbol, show_warning=True):
    """获取港股15分钟K线数据"""
    stock_info = parse_stock_code(symbol, show_warning=False)
    if not stock_info or not stock_info.get('is_hk', False):
        return pd.DataFrame()

    stock_code = stock_info['code']

    # 检查xtquant连接状态
    is_connected, error_msg = check_xtquant_connection()
    if not is_connected:
        if show_warning:
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, error_msg))
        return pd.DataFrame()

    try:
        # 直接获取15分钟历史数据
        historical_data = fetch_hk_kline_data(stock_code, '15m', HK_FIFTEEN_MINUTE_CYCLE_COUNT, show_warning)
        return historical_data

    except Exception as e:
        print(f"获取港股15分钟数据失败: {symbol}, {e}")
        if show_warning:
            error_str = str(e)
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, f"港股数据获取异常: {error_str}"))
        return pd.DataFrame()


def fetch_hk_60min_data(symbol, show_warning=True):
    """获取港股60分钟K线数据"""
    stock_info = parse_stock_code(symbol, show_warning=False)
    if not stock_info or not stock_info.get('is_hk', False):
        return pd.DataFrame()

    stock_code = stock_info['code']

    # 检查xtquant连接状态
    is_connected, error_msg = check_xtquant_connection()
    if not is_connected:
        if show_warning:
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, error_msg))
        return pd.DataFrame()

    try:
        # 直接获取60分钟历史数据
        historical_data = fetch_hk_kline_data(stock_code, '60m', HK_SIXTY_MINUTE_CYCLE_COUNT, show_warning)
        return historical_data

    except Exception as e:
        print(f"获取港股60分钟数据失败: {symbol}, {e}")
        if show_warning:
            error_str = str(e)
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, f"港股数据获取异常: {error_str}"))
        return pd.DataFrame()


def fetch_hk_daily_data(symbol, show_warning=True):
    """获取港股日线数据"""
    stock_info = parse_stock_code(symbol, show_warning=False)
    if not stock_info or not stock_info.get('is_hk', False):
        return pd.DataFrame()

    stock_code = stock_info['code']

    # 检查xtquant连接状态
    is_connected, error_msg = check_xtquant_connection()
    if not is_connected:
        if show_warning:
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, error_msg))
        return pd.DataFrame()

    try:
        # 直接获取日线历史数据
        historical_data = fetch_hk_kline_data(stock_code, '1d', HK_DAILY_CYCLE_COUNT, show_warning)
        return historical_data

    except Exception as e:
        print(f"获取港股日线数据失败: {symbol}, {e}")
        if show_warning:
            error_str = str(e)
            QTimer.singleShot(0, lambda: show_xtquant_warning(None, f"港股数据获取异常: {error_str}"))
        return pd.DataFrame()





def synthesize_kline_from_minute_data(minute_df, target_period_minutes):
    """
    从1分钟K线数据合成指定周期的K线数据

    参数:
    minute_df: 1分钟K线数据DataFrame
    target_period_minutes: 目标周期分钟数 (5, 15, 60, 1440)

    返回:
    DataFrame: 合成的K线数据
    """
    if minute_df.empty:
        return pd.DataFrame()

    try:
        # 转换时间列为datetime
        minute_df = minute_df.copy()
        minute_df['datetime'] = pd.to_datetime(minute_df['time'])
        minute_df = minute_df.sort_values('datetime').reset_index(drop=True)

        synthesized_data = []

        if target_period_minutes == 1440:  # 日线
            # 按日期分组
            minute_df['date'] = minute_df['datetime'].dt.date
            for date, group in minute_df.groupby('date'):
                if len(group) > 0:
                    synthesized_data.append({
                        'time': date.strftime('%Y-%m-%d'),
                        'open': group.iloc[0]['open'],
                        'high': group['high'].max(),
                        'low': group['low'].min(),
                        'close': group.iloc[-1]['close'],
                        'volume': group['volume'].sum()
                    })
        else:
            # 其他周期：按时间分组
            for i in range(0, len(minute_df), target_period_minutes):
                group = minute_df.iloc[i:i+target_period_minutes]
                if len(group) > 0:
                    # 计算周期开始时间
                    first_time = group.iloc[0]['datetime']

                    # 对于5分钟、15分钟、60分钟，需要对齐到正确的时间边界
                    if target_period_minutes in [5, 15, 60]:
                        # 计算从9:30开始的分钟数
                        market_open = first_time.replace(hour=9, minute=30, second=0, microsecond=0)
                        minutes_since_open = int((first_time - market_open).total_seconds() / 60)

                        # 对齐到周期边界
                        aligned_minutes = (minutes_since_open // target_period_minutes) * target_period_minutes
                        period_start = market_open + timedelta(minutes=aligned_minutes)
                    else:
                        period_start = first_time

                    synthesized_data.append({
                        'time': period_start.strftime('%Y-%m-%d %H:%M'),
                        'open': group.iloc[0]['open'],
                        'high': group['high'].max(),
                        'low': group['low'].min(),
                        'close': group.iloc[-1]['close'],
                        'volume': group['volume'].sum()
                    })

        return pd.DataFrame(synthesized_data)

    except Exception as e:
        print(f"从1分钟数据合成{target_period_minutes}分钟数据失败: {e}")
        return pd.DataFrame()


def diagnose_hk_data_issue(symbol):
    """
    诊断港股数据获取问题

    参数:
    symbol: 港股代码，如 '00700.HK'
    """
    print(f"\n{'='*60}")
    print(f"港股数据诊断报告 - {symbol}")
    print(f"{'='*60}")

    # 1. 检查xtquant可用性
    print(f"1. xtquant软件检查:")
    if not XTQUANT_AVAILABLE:
        print(f"   ❌ xtquant未安装")
        return
    else:
        print(f"   ✅ xtquant已安装")

    # 2. 检查连接状态
    print(f"2. 连接状态检查:")
    is_connected, error_msg = check_xtquant_connection()
    if not is_connected:
        print(f"   ❌ 连接失败: {error_msg}")
        return
    else:
        print(f"   ✅ 连接正常")

    # 3. 检查股票代码解析
    print(f"3. 股票代码解析:")
    stock_info = parse_stock_code(symbol, show_warning=False)
    if not stock_info or not stock_info.get('is_hk', False):
        print(f"   ❌ 股票代码解析失败或不是港股代码")
        return
    else:
        print(f"   ✅ 股票代码解析成功: {stock_info}")

    # 4. 测试各个周期的数据获取
    print(f"4. 数据获取测试:")
    periods = ['1m', '5m', '15m', '60m', '1d']
    for period in periods:
        try:
            print(f"   测试 {period} 周期...")

            # 先尝试下载历史数据
            download_success = download_hk_history_data(stock_info['code'], period)
            print(f"     历史数据下载: {'✅' if download_success else '❌'}")

            # 获取数据
            test_data = xtdata.get_market_data(
                field_list=['close'],
                stock_list=[stock_info['code']],
                period=period,
                count=10,
                dividend_type='none'
            )

            if test_data and 'close' in test_data:
                times = test_data['close'].columns.tolist()
                if times and stock_info['code'] in test_data['close'].index:
                    latest_time = times[-1] if times else "无数据"
                    print(f"     数据获取: ✅ (最新时间: {latest_time}, 数据量: {len(times)})")
                else:
                    print(f"     数据获取: ❌ (无有效数据)")
            else:
                print(f"     数据获取: ❌ (API返回空)")

        except Exception as e:
            print(f"     数据获取: ❌ (异常: {e})")

    print(f"{'='*60}\n")


def normalize_symbol_for_cache(symbol):
    """标准化股票代码用于缓存键，避免冲突"""
    # 移除可能的后缀，统一格式
    if symbol.endswith('.SH') or symbol.endswith('.SZ'):
        return symbol[:-3]
    elif symbol.endswith('.HK'):
        return symbol[:-3]
    return symbol

def get_bar_data(symbol, timeframe, show_warning=True):
    """获取K线数据，支持缓存，自动判断A股或港股"""
    # 使用标准化的股票代码作为缓存键
    normalized_symbol = normalize_symbol_for_cache(symbol)
    cache_key = f"{normalized_symbol}_{timeframe}"
    global history_cache

    # 减少调试输出频率
    # print(f"DEBUG: 获取K线数据 - 股票: {symbol}, 周期: {timeframe}")

    if cache_key in history_cache:
        # print(f"DEBUG: 从缓存获取数据: {cache_key}")
        return history_cache[cache_key]

    try:
        # 判断是否为港股 - 只解析一次
        stock_info = parse_stock_code(symbol, show_warning=False)
        # print(f"DEBUG: 股票信息解析完成: {stock_info}")

        # 统一数据获取逻辑：港股和A股都使用相同的处理方式
        if timeframe == '1min':
            new_data = fetch_minute_data(symbol, show_warning)
        elif timeframe == '5min':
            new_data = fetch_5min_data(symbol, show_warning)
        elif timeframe == '15min':
            new_data = fetch_15min_data(symbol, show_warning)
        elif timeframe == '60min':
            new_data = fetch_60min_data(symbol, show_warning)
        elif timeframe == '1day':
            new_data = fetch_daily_data(symbol, show_warning)
        else:
            print(f"ERROR: 不支持的时间周期: {timeframe}")
            return pd.DataFrame()

        # print(f"DEBUG: 数据获取完成 - 数据量: {len(new_data) if new_data is not None else 0}")

        if new_data is not None and not new_data.empty:
            history_cache[cache_key] = new_data
            # print(f"DEBUG: 数据已缓存: {cache_key}")
        else:
            print(f"WARNING: 获取到空数据: {symbol} {timeframe}")

        return new_data

    except Exception as e:
        print(f"ERROR: 获取K线数据异常: {symbol} {timeframe} - {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()


class AllDataPreloadThread(QThread):
    """预加载所有周期数据的线程 - 先获取所有数据到缓存，再通知UI更新"""
    all_data_loaded = Signal(str, dict)  # 信号：symbol, {timeframe: data}
    preload_failed = Signal(str, str)  # 信号：symbol, error_message

    def __init__(self, symbol, timeframes):
        super().__init__()
        self.symbol = symbol
        self.timeframes = timeframes  # ['1min', '5min', '15min', '60min']

    def run(self):
        """并发获取所有周期的数据"""
        try:
            print(f"DEBUG: 开始预加载股票数据: {self.symbol}, 周期: {self.timeframes}")

            all_data = {}
            failed_timeframes = []

            # 检查是否为港股，如果是港股需要按顺序加载
            stock_info = parse_stock_code(self.symbol, show_warning=False)
            is_hk_stock = stock_info and stock_info.get('is_hk', False)

            if is_hk_stock:
                print(f"DEBUG: 港股数据按顺序加载: {self.symbol}")
                # 港股：先加载1min数据，再加载其他周期
                ordered_timeframes = []
                # 1. 先加载1min（如果需要）
                if '1min' in self.timeframes:
                    ordered_timeframes.append('1min')
                # 2. 再加载5min（如果需要）
                if '5min' in self.timeframes:
                    ordered_timeframes.append('5min')
                # 3. 最后加载15min和60min（依赖1min数据）
                for tf in ['15min', '60min']:
                    if tf in self.timeframes:
                        ordered_timeframes.append(tf)
                # 4. 添加其他周期
                for tf in self.timeframes:
                    if tf not in ordered_timeframes:
                        ordered_timeframes.append(tf)

                timeframes_to_load = ordered_timeframes
                print(f"DEBUG: 港股加载顺序: {timeframes_to_load}")
            else:
                # A股：可以并发加载
                timeframes_to_load = self.timeframes

            # 按顺序获取数据
            for i, timeframe in enumerate(timeframes_to_load):
                try:
                    # 减少调试输出频率
                    # print(f"DEBUG: 正在获取 {self.symbol} {timeframe} 数据... (第{i+1}/{len(timeframes_to_load)}个)")
                    # 只在第一个时间周期显示警告，避免重复弹窗
                    show_warning = (i == 0)
                    data = get_bar_data(self.symbol, timeframe, show_warning)

                    if data is None:
                        print(f"ERROR: {self.symbol} {timeframe} 返回None")
                        failed_timeframes.append(timeframe)
                    elif data.empty:
                        print(f"WARNING: {self.symbol} {timeframe} 数据为空DataFrame")
                        failed_timeframes.append(timeframe)
                    else:
                        all_data[timeframe] = data
                        print(f"SUCCESS: {self.symbol} {timeframe} 数据获取成功: {len(data)}条")

                        # 港股：如果是1min数据成功加载，稍等一下让缓存生效
                        if is_hk_stock and timeframe == '1min':
                            print(f"DEBUG: 港股1min数据加载完成，等待缓存生效...")
                            time.sleep(1)  # 等待1秒让xtquant缓存生效

                except Exception as e:
                    print(f"ERROR: 获取 {self.symbol} {timeframe} 数据异常: {e}")
                    import traceback
                    traceback.print_exc()
                    failed_timeframes.append(timeframe)

            # 检查是否有足够的数据
            if len(all_data) == 0:
                error_msg = f"股票代码不存在或暂无数据: {self.symbol}"
                print(f"ERROR: 预加载失败 - {error_msg}")
                self.preload_failed.emit(self.symbol, error_msg)
                return

            if failed_timeframes:
                print(f"WARNING: 部分周期数据获取失败: {failed_timeframes}")

            print(f"SUCCESS: 数据预加载完成: {self.symbol}, 成功周期: {list(all_data.keys())}")

            # 发送所有数据加载完成信号
            self.all_data_loaded.emit(self.symbol, all_data)

        except Exception as e:
            print(f"FATAL: 数据预加载异常: {self.symbol} - {e}")
            import traceback
            traceback.print_exc()
            self.preload_failed.emit(self.symbol, str(e))


class StockSearchThread(QThread):
    """后台股票搜索线程 - 处理新股票代码的数据加载"""
    # 分阶段信号，避免主线程阻塞
    data_loaded = Signal(str, str, pd.DataFrame)  # 信号：symbol, timeframe, data - 数据加载完成
    search_failed = Signal(str, str)  # 信号：symbol, error_message

    def __init__(self, symbol, timeframe, chart):
        super().__init__()
        self.symbol = symbol
        self.timeframe = timeframe
        self.chart = chart

    def run(self):
        """简化的后台搜索线程"""
        try:
            # 获取K线数据
            new_data = get_bar_data(self.symbol, self.timeframe)

            if new_data.empty:
                error_msg = f"股票代码不存在或暂无数据: {self.symbol}"
                self.search_failed.emit(self.symbol, error_msg)
                return

            # 发送数据加载完成信号
            self.data_loaded.emit(self.symbol, self.timeframe, new_data)

        except Exception as e:
            self.search_failed.emit(self.symbol, str(e))


# ============================================================================
# 港股实时数据更新相关函数
# ============================================================================

def is_period_closed(current_time, period_minutes):
    """
    检测指定周期是否已闭合

    参数:
    current_time: 当前时间 (datetime对象)
    period_minutes: 周期分钟数 (5, 15, 60)

    返回:
    bool: True表示周期已闭合
    """
    current_minute = current_time.minute

    if period_minutes == 5:
        return current_minute % 5 == 0
    elif period_minutes == 15:
        return current_minute % 15 == 0
    elif period_minutes == 60:
        return current_minute == 0

    return False


def get_period_boundary_time(current_time, period_minutes):
    """
    获取周期边界时间

    参数:
    current_time: 当前时间
    period_minutes: 周期分钟数

    返回:
    datetime: 周期边界时间
    """
    if period_minutes == 5:
        boundary_minute = (current_time.minute // 5) * 5
        return current_time.replace(minute=boundary_minute, second=0, microsecond=0)
    elif period_minutes == 15:
        boundary_minute = (current_time.minute // 15) * 15
        return current_time.replace(minute=boundary_minute, second=0, microsecond=0)
    elif period_minutes == 60:
        return current_time.replace(minute=0, second=0, microsecond=0)

    return current_time


def synthesize_kline_from_1min(stock_code, target_period_minutes, current_time):
    """
    从1分钟K线数据合成指定周期的K线

    参数:
    stock_code: 股票代码
    target_period_minutes: 目标周期分钟数 (5, 15, 60)
    current_time: 当前时间

    返回:
    dict: 合成的K线数据，如果无法合成则返回None
    """
    global hk_kline_cache

    # 获取1分钟K线缓存
    one_min_df = hk_kline_cache.get(stock_code, {}).get('1m', pd.DataFrame())
    if one_min_df.empty:
        return None

    # 获取当前周期的边界时间
    boundary_time = get_period_boundary_time(current_time, target_period_minutes)

    # 对于合成K线，我们需要获取当前正在进行的周期的数据
    period_start = boundary_time
    period_end = period_start + timedelta(minutes=target_period_minutes)

    # 筛选周期内的1分钟数据
    period_start_str = period_start.strftime('%Y-%m-%d %H:%M')
    period_end_str = period_end.strftime('%Y-%m-%d %H:%M')

    # 筛选数据时使用字符串比较
    period_data = one_min_df[
        (one_min_df['time'] >= period_start_str) &
        (one_min_df['time'] < period_end_str)
    ].copy()

    if period_data.empty:
        return None

    # 合成K线数据
    synthesized_kline = {
        'time': boundary_time.strftime('%Y-%m-%d %H:%M'),
        'open': period_data.iloc[0]['open'],      # 第一根1分钟K线的开盘价
        'high': period_data['high'].max(),        # 周期内最高价
        'low': period_data['low'].min(),          # 周期内最低价
        'close': period_data.iloc[-1]['close'],   # 最后一根1分钟K线的收盘价
        'volume': period_data['volume'].sum()     # 周期内成交量总和
    }

    return synthesized_kline


def update_hk_kline_cache(stock_code, timeframe, kline_data):
    """
    更新港股K线缓存

    参数:
    stock_code: 股票代码
    timeframe: 时间周期 ('1m', '5m', '15m', '60m')
    kline_data: K线数据 (dict格式)
    """
    global hk_kline_cache, hk_last_update_times

    if kline_data is None:
        return

    # 初始化缓存结构
    if stock_code not in hk_kline_cache:
        hk_kline_cache[stock_code] = {}
    if stock_code not in hk_last_update_times:
        hk_last_update_times[stock_code] = {}

    # 获取当前缓存
    current_cache = hk_kline_cache[stock_code].get(timeframe, pd.DataFrame())

    # 创建新的数据行
    new_row = pd.DataFrame([kline_data])

    # 检查是否是新数据
    kline_time = kline_data['time']
    if not current_cache.empty:
        # 检查最新时间是否已存在
        if kline_time in current_cache['time'].values:
            # 更新现有数据
            mask = current_cache['time'] == kline_time
            for col in ['open', 'high', 'low', 'close', 'volume']:
                current_cache.loc[mask, col] = kline_data[col]
        else:
            # 添加新数据
            current_cache = pd.concat([current_cache, new_row], ignore_index=True)
    else:
        # 首次添加数据
        current_cache = new_row.copy()

    # 保持数据量限制（保留最近1000条）
    if len(current_cache) > 1000:
        current_cache = current_cache.tail(1000).reset_index(drop=True)

    # 更新缓存
    hk_kline_cache[stock_code][timeframe] = current_cache
    hk_last_update_times[stock_code][timeframe] = kline_time


class DataUpdateThread(QThread):
    """后台数据更新线程 - 支持A股和港股，专门获取1分钟实时数据，其他周期基于1分钟数据计算"""
    data_updated = Signal(pd.Series)  # 信号：用于发送新数据到主线程

    def __init__(self, symbol):
        super().__init__()
        self.symbol = symbol
        self.running = True
        self.last_timestamp = None

        # 判断是否为港股
        stock_info = parse_stock_code(symbol, show_warning=False)
        self.is_hk_stock = stock_info and stock_info.get('is_hk', False)

        # 1分钟缓存数据
        self.one_min_cache = pd.DataFrame()

    def stop(self):
        """停止线程并清理资源"""
        print(f"DEBUG: [线程停止] 开始停止线程: {self.symbol}")
        self.running = False

        print(f"DEBUG: [线程停止] 线程停止完成: {self.symbol}")
        self.quit()
        self.wait()

    def run(self):
        # 根据股票类型选择不同的更新逻辑
        if self.is_hk_stock:
            print(f"DEBUG: 启动港股实时更新线程: {self.symbol}")
            self.run_hk_realtime_update()
        else:
            print(f"DEBUG: 启动A股实时更新线程: {self.symbol}")
            self.run_a_share_update()

    def run_hk_realtime_update(self):
        """
        港股实时数据更新逻辑 - 按照用户要求实现：
        1. 每30秒轮询一次
        2. 先xtdata.download_history_data，后get_market_data
        3. 先下载1min、5min，下载完成之后下载15min和60min
        4. 保证最后一根K线在不同周期上也是每30秒更新一次
        """
        print(f"DEBUG: [港股线程] 进入港股实时更新线程: {self.symbol}")
        print(f"DEBUG: [港股线程] 实时更新策略：30秒轮询，先下载历史数据，后获取市场数据")
        print(f"DEBUG: [港股线程] 下载顺序：先1min、5min，完成后下载15min、60min")

        stock_info = parse_stock_code(self.symbol, show_warning=False)
        if not stock_info:
            print(f"ERROR: [港股线程] 股票代码解析失败: {self.symbol}")
            return

        stock_code = stock_info['code']
        print(f"DEBUG: [港股线程] 股票代码解析成功: {self.symbol} -> {stock_code}")
        print(f"DEBUG: [港股线程] 开始港股30秒轮询更新: {stock_code}")

        loop_count = 0
        # 支持的港股实时更新周期（按照用户要求的顺序）
        hk_periods = ['1m', '5m', '15m', '60m']
        last_data_cache = {}  # 缓存上次获取的数据，用于检测变化

        # 主循环：每30秒获取一次多周期数据
        while self.running:
            loop_count += 1
            current_local_time = datetime.now().strftime('%H:%M:%S')

            try:
                print(f"DEBUG: [港股线程] 第{loop_count}次轮询开始 ({current_local_time}): {self.symbol}")

                # 检查线程是否应该继续运行
                if not self.running:
                    print(f"DEBUG: [港股线程] 线程停止标志已设置，退出轮询: {self.symbol}")
                    break

                # 检查是否在港股交易时间段内
                is_trading = is_hk_trading_time()
                print(f"DEBUG: [港股线程] 交易时间检查: {is_trading}, 当前时间: {current_local_time}")

                if not is_trading:
                    print(f"DEBUG: [港股线程] 港股非交易时间，跳过更新: {self.symbol}")
                    # 非交易时间等待30秒
                    for i in range(30):
                        if not self.running:
                            print(f"DEBUG: [港股线程] 等待期间检测到停止信号，立即退出: {self.symbol}")
                            break
                        time.sleep(1)
                    continue

                # 按照用户要求的顺序获取数据：先下载1min、5min，下载完成之后下载15min、60min
                print(f"DEBUG: [港股线程] 开始获取港股实时数据: {stock_code}")

                # 第一批：先下载1min和5min（按照用户要求的顺序）
                # 改进：每次获取最新的两根K线，确保数据完整性
                first_batch = ['1m', '5m']
                print(f"DEBUG: [港股线程] 第一批：开始下载 {first_batch} 数据（每次获取最新两根K线）")

                for period in first_batch:
                    if not self.running:
                        break

                    print(f"DEBUG: [港股线程] 获取 {period} 最新两根K线数据: {stock_code}")
                    two_bars_data = fetch_hk_realtime_data_two_bars(stock_code, period)

                    if two_bars_data and len(two_bars_data) >= 1:
                        # 处理K线数据（可能是1根或2根，取决于时间过滤结果）
                        print(f"DEBUG: [港股线程] {period} 获取到{len(two_bars_data)}根有效K线")

                        # 处理每根有效的K线
                        for i, bar_data in enumerate(two_bars_data):
                            bar_type = 'previous' if i == 0 and len(two_bars_data) == 2 else 'current'

                            print(f"DEBUG: [港股线程] {period} {bar_type}K线: {bar_data['time']}")

                            # 发送K线数据更新信号
                            bar_series = pd.Series(bar_data)
                            bar_series['period'] = period
                            bar_series['bar_type'] = bar_type

                            print(f"DEBUG: [港股线程] {period} 发送{bar_type}K线更新信号")
                            self.data_updated.emit(bar_series)

                        # 更新缓存（使用最后一根K线数据）
                        cache_key = f"{stock_code}_{period}"
                        last_data_cache[cache_key] = two_bars_data[-1].copy()

                    else:
                        print(f"WARNING: [港股线程] {period} K线数据获取失败或被时间过滤器过滤")

                print(f"DEBUG: [港股线程] 第一批下载完成，开始第二批")

                # 第二批：下载完成之后下载15min和60min（按照用户要求的顺序）
                # 改进：每次获取最新的两根K线，确保数据完整性
                second_batch = ['15m', '60m']
                print(f"DEBUG: [港股线程] 第二批：开始下载 {second_batch} 数据（每次获取最新两根K线）")

                for period in second_batch:
                    if not self.running:
                        break

                    print(f"DEBUG: [港股线程] 获取 {period} 最新两根K线数据: {stock_code}")
                    two_bars_data = fetch_hk_realtime_data_two_bars(stock_code, period)

                    if two_bars_data and len(two_bars_data) >= 1:
                        # 处理K线数据（可能是1根或2根，取决于时间过滤结果）
                        print(f"DEBUG: [港股线程] {period} 获取到{len(two_bars_data)}根有效K线")

                        # 处理每根有效的K线
                        for i, bar_data in enumerate(two_bars_data):
                            bar_type = 'previous' if i == 0 and len(two_bars_data) == 2 else 'current'

                            print(f"DEBUG: [港股线程] {period} {bar_type}K线: {bar_data['time']}")

                            # 发送K线数据更新信号
                            bar_series = pd.Series(bar_data)
                            bar_series['period'] = period
                            bar_series['bar_type'] = bar_type

                            print(f"DEBUG: [港股线程] {period} 发送{bar_type}K线更新信号")
                            self.data_updated.emit(bar_series)

                        # 更新缓存（使用最后一根K线数据）
                        cache_key = f"{stock_code}_{period}"
                        last_data_cache[cache_key] = two_bars_data[-1].copy()

                    else:
                        print(f"WARNING: [港股线程] {period} K线数据获取失败或被时间过滤器过滤")

                print(f"DEBUG: [港股线程] 第二批下载完成")

                print(f"DEBUG: [港股线程] 第{loop_count}次轮询完成 ({current_local_time})，等待30秒...")
                # 等待30秒后进行下一次轮询，但要检查停止标志（按照用户要求的30秒间隔）
                for i in range(30):
                    if not self.running:
                        print(f"DEBUG: [港股线程] 等待期间检测到停止信号，立即退出: {self.symbol}")
                        break
                    time.sleep(1)

            except Exception as e:
                print(f"ERROR: [港股线程] 第{loop_count}次轮询异常: {self.symbol}, {e}")
                import traceback
                traceback.print_exc()

                # 检查是否因为线程停止导致的异常
                if not self.running:
                    print(f"DEBUG: [港股线程] 线程已停止，异常可能是正常的清理过程: {self.symbol}")
                    break

                print(f"DEBUG: [港股线程] 异常后等待30秒...")
                # 等待30秒，但要检查停止标志（按照用户要求的30秒间隔）
                for i in range(30):
                    if not self.running:
                        print(f"DEBUG: [港股线程] 异常等待期间检测到停止信号，立即退出: {self.symbol}")
                        break
                    time.sleep(1)

        print(f"DEBUG: [港股线程] 港股实时更新线程正常退出: {self.symbol}")






    def fetch_closed_kline_from_xtquant(self, stock_code, period, boundary_time):
        """
        从xtquant获取闭合的K线数据

        参数:
        stock_code: 股票代码
        period: 周期 ('5m', '15m', '60m')
        boundary_time: 边界时间

        返回:
        dict: K线数据，如果获取失败则返回None
        """
        try:
            # 获取指定周期的K线数据
            kline_data = xtdata.get_market_data(
                field_list=[],  # 获取所有字段
                stock_list=[stock_code],
                period=period,
                count=5,  # 获取最近5条数据
                dividend_type='none'
            )

            if kline_data and 'time' in kline_data:
                times = kline_data['time'].columns.tolist()

                if times and stock_code in kline_data['time'].index:
                    # 查找匹配的时间
                    boundary_time_str = boundary_time.strftime('%Y%m%d%H%M%S')

                    for time_str in times:
                        if time_str == boundary_time_str:
                            # 找到匹配的K线数据
                            return {
                                'time': boundary_time.strftime('%Y-%m-%d %H:%M'),
                                'open': kline_data['open'].loc[stock_code, time_str],
                                'high': kline_data['high'].loc[stock_code, time_str],
                                'low': kline_data['low'].loc[stock_code, time_str],
                                'close': kline_data['close'].loc[stock_code, time_str],
                                'volume': kline_data['volume'].loc[stock_code, time_str]
                            }

        except Exception as e:
            print(f"从xtquant获取{period}K线数据失败: {e}")

        return None

    def run_a_share_update(self):
        """A股实时数据更新逻辑（原有逻辑）"""
        global api, stock_codes

        while self.running:
            try:
                # 获取或解析股票信息
                if self.symbol not in stock_codes:
                    stock_info = parse_stock_code(self.symbol, show_warning=False)
                    if not stock_info:
                        time.sleep(10)
                        continue
                else:
                    stock_info = stock_codes[self.symbol]

                market = stock_info['market']
                clean_symbol = stock_info['code']

                # 使用标准行情API
                if not ensure_connection():
                    time.sleep(10)
                    continue
                current_api = api
                # 只获取最新的1分钟数据（period=7表示1分钟）
                data = current_api.get_security_bars(7, market, clean_symbol, 0, 1)

                if data and len(data) > 0:
                    bar = data[0]
                    try:
                        # 检查必需字段
                        required_fields = ['datetime', 'open', 'high', 'low', 'close', 'vol']
                        missing_fields = [field for field in required_fields if field not in bar]
                        if missing_fields:
                            time.sleep(10)
                            continue

                        datetime_value = bar['datetime']
                        if isinstance(datetime_value, str):
                            dt = datetime.strptime(datetime_value, '%Y-%m-%d %H:%M')
                            time_str = dt.strftime('%Y-%m-%d %H:%M')
                        elif hasattr(datetime_value, 'strftime'):
                            time_str = datetime_value.strftime('%Y-%m-%d %H:%M')
                        else:
                            time_str = str(datetime_value)

                        # 检查是否在交易时间内
                        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                        if not is_trading_time(dt):
                            time.sleep(10)
                            continue

                        # 如果时间戳不同，更新缓存并发送更新信号
                        if time_str != self.last_timestamp:
                            self.last_timestamp = time_str

                            try:
                                latest_data = pd.Series({
                                    'time': time_str,
                                    'open': float(bar['open']),
                                    'high': float(bar['high']),
                                    'low': float(bar['low']),
                                    'close': float(bar['close']),
                                    'volume': int(bar['vol'])
                                })

                                # 更新1分钟缓存数据
                                self.update_1min_cache(latest_data)

                                # 发送实时数据信号，让各个图表根据自己的时间周期决定是否更新
                                self.data_updated.emit(latest_data)
                            except Exception as data_error:
                                pass

                    except Exception as e:
                        pass

            except Exception as e:
                pass

            # 休眠30秒（减少服务器压力），但要检查停止标志
            for i in range(30):
                if not self.running:
                    break
                time.sleep(1)

    def update_1min_cache(self, latest_data):
        """更新1分钟缓存数据（仅支持A股）"""
        # A股缓存更新
        global history_cache
        cache_key_1min = f"{self.symbol}_1min"

        if cache_key_1min in history_cache:
            cached_df = history_cache[cache_key_1min]

            # 检查是否已存在相同时间的数据
            existing_row_idx = cached_df[cached_df['time'] == latest_data['time']].index
            if len(existing_row_idx) > 0:
                # 更新现有数据
                history_cache[cache_key_1min].loc[existing_row_idx[0]] = latest_data
            else:
                # 添加新数据
                new_row = pd.DataFrame([latest_data])
                history_cache[cache_key_1min] = pd.concat([cached_df, new_row], ignore_index=True)

                # 保持数据量限制
                if len(history_cache[cache_key_1min]) > MINUTE_CYCLE_COUNT:
                    history_cache[cache_key_1min] = history_cache[cache_key_1min].tail(MINUTE_CYCLE_COUNT)

    def stop(self):
        self.running = False


# 在初始化时就创建并启动更新线程
def on_data_loaded(chart, symbol, timeframe, new_data):
    """处理数据加载完成的回调函数 - 快速更新图表，不阻塞UI"""
    try:

        # 数据加载成功，清除错误标记
        global current_error_symbol
        if current_error_symbol == symbol:
            current_error_symbol = None

        # 更新图表标识
        chart.topbar['symbol'].set(symbol)

        # 数据验证
        if new_data is None or len(new_data) == 0:
            print(f"警告: {symbol} {timeframe} 数据为空")
            return

        # 设置K线数据（增加重试机制）
        success = False
        for attempt in range(3):  # 最多重试3次
            try:
                chart.set(new_data)
                chart.fit()
                success = True
                print(f"数据设置成功: {symbol} {timeframe} (尝试{attempt+1})")
                break
            except Exception as set_error:
                print(f"数据设置失败 (尝试{attempt+1}): {set_error}")
                if attempt < 2:  # 不是最后一次尝试
                    import time
                    time.sleep(0.1 * (attempt + 1))  # 递增延迟

        if not success:
            print(f"所有尝试都失败: {symbol} {timeframe}")
            return







        # 启动实时数据更新
        start_update_thread(chart, symbol)


    except Exception as e:
        print(f"数据加载回调失败: {symbol} {timeframe} - {e}")






    except Exception as e:
        pass


def on_search_failed(chart, symbol, error_message):
    """处理后台搜索失败的回调函数"""

    # 检查是否已经为这个股票代码显示了错误对话框
    global current_error_symbol
    if current_error_symbol == symbol:
        return

    # 标记当前错误股票代码
    current_error_symbol = symbol

    # 显示警告对话框
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Warning)
    msg_box.setWindowTitle("股票代码错误")
    msg_box.setText(f"无法加载股票: {symbol}")

    # 根据错误类型提供不同的提示信息
    if "格式错误" in error_message:
        detailed_info = f"{error_message}\n\n示例格式：\n• 上海证券交易所：600000.SH\n• 深圳证券交易所：000001.SZ"
    elif "不能为空" in error_message:
        detailed_info = f"{error_message}\n\n请输入有效的股票代码"
    else:
        detailed_info = f"{error_message}\n\n请确认：\n• 股票代码拼写正确\n• 股票正常交易\n• 网络连接正常"

    msg_box.setInformativeText(detailed_info)
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.setDefaultButton(QMessageBox.Ok)

    # 设置对话框样式
    msg_box.setStyleSheet("""
        QMessageBox {
            background-color: #f0f0f0;
            font-size: 12px;
        }
        QMessageBox QLabel {
            color: #333333;
            font-size: 12px;
        }
        QMessageBox QPushButton {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 11px;
        }
        QMessageBox QPushButton:hover {
            background-color: #005a9e;
        }
        QMessageBox QPushButton:pressed {
            background-color: #004578;
        }
    """)

    # 显示对话框
    msg_box.exec()

    # 对话框关闭后，清除错误标记，允许下次显示
    current_error_symbol = None


def start_update_thread(chart, symbol):
    print(f"DEBUG: [线程管理] 启动更新线程: {symbol}")

    # 停止现有线程
    if hasattr(chart, 'update_thread'):
        print(f"DEBUG: [线程管理] 停止现有线程: {chart.update_thread.symbol}")
        chart.update_thread.stop()

        # 等待线程完全停止
        if not chart.update_thread.wait(3000):  # 最多等待3秒
            print(f"WARNING: [线程管理] 线程停止超时，强制终止")
            chart.update_thread.terminate()
            chart.update_thread.wait(1000)

        print(f"DEBUG: [线程管理] 旧线程已停止")

    # 创建新线程
    print(f"DEBUG: [线程管理] 创建新线程: {symbol}")
    chart.update_thread = DataUpdateThread(symbol)
    chart.update_thread.data_updated.connect(lambda data: update_chart_data(chart, data))
    chart.update_thread.start()
    print(f"DEBUG: [线程管理] 新线程已启动: {symbol}")


def on_search(chart, searched_string):  # Called when the user searches.
    """启动后台搜索线程，完全避免阻塞主线程"""

    # 首先检查股票代码是否有效
    stock_info = parse_stock_code(searched_string, show_warning=True)
    if stock_info.get('market') == -999:  # 无效代码标记
        print(f"股票代码无效，停止搜索: {searched_string}")
        return

    # 获取旧的股票代码
    old_symbol = chart.topbar['symbol'].value

    # 清除旧股票的SWING和FVG线条缓存
    if old_symbol and old_symbol != searched_string:
        print(f"DEBUG: 单图表切换，清除旧股票的SWING和FVG线条缓存: {old_symbol}")
        clear_all_swing_fvg_lines_for_symbol(old_symbol)

    # 停止现有的搜索线程（如果存在）
    if hasattr(chart, 'search_thread') and chart.search_thread.isRunning():
        chart.search_thread.terminate()
        chart.search_thread.wait()

    # 停止现有的更新线程
    if hasattr(chart, 'update_thread'):
        chart.update_thread.stop()
        chart.update_thread.wait(2000)  # 最多等待2秒

    # 获取当前时间周期
    current_timeframe = chart.topbar['timeframe'].value

    # 创建并启动后台搜索线程
    chart.search_thread = StockSearchThread(searched_string, current_timeframe, chart)

    # 连接分阶段信号
    chart.search_thread.data_loaded.connect(
        lambda symbol, timeframe, data: on_data_loaded(chart, symbol, timeframe, data)
    )

    chart.search_thread.search_failed.connect(
        lambda symbol, error: on_search_failed(chart, symbol, error)
    )

    chart.search_thread.start()



# 原来的搜索函数已移到ChartTab类中





def on_timeframe_selection(chart):
    """最简化的时间周期切换函数"""
    timeframe = chart.topbar['timeframe'].value
    symbol = chart.topbar['symbol'].value

    # 获取数据
    cache_key = f"{symbol}_{timeframe}"
    global history_cache
    if cache_key in history_cache:
        df = history_cache[cache_key].copy()
    else:
        if timeframe == '1min':
            df = fetch_minute_data(symbol)
        elif timeframe == '5min':
            df = fetch_5min_data(symbol)
        elif timeframe == '15min':
            df = fetch_15min_data(symbol)
        elif timeframe == '60min':
            df = fetch_60min_data(symbol)
        elif timeframe == '1day':
            df = fetch_daily_data(symbol)
        else:
            return
        history_cache[cache_key] = df.copy()

    # 增强的数据设置逻辑
    if df is None or len(df) == 0:
        print(f"警告: {symbol} {timeframe} 数据为空")
        return

    try:
        # 验证数据格式
        required_columns = ['time', 'open', 'high', 'low', 'close']
        if not all(col in df.columns for col in required_columns):
            print(f"错误: {symbol} {timeframe} 数据格式不正确")
            return

        chart.set(df)
        chart.fit()
        print(f"成功设置 {symbol} {timeframe} 数据: {len(df)}条")

        # 对15分钟和60分钟图表应用SWING和FVG支撑阻力分析
        if timeframe in ['15min', '60min']:
            print(f"DEBUG: 时间周期切换到{timeframe}，开始应用SWING和FVG分析: {symbol}")
            try:
                apply_swing_fvg_analysis_to_15min_chart(chart, symbol, df)
            except Exception as analysis_error:
                print(f"ERROR: {timeframe}SWING和FVG分析失败: {symbol} - {analysis_error}")
                import traceback
                traceback.print_exc()

    except Exception as e:
        print(f"数据设置失败 {symbol} {timeframe}: {e}")
        # 重试一次
        try:
            import time
            time.sleep(0.1)  # 短暂延迟
            chart.set(df)
            chart.fit()
            print(f"重试成功 {symbol} {timeframe}")

            # 重试成功后，对15分钟和60分钟图表应用SWING和FVG分析
            if timeframe in ['15min', '60min']:
                print(f"DEBUG: 重试成功后，对{timeframe}图表应用SWING和FVG分析: {symbol}")
                try:
                    apply_swing_fvg_analysis_to_15min_chart(chart, symbol, df)
                except Exception as analysis_error:
                    print(f"ERROR: {timeframe}SWING和FVG分析失败: {symbol} - {analysis_error}")
                    import traceback
                    traceback.print_exc()

        except Exception as retry_e:
            print(f"重试也失败 {symbol} {timeframe}: {retry_e}")







def update_chart_data(chart, new_1min_data):
    """更新图表数据的回调函数 - 基于1分钟实时数据更新各时间周期图表"""
    global history_cache

    print(f"DEBUG: [图表更新] 接收到实时数据信号")
    print(f"DEBUG: [图表更新] 数据类型: {type(new_1min_data)}")
    print(f"DEBUG: [图表更新] 数据内容: {new_1min_data.to_dict() if hasattr(new_1min_data, 'to_dict') else new_1min_data}")

    try:
        # 数据验证
        if new_1min_data is None:
            print(f"WARNING: [图表更新] 接收到空数据，跳过更新")
            return

        if not hasattr(new_1min_data, 'get') and not hasattr(new_1min_data, '__getitem__'):
            print(f"WARNING: [图表更新] 数据格式无效，跳过更新")
            return

        current_timeframe = chart.topbar['timeframe'].value
        symbol = chart.topbar['symbol'].value

        print(f"DEBUG: [图表更新] 图表信息: {symbol} {current_timeframe}")

        # 验证数据完整性
        required_fields = ['time', 'open', 'high', 'low', 'close', 'volume']
        for field in required_fields:
            if field not in new_1min_data:
                print(f"WARNING: [图表更新] 缺少必要字段 {field}，跳过更新")
                return

        # 判断是否为港股
        stock_info = parse_stock_code(symbol, show_warning=False)
        is_hk_stock = stock_info and stock_info.get('is_hk', False)

        print(f"DEBUG: [图表更新] 股票类型: {'港股' if is_hk_stock else 'A股'}")

        # 检查数据是否属于当前股票（防止线程切换时的数据混乱）
        data_time = new_1min_data.get('time', '')
        if not data_time:
            print(f"WARNING: [图表更新] 数据时间为空，跳过更新")
            return

        # 检查是否为港股多周期实时数据
        data_period = new_1min_data.get('period', None)
        bar_type = new_1min_data.get('bar_type', None)  # 检查是否为两根K线模式

        if is_hk_stock and data_period:
            # 港股多周期实时更新逻辑
            print(f"DEBUG: [图表更新] 港股多周期数据: {data_period}, 图表周期: {current_timeframe}")

            if bar_type:
                print(f"DEBUG: [图表更新] K线类型: {bar_type}")

            # 转换周期格式：1m -> 1min, 5m -> 5min, 15m -> 15min, 60m -> 60min
            period_mapping = {'1m': '1min', '5m': '5min', '15m': '15min', '60m': '60min'}
            data_timeframe = period_mapping.get(data_period, data_period)

            # 只有当数据周期与图表周期匹配时才更新
            if data_timeframe != current_timeframe:
                print(f"DEBUG: [图表更新] 周期不匹配，跳过更新: 数据{data_timeframe} vs 图表{current_timeframe}")
                return

            # 移除period和bar_type字段，构造图表数据（转换为pandas Series）
            chart_data = pd.Series({
                'time': new_1min_data['time'],
                'open': new_1min_data['open'],
                'high': new_1min_data['high'],
                'low': new_1min_data['low'],
                'close': new_1min_data['close'],
                'volume': new_1min_data['volume']
            })

            if bar_type:
                print(f"DEBUG: [图表更新] 港股{bar_type}K线数据匹配，准备更新图表: {chart_data['time']}")
            else:
                print(f"DEBUG: [图表更新] 港股数据匹配，准备更新图表: {chart_data['time']}")
            print(f"DEBUG: [图表更新] 数据类型转换: dict -> pandas.Series")

        else:
            # A股实时更新逻辑（原有逻辑）
            if current_timeframe == '1min':
                # 1分钟周期：直接使用1分钟实时数据
                chart_data = new_1min_data

            elif current_timeframe == '5min':
                # 5分钟周期：基于1分钟数据计算并更新当前5分钟K线
                chart_data = update_5min_realtime(symbol, new_1min_data)
                if chart_data is None:
                    return  # 还没到5分钟边界或更新失败，不更新图表

            elif current_timeframe == '15min':
                # 15分钟周期：基于1分钟数据计算并更新当前15分钟K线
                chart_data = update_15min_realtime(symbol, new_1min_data)
                if chart_data is None:
                    return  # 还没到15分钟边界或更新失败，不更新图表



            elif current_timeframe == '60min':
                # 60分钟周期：基于1分钟数据计算并更新当前60分钟K线
                chart_data = update_60min_realtime(symbol, new_1min_data)
                if chart_data is None:
                    return  # 还没到60分钟边界或更新失败，不更新图表



            elif current_timeframe == '1day':
                # 日线周期：基于1分钟数据计算并更新当前日K线
                chart_data = update_daily_realtime(symbol, new_1min_data)
                if chart_data is None:
                    return  # 还没到日线边界或更新失败，不更新图表
            else:
                return  # 未知时间周期

        # 更新图表数据
        print(f"DEBUG: [图表更新] 开始更新图表: {symbol} {current_timeframe}")
        print(f"DEBUG: [图表更新] 更新数据: {chart_data}")

        try:
            chart.update(chart_data)
            print(f"DEBUG: [图表更新] 图表更新成功: {symbol} {current_timeframe}")

            # 对15分钟和60分钟图表的实时更新应用SWING和FVG分析
            if current_timeframe in ['15min', '60min']:
                print(f"DEBUG: {current_timeframe}实时更新完成，重新应用SWING和FVG分析: {symbol}")
                try:
                    # 获取完整的数据用于分析
                    cache_key = f"{symbol}_{current_timeframe}"
                    global history_cache
                    if cache_key in history_cache:
                        full_data = history_cache[cache_key].copy()
                        apply_swing_fvg_analysis_to_15min_chart(chart, symbol, full_data)
                    else:
                        print(f"WARNING: {current_timeframe}缓存数据不存在，跳过SWING和FVG分析: {symbol}")
                except Exception as analysis_error:
                    print(f"ERROR: {current_timeframe}实时SWING和FVG分析失败: {symbol} - {analysis_error}")
                    import traceback
                    traceback.print_exc()

        except Exception as update_error:
            print(f"ERROR: [图表更新] 图表更新失败: {symbol} {current_timeframe} - {update_error}")
            import traceback
            traceback.print_exc()



        # 如果存在成交量子图，也更新成交量数据
        if hasattr(chart, '_subcharts') and chart._subcharts:
            try:
                # 根据涨跌设置成交量颜色：红涨绿跌
                # 兼容字典和pandas Series两种数据格式
                if isinstance(chart_data, pd.Series):
                    close_price = chart_data['close']
                    open_price = chart_data['open']
                    time_value = chart_data['time']
                    volume_value = chart_data['volume']
                else:
                    close_price = chart_data.get('close', 0)
                    open_price = chart_data.get('open', 0)
                    time_value = chart_data.get('time', '')
                    volume_value = chart_data.get('volume', 0)

                vol_color = 'rgba(255, 82, 82, 0.8)' if close_price >= open_price else 'rgba(54, 207, 113, 0.8)'
                vol_data = pd.Series({
                    'time': time_value,
                    'value': volume_value,
                    'color': vol_color
                })
                chart._subcharts[0]._series[0].update(vol_data)
                print(f"DEBUG: [图表更新] 成交量数据更新成功")
            except Exception as vol_error:
                print(f"WARNING: [图表更新] 成交量数据更新失败: {vol_error}")

    except Exception as e:
        pass


def is_trading_time(dt):
    """检查是否在股票交易时间内"""
    hour = dt.hour
    minute = dt.minute

    # 上午交易时间：9:30-11:30（包含11:30）
    if hour == 9 and minute >= 30:
        return True
    elif hour == 10:
        return True
    elif hour == 11 and minute <= 30:
        return True

    # 下午交易时间：13:01-15:00（注意：13:00不是交易时间，13:01开始）
    elif hour == 13 and minute >= 1:
        return True
    elif hour == 14:
        return True
    elif hour == 15 and minute == 0:
        return True

    return False


def is_valid_5min_boundary(dt):
    """检查是否是有效的5分钟K线边界时间"""
    hour = dt.hour
    minute = dt.minute

    # 上午有效的5分钟边界：9:35, 9:40, 9:45, 9:50, 9:55, 10:00, 10:05, ..., 11:25, 11:30
    if hour == 9 and minute >= 35 and minute % 5 == 0:
        return True
    elif hour == 10 and minute % 5 == 0:
        return True
    elif hour == 11 and minute <= 30 and minute % 5 == 0:
        return True

    # 下午有效的5分钟边界：13:05, 13:10, 13:15, ..., 14:55, 15:00
    # 注意：13:00不是有效边界，下午第一根K线是13:05
    elif hour == 13 and minute >= 5 and minute % 5 == 0:
        return True
    elif hour == 14 and minute % 5 == 0:
        return True
    elif hour == 15 and minute == 0:
        return True

    return False


def get_5min_boundary(dt):
    """获取指定时间所属的5分钟边界时间"""
    from datetime import datetime, timedelta

    hour = dt.hour
    minute = dt.minute

    # 股票交易的5分钟边界：9:35, 9:40, 9:45, 9:50, 9:55, 10:00, 10:05, ..., 11:25, 11:30, 13:05, 13:10, ..., 14:55, 15:00
    valid_5min_boundaries = [
        (9, 35), (9, 40), (9, 45), (9, 50), (9, 55), (10, 0), (10, 5), (10, 10), (10, 15), (10, 20), (10, 25), (10, 30), (10, 35), (10, 40), (10, 45), (10, 50), (10, 55), (11, 0), (11, 5), (11, 10), (11, 15), (11, 20), (11, 25), (11, 30),  # 上午
        (13, 5), (13, 10), (13, 15), (13, 20), (13, 25), (13, 30), (13, 35), (13, 40), (13, 45), (13, 50), (13, 55), (14, 0), (14, 5), (14, 10), (14, 15), (14, 20), (14, 25), (14, 30), (14, 35), (14, 40), (14, 45), (14, 50), (14, 55), (15, 0)  # 下午
    ]

    current_boundary = (hour, minute)

    # 如果当前时间正好是边界时间，直接返回
    if current_boundary in valid_5min_boundaries:
        return dt.replace(second=0, microsecond=0)

    # 找到下一个边界时间
    for boundary_hour, boundary_minute in valid_5min_boundaries:
        if (boundary_hour > hour) or (boundary_hour == hour and boundary_minute > minute):
            return dt.replace(hour=boundary_hour, minute=boundary_minute, second=0, microsecond=0)

    # 如果没有找到当天的边界，返回None（非交易时间）
    if hour < 9 or (hour == 9 and minute < 30):
        return dt.replace(hour=9, minute=35, second=0, microsecond=0)
    elif hour > 15 or (hour == 15 and minute > 0):
        # 下一个交易日的9:35
        next_day = dt + timedelta(days=1)
        return next_day.replace(hour=9, minute=35, second=0, microsecond=0)
    else:
        # 午休时间，下午13:05
        return dt.replace(hour=13, minute=5, second=0, microsecond=0)


def get_trading_session(dt):
    """获取交易时段：'morning', 'afternoon', 'break'"""
    hour = dt.hour
    minute = dt.minute

    # 上午交易时间：9:30-11:30
    if (hour == 9 and minute >= 30) or hour == 10 or (hour == 11 and minute <= 30):
        return 'morning'

    # 下午交易时间：13:00-15:00
    elif hour == 13 or hour == 14 or (hour == 15 and minute == 0):
        return 'afternoon'

    # 其他时间（包括午休）
    else:
        return 'break'


def get_5min_period_start(boundary_time):
    """获取5分钟周期的开始时间"""
    from datetime import datetime, timedelta

    hour = boundary_time.hour
    minute = boundary_time.minute

    # 上午交易时间的5分钟周期
    if hour == 9:
        if minute == 35:  # 第一个5分钟周期：9:30-9:35
            return boundary_time.replace(minute=30)
        elif minute >= 40:  # 其他周期：往前推5分钟
            return boundary_time.replace(minute=minute-5)
    elif hour == 10:
        if minute == 0:  # 9:55-10:00
            return boundary_time.replace(hour=9, minute=55)
        else:  # 其他周期：往前推5分钟
            return boundary_time.replace(minute=minute-5)
    elif hour == 11:
        if minute <= 30:  # 上午最后几个周期
            return boundary_time.replace(minute=minute-5)

    # 下午交易时间的5分钟周期
    elif hour == 13:
        if minute == 5:  # 下午第一个5分钟周期：13:00-13:05
            return boundary_time.replace(minute=0)
        else:  # 其他周期：往前推5分钟
            return boundary_time.replace(minute=minute-5)
    elif hour == 14:
        return boundary_time.replace(minute=minute-5)
    elif hour == 15 and minute == 0:  # 最后一个周期：14:55-15:00
        return boundary_time.replace(hour=14, minute=55)

    # 默认往前推5分钟
    return boundary_time - timedelta(minutes=5)


def get_15min_boundary(dt):
    """获取指定时间所属的15分钟边界时间"""
    from datetime import datetime

    hour = dt.hour
    minute = dt.minute

    # 股票交易的15分钟边界：9:45, 10:00, 10:15, 10:30, 10:45, 11:00, 11:15, 11:30, 13:15, 13:30, 13:45, 14:00, 14:15, 14:30, 14:45, 15:00
    valid_15min_boundaries = [
        (9, 45), (10, 0), (10, 15), (10, 30), (10, 45), (11, 0), (11, 15), (11, 30),  # 上午
        (13, 15), (13, 30), (13, 45), (14, 0), (14, 15), (14, 30), (14, 45), (15, 0)  # 下午
    ]

    current_boundary = (hour, minute)

    # 如果当前时间正好是边界时间，直接返回
    if current_boundary in valid_15min_boundaries:
        return dt.replace(second=0, microsecond=0)

    # 找到下一个边界时间
    for boundary_hour, boundary_minute in valid_15min_boundaries:
        if (boundary_hour > hour) or (boundary_hour == hour and boundary_minute > minute):
            return dt.replace(hour=boundary_hour, minute=boundary_minute, second=0, microsecond=0)

    # 如果没有找到当天的边界，返回None（非交易时间）
    return None


def get_15min_period_start(boundary_time):
    """获取15分钟周期的开始时间"""
    from datetime import datetime, timedelta

    hour = boundary_time.hour
    minute = boundary_time.minute

    # 上午交易时间的15分钟周期
    if hour == 9 and minute == 45:  # 第一个15分钟周期：9:30-9:45
        return boundary_time.replace(minute=30)
    elif hour == 10 and minute == 0:  # 9:45-10:00
        return boundary_time.replace(hour=9, minute=45)
    elif hour == 10 and minute == 15:  # 10:00-10:15
        return boundary_time.replace(minute=0)
    elif hour == 10 and minute == 30:  # 10:15-10:30
        return boundary_time.replace(minute=15)
    elif hour == 10 and minute == 45:  # 10:30-10:45
        return boundary_time.replace(minute=30)
    elif hour == 11 and minute == 0:  # 10:45-11:00
        return boundary_time.replace(hour=10, minute=45)
    elif hour == 11 and minute == 15:  # 11:00-11:15
        return boundary_time.replace(minute=0)
    elif hour == 11 and minute == 30:  # 11:15-11:30
        return boundary_time.replace(minute=15)

    # 下午交易时间的15分钟周期
    elif hour == 13 and minute == 15:  # 下午第一个15分钟周期：13:00-13:15
        return boundary_time.replace(minute=0)
    elif hour == 13 and minute == 30:  # 13:15-13:30
        return boundary_time.replace(minute=15)
    elif hour == 13 and minute == 45:  # 13:30-13:45
        return boundary_time.replace(minute=30)
    elif hour == 14 and minute == 0:  # 13:45-14:00
        return boundary_time.replace(hour=13, minute=45)
    elif hour == 14 and minute == 15:  # 14:00-14:15
        return boundary_time.replace(minute=0)
    elif hour == 14 and minute == 30:  # 14:15-14:30
        return boundary_time.replace(minute=15)
    elif hour == 14 and minute == 45:  # 14:30-14:45
        return boundary_time.replace(minute=30)
    elif hour == 15 and minute == 0:  # 14:45-15:00
        return boundary_time.replace(hour=14, minute=45)

    # 默认往前推15分钟
    return boundary_time - timedelta(minutes=15)


def get_60min_boundary(dt):
    """获取指定时间所属的60分钟边界时间"""
    from datetime import datetime

    hour = dt.hour
    minute = dt.minute

    # 严格检查：15:00之后（包括15:00）的所有时间点都返回None
    if hour > 15 or (hour == 15 and minute >= 0):
        return None

    # 股票交易的60分钟边界：10:30, 11:30, 14:00
    valid_60min_boundaries = [
        (10, 30), (11, 30), (14, 0)
    ]

    # 找到当前时间所属的下一个或当前边界
    for boundary_hour, boundary_minute in valid_60min_boundaries:
        # 如果当前时间小于或等于边界时间，则该边界就是目标
        if (hour < boundary_hour) or (hour == boundary_hour and minute <= boundary_minute):
            return dt.replace(hour=boundary_hour, minute=boundary_minute, second=0, microsecond=0)

    # 如果循环结束仍未找到，说明时间在当天所有边界之后（理论上不会执行到这里，因为已被前面的逻辑覆盖）
    return None


def get_60min_period_start(boundary_time):
    """获取60分钟周期的开始时间"""
    from datetime import datetime, timedelta

    hour = boundary_time.hour
    minute = boundary_time.minute

    # 60分钟周期的开始时间
    if hour == 10 and minute == 30:  # 第一个60分钟周期：9:30-10:30
        return boundary_time.replace(hour=9, minute=30)
    elif hour == 11 and minute == 30:  # 第二个60分钟周期：10:30-11:30
        return boundary_time.replace(hour=10, minute=30)
    elif hour == 14 and minute == 0:  # 第三个60分钟周期：13:00-14:00
        return boundary_time.replace(hour=13, minute=0)
    elif hour == 15 and minute == 0:  # 第四个60分钟周期：14:00-15:00
        return boundary_time.replace(hour=14, minute=0)

    # 默认往前推60分钟
    return boundary_time - timedelta(minutes=60)


def check_and_update_closed_15min_bars(symbol, current_time):
    """检查并更新已闭合的15分钟K线 - 使用pytdx直接获取闭合数据"""
    from datetime import datetime, timedelta
    global history_cache

    # 15分钟边界时间点
    valid_15min_boundaries = [
        (9, 45), (10, 0), (10, 15), (10, 30), (10, 45), (11, 0), (11, 15), (11, 30),  # 上午
        (13, 15), (13, 30), (13, 45), (14, 0), (14, 15), (14, 30), (14, 45), (15, 0)  # 下午
    ]

    cache_key_15min = f"{symbol}_15min"
    if cache_key_15min not in history_cache:
        return False

    cached_15min_df = history_cache[cache_key_15min].copy()
    if len(cached_15min_df) == 0:
        return False

    # 获取最后一根15分钟K线的时间
    last_15min_time_str = cached_15min_df.iloc[-1]['time']
    last_15min_time = datetime.strptime(last_15min_time_str, '%Y-%m-%d %H:%M')

    # 检查是否有已闭合但未更新的15分钟K线
    updated = False
    for hour, minute in valid_15min_boundaries:
        boundary_time = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)

        # 如果边界时间已经过去，且比最后一根K线时间新，说明这根K线已闭合但未更新
        if boundary_time > last_15min_time and current_time > boundary_time:

            # 使用pytdx获取这根闭合的15分钟K线
            try:
                # 获取最新的15分钟数据（只需要最后几根）
                latest_15min_data = fetch_15min_data(symbol, count=5)  # 获取最后5根

                if latest_15min_data is not None and len(latest_15min_data) > 0:
                    # 找到对应时间的K线
                    for _, row in latest_15min_data.iterrows():
                        row_time = datetime.strptime(row['time'], '%Y-%m-%d %H:%M')
                        if row_time == boundary_time:
                            # 更新缓存中的这根K线
                            mask = cached_15min_df['time'] == boundary_time.strftime('%Y-%m-%d %H:%M')
                            if mask.any():
                                # 更新现有K线
                                cached_15min_df.loc[mask, ['open', 'high', 'low', 'close', 'volume']] = [
                                    row['open'], row['high'], row['low'], row['close'], row['volume']
                                ]
                            else:
                                # 添加新的K线
                                new_row = pd.DataFrame([{
                                    'time': boundary_time.strftime('%Y-%m-%d %H:%M'),
                                    'open': row['open'],
                                    'high': row['high'],
                                    'low': row['low'],
                                    'close': row['close'],
                                    'volume': row['volume']
                                }])
                                cached_15min_df = pd.concat([cached_15min_df, new_row], ignore_index=True)

                            updated = True
                            break

            except Exception as e:
                pass

    if updated:
        # 更新缓存
        history_cache[cache_key_15min] = cached_15min_df

    return updated


def check_and_update_closed_5min_bars(symbol, current_time):
    """检查并更新已闭合的5分钟K线 - 使用pytdx直接获取闭合数据"""
    from datetime import datetime, timedelta
    global history_cache

    cache_key_5min = f"{symbol}_5min"
    if cache_key_5min not in history_cache:
        return False

    cached_5min_df = history_cache[cache_key_5min].copy()
    if len(cached_5min_df) == 0:
        return False

    # 获取最后一根5分钟K线的时间
    last_5min_time_str = cached_5min_df.iloc[-1]['time']
    last_5min_time = datetime.strptime(last_5min_time_str, '%Y-%m-%d %H:%M')

    # 5分钟边界：每5分钟一个边界点
    updated = False

    # 计算从最后一根K线到当前时间之间可能闭合的5分钟边界
    check_time = last_5min_time + timedelta(minutes=5)
    while check_time <= current_time - timedelta(minutes=5):  # 至少过去5分钟才算闭合
        # 检查这个时间点是否在交易时间内
        if is_trading_time(check_time):

            # 使用pytdx获取这根闭合的5分钟K线
            try:
                latest_5min_data = fetch_5min_data(symbol, count=5)  # 获取最后5根

                if latest_5min_data is not None and len(latest_5min_data) > 0:
                    # 找到对应时间的K线
                    for _, row in latest_5min_data.iterrows():
                        row_time = datetime.strptime(row['time'], '%Y-%m-%d %H:%M')
                        if row_time == check_time:
                            # 添加新的K线
                            new_row = pd.DataFrame([{
                                'time': check_time.strftime('%Y-%m-%d %H:%M'),
                                'open': row['open'],
                                'high': row['high'],
                                'low': row['low'],
                                'close': row['close'],
                                'volume': row['volume']
                            }])
                            cached_5min_df = pd.concat([cached_5min_df, new_row], ignore_index=True)
                            updated = True
                            break

            except Exception as e:
                pass

        check_time += timedelta(minutes=5)

    if updated:
        # 更新缓存
        history_cache[cache_key_5min] = cached_5min_df

    return updated


def update_5min_realtime(symbol, new_1min_data):
    """更新5分钟实时数据 - 混合策略：闭合K线用pytdx数据，非闭合K线用1分钟数据合成"""
    from datetime import datetime
    global history_cache

    current_time = datetime.strptime(new_1min_data['time'], '%Y-%m-%d %H:%M')

    # 首先检查是否有已闭合的5分钟K线需要更新（不受交易时间限制）
    closed_bar_updated = check_and_update_closed_5min_bars(symbol, current_time)
    if closed_bar_updated:
        pass

    # 检查是否在交易时间内（只影响未闭合K线的实时合成）
    if not is_trading_time(current_time):
        return None

    # 获取1分钟缓存数据用于合成5分钟K线
    cache_key_1min = f"{symbol}_1min"
    if cache_key_1min not in history_cache:
        return None

    cached_1min_df = history_cache[cache_key_1min].copy()
    if len(cached_1min_df) == 0:
        return None

    # 计算当前1分钟数据应该属于哪个5分钟周期
    current_5min_boundary = get_5min_boundary(current_time)
    if current_5min_boundary is None:
        return None

    # 获取5分钟缓存数据
    cache_key_5min = f"{symbol}_5min"
    if cache_key_5min not in history_cache:
        return None

    cached_5min_df = history_cache[cache_key_5min].copy()
    if len(cached_5min_df) == 0:
        return None

    # 获取最新一根5分钟K线的时间
    last_5min_time_str = cached_5min_df.iloc[-1]['time']
    last_5min_time = datetime.strptime(last_5min_time_str, '%Y-%m-%d %H:%M')

    boundary_time_str = current_5min_boundary.strftime('%Y-%m-%d %H:%M')

    # 计算5分钟周期的开始时间
    start_time = get_5min_period_start(current_5min_boundary)
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M')

    # 从1分钟数据中筛选出当前5分钟周期内的所有数据
    period_1min_data = cached_1min_df[
        (cached_1min_df['time'] > start_time_str) &
        (cached_1min_df['time'] <= boundary_time_str)
    ].copy()

    if len(period_1min_data) == 0:
        return None

    # 合成5分钟K线数据
    synthesized_5min_bar = {
        'time': boundary_time_str,
        'open': period_1min_data.iloc[0]['open'],  # 第一根1分钟K线的开盘价
        'high': period_1min_data['high'].max(),    # 周期内最高价
        'low': period_1min_data['low'].min(),      # 周期内最低价
        'close': period_1min_data.iloc[-1]['close'],  # 最后一根1分钟K线的收盘价
        'volume': period_1min_data['volume'].sum()     # 周期内成交量总和
    }


    # 如果当前5分钟边界等于最新5分钟K线时间，则更新该K线
    if current_5min_boundary == last_5min_time:
        # 更新最新一根5分钟K线
        cached_5min_df.iloc[-1, cached_5min_df.columns.get_loc('open')] = synthesized_5min_bar['open']
        cached_5min_df.iloc[-1, cached_5min_df.columns.get_loc('high')] = synthesized_5min_bar['high']
        cached_5min_df.iloc[-1, cached_5min_df.columns.get_loc('low')] = synthesized_5min_bar['low']
        cached_5min_df.iloc[-1, cached_5min_df.columns.get_loc('close')] = synthesized_5min_bar['close']
        cached_5min_df.iloc[-1, cached_5min_df.columns.get_loc('volume')] = synthesized_5min_bar['volume']

        # 更新缓存
        history_cache[cache_key_5min] = cached_5min_df

        # 返回更新后的最新5分钟K线数据
        latest_5min_bar = cached_5min_df.iloc[-1]
        return latest_5min_bar
    else:
        # 如果是新的5分钟周期，需要创建新的5分钟K线
        if current_5min_boundary > last_5min_time:
            # 添加新的5分钟K线
            new_row = pd.DataFrame([synthesized_5min_bar])
            cached_5min_df = pd.concat([cached_5min_df, new_row], ignore_index=True)

            # 保持数据量限制
            if len(cached_5min_df) > FIVE_MINUTE_CYCLE_COUNT:
                cached_5min_df = cached_5min_df.tail(FIVE_MINUTE_CYCLE_COUNT)

            # 更新缓存
            history_cache[cache_key_5min] = cached_5min_df

            return pd.Series(synthesized_5min_bar)
        else:
            return None




def update_15min_realtime(symbol, new_1min_data):
    """更新15分钟实时数据 - 混合策略：闭合K线用pytdx数据，非闭合K线用1分钟数据合成"""
    from datetime import datetime, timedelta
    global history_cache

    current_time = datetime.strptime(new_1min_data['time'], '%Y-%m-%d %H:%M')

    # 首先检查是否有已闭合的15分钟K线需要更新（不受交易时间限制）
    closed_bar_updated = check_and_update_closed_15min_bars(symbol, current_time)
    if closed_bar_updated:
        pass

    # 检查是否在交易时间内（只影响未闭合K线的实时合成）
    if not is_trading_time(current_time):
        return None

    # 继续处理当前未闭合的15分钟K线（用1分钟数据合成）

    # 计算当前时间应该属于哪个15分钟周期
    current_15min_boundary = get_15min_boundary(current_time)
    if current_15min_boundary is None:
        return None

    # 检查当前15分钟周期是否已经闭合
    if current_time >= current_15min_boundary:
        return None  # 已闭合的K线应该由上面的check_and_update_closed_15min_bars处理


    # 获取1分钟缓存数据用于合成15分钟K线
    cache_key_1min = f"{symbol}_1min"
    if cache_key_1min not in history_cache:
        return None

    cached_1min_df = history_cache[cache_key_1min].copy()
    if len(cached_1min_df) == 0:
        return None

    # 获取15分钟缓存数据
    cache_key_15min = f"{symbol}_15min"
    if cache_key_15min not in history_cache:
        return None

    cached_15min_df = history_cache[cache_key_15min].copy()
    if len(cached_15min_df) == 0:
        return None

    # 获取最新一根15分钟K线的时间
    last_15min_time_str = cached_15min_df.iloc[-1]['time']
    last_15min_time = datetime.strptime(last_15min_time_str, '%Y-%m-%d %H:%M')

    boundary_time_str = current_15min_boundary.strftime('%Y-%m-%d %H:%M')

    # 计算15分钟周期的开始时间
    start_time = get_15min_period_start(current_15min_boundary)
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M')

    # 从1分钟数据中筛选出当前15分钟周期内的所有数据
    period_1min_data = cached_1min_df[
        (cached_1min_df['time'] > start_time_str) &
        (cached_1min_df['time'] <= boundary_time_str)
    ].copy()

    if len(period_1min_data) == 0:
        return None

    # 合成15分钟K线数据
    synthesized_15min_bar = {
        'time': boundary_time_str,
        'open': period_1min_data.iloc[0]['open'],  # 第一根1分钟K线的开盘价
        'high': period_1min_data['high'].max(),    # 周期内最高价
        'low': period_1min_data['low'].min(),      # 周期内最低价
        'close': period_1min_data.iloc[-1]['close'],  # 最后一根1分钟K线的收盘价
        'volume': period_1min_data['volume'].sum()     # 周期内成交量总和
    }


    # 如果当前15分钟边界等于最新15分钟K线时间，则更新该K线
    if current_15min_boundary == last_15min_time:
        # 更新最新一根15分钟K线
        cached_15min_df.iloc[-1, cached_15min_df.columns.get_loc('open')] = synthesized_15min_bar['open']
        cached_15min_df.iloc[-1, cached_15min_df.columns.get_loc('high')] = synthesized_15min_bar['high']
        cached_15min_df.iloc[-1, cached_15min_df.columns.get_loc('low')] = synthesized_15min_bar['low']
        cached_15min_df.iloc[-1, cached_15min_df.columns.get_loc('close')] = synthesized_15min_bar['close']
        cached_15min_df.iloc[-1, cached_15min_df.columns.get_loc('volume')] = synthesized_15min_bar['volume']

        # 更新缓存
        history_cache[cache_key_15min] = cached_15min_df



        # 返回更新后的最新15分钟K线数据
        latest_15min_bar = cached_15min_df.iloc[-1]
        return latest_15min_bar
    else:
        # 如果是新的15分钟周期，需要创建新的15分钟K线
        if current_15min_boundary > last_15min_time:
            # 添加新的15分钟K线
            new_row = pd.DataFrame([synthesized_15min_bar])
            cached_15min_df = pd.concat([cached_15min_df, new_row], ignore_index=True)

            # 保持数据量限制
            if len(cached_15min_df) > FIFTEEN_MINUTE_CYCLE_COUNT:
                cached_15min_df = cached_15min_df.tail(FIFTEEN_MINUTE_CYCLE_COUNT)

            # 更新缓存
            history_cache[cache_key_15min] = cached_15min_df



            return pd.Series(synthesized_15min_bar)
        else:
            return None




def check_and_update_closed_60min_bars(symbol, current_time):
    """检查并更新已闭合的60分钟K线 - 使用pytdx直接获取闭合数据"""
    from datetime import datetime, timedelta
    global history_cache

    # 60分钟边界时间点：10:30, 11:30, 14:00, 15:00
    valid_60min_boundaries = [
        (10, 30), (11, 30), (14, 0), (15, 0)
    ]

    cache_key_60min = f"{symbol}_60min"
    if cache_key_60min not in history_cache:
        return False

    cached_60min_df = history_cache[cache_key_60min].copy()
    if len(cached_60min_df) == 0:
        return False

    # 获取最后一根60分钟K线的时间
    last_60min_time_str = cached_60min_df.iloc[-1]['time']
    last_60min_time = datetime.strptime(last_60min_time_str, '%Y-%m-%d %H:%M')

    # 检查是否有已闭合但未更新的60分钟K线
    updated = False
    for hour, minute in valid_60min_boundaries:
        boundary_time = current_time.replace(hour=hour, minute=minute, second=0, microsecond=0)

        # 如果边界时间已经过去，且比最后一根K线时间新，说明这根K线已闭合但未更新
        if boundary_time > last_60min_time and current_time > boundary_time:

            # 使用pytdx获取这根闭合的60分钟K线
            try:
                latest_60min_data = fetch_60min_data(symbol, count=5)  # 获取最后5根

                if latest_60min_data is not None and len(latest_60min_data) > 0:
                    # 找到对应时间的K线
                    for _, row in latest_60min_data.iterrows():
                        row_time = datetime.strptime(row['time'], '%Y-%m-%d %H:%M')
                        if row_time == boundary_time:
                            # 更新缓存中的这根K线
                            mask = cached_60min_df['time'] == boundary_time.strftime('%Y-%m-%d %H:%M')
                            if mask.any():
                                # 更新现有K线
                                cached_60min_df.loc[mask, ['open', 'high', 'low', 'close', 'volume']] = [
                                    row['open'], row['high'], row['low'], row['close'], row['volume']
                                ]
                            else:
                                # 添加新的K线
                                new_row = pd.DataFrame([{
                                    'time': boundary_time.strftime('%Y-%m-%d %H:%M'),
                                    'open': row['open'],
                                    'high': row['high'],
                                    'low': row['low'],
                                    'close': row['close'],
                                    'volume': row['volume']
                                }])
                                cached_60min_df = pd.concat([cached_60min_df, new_row], ignore_index=True)

                            updated = True
                            break

            except Exception as e:
                pass

    if updated:
        # 更新缓存
        history_cache[cache_key_60min] = cached_60min_df

    return updated


def update_60min_realtime(symbol, new_1min_data):
    """更新60分钟实时数据 - 混合策略：闭合K线用pytdx数据，非闭合K线用1分钟数据合成"""
    from datetime import datetime, timedelta
    global history_cache

    current_time = datetime.strptime(new_1min_data['time'], '%Y-%m-%d %H:%M')

    # 首先检查是否有已闭合的60分钟K线需要更新（不受交易时间限制）
    closed_bar_updated = check_and_update_closed_60min_bars(symbol, current_time)
    if closed_bar_updated:
        pass

    # 检查是否在交易时间内（只影响未闭合K线的实时合成）
    if not is_trading_time(current_time):
        return None

    # 获取1分钟缓存数据用于合成60分钟K线
    cache_key_1min = f"{symbol}_1min"
    if cache_key_1min not in history_cache:
        return None

    cached_1min_df = history_cache[cache_key_1min].copy()
    if len(cached_1min_df) == 0:
        return None

    # 计算当前1分钟数据应该属于哪个60分钟周期
    current_60min_boundary = get_60min_boundary(current_time)
    if current_60min_boundary is None:
        return None

    # 额外验证：确保边界时间是有效的60分钟时间点
    boundary_hour = current_60min_boundary.hour
    boundary_minute = current_60min_boundary.minute
    valid_boundaries = [(10, 30), (11, 30), (14, 0), (15, 0)]
    if (boundary_hour, boundary_minute) not in valid_boundaries:
        return None

    # 获取60分钟缓存数据
    cache_key_60min = f"{symbol}_60min"
    if cache_key_60min not in history_cache:
        return None

    cached_60min_df = history_cache[cache_key_60min].copy()
    if len(cached_60min_df) == 0:
        return None

    # 获取最新一根60分钟K线的时间
    last_60min_time_str = cached_60min_df.iloc[-1]['time']
    last_60min_time = datetime.strptime(last_60min_time_str, '%Y-%m-%d %H:%M')

    boundary_time_str = current_60min_boundary.strftime('%Y-%m-%d %H:%M')

    # 计算60分钟周期的开始时间
    start_time = get_60min_period_start(current_60min_boundary)
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M')

    # 从1分钟数据中筛选出当前60分钟周期内的所有数据
    period_1min_data = cached_1min_df[
        (cached_1min_df['time'] > start_time_str) &
        (cached_1min_df['time'] <= boundary_time_str)
    ].copy()

    if len(period_1min_data) == 0:
        return None

    # 最终验证：确保生成的60分钟K线时间是有效的
    boundary_time_parts = boundary_time_str.split(' ')[1]  # 获取时间部分 HH:MM
    if boundary_time_parts not in ['10:30', '11:30', '14:00', '15:00']:
        return None

    # 合成60分钟K线数据
    synthesized_60min_bar = {
        'time': boundary_time_str,
        'open': period_1min_data.iloc[0]['open'],  # 第一根1分钟K线的开盘价
        'high': period_1min_data['high'].max(),    # 周期内最高价
        'low': period_1min_data['low'].min(),      # 周期内最低价
        'close': period_1min_data.iloc[-1]['close'],  # 最后一根1分钟K线的收盘价
        'volume': period_1min_data['volume'].sum()     # 周期内成交量总和
    }


    # 如果当前60分钟边界等于最新60分钟K线时间，则更新该K线
    if current_60min_boundary == last_60min_time:
        # 更新最新一根60分钟K线
        cached_60min_df.iloc[-1, cached_60min_df.columns.get_loc('open')] = synthesized_60min_bar['open']
        cached_60min_df.iloc[-1, cached_60min_df.columns.get_loc('high')] = synthesized_60min_bar['high']
        cached_60min_df.iloc[-1, cached_60min_df.columns.get_loc('low')] = synthesized_60min_bar['low']
        cached_60min_df.iloc[-1, cached_60min_df.columns.get_loc('close')] = synthesized_60min_bar['close']
        cached_60min_df.iloc[-1, cached_60min_df.columns.get_loc('volume')] = synthesized_60min_bar['volume']

        # 更新缓存
        history_cache[cache_key_60min] = cached_60min_df



        # 返回更新后的最新60分钟K线数据
        latest_60min_bar = cached_60min_df.iloc[-1]



        return latest_60min_bar
    else:
        # 如果是新的60分钟周期，需要创建新的60分钟K线
        if current_60min_boundary > last_60min_time:
            # 添加新的60分钟K线
            new_row = pd.DataFrame([synthesized_60min_bar])
            cached_60min_df = pd.concat([cached_60min_df, new_row], ignore_index=True)

            # 保持数据量限制
            if len(cached_60min_df) > SIXTY_MINUTE_CYCLE_COUNT:
                cached_60min_df = cached_60min_df.tail(SIXTY_MINUTE_CYCLE_COUNT)

            # 更新缓存
            history_cache[cache_key_60min] = cached_60min_df



            return pd.Series(synthesized_60min_bar)
        else:
            return None




def update_daily_realtime(symbol, new_1min_data):
    """更新日线实时数据 - 基于最新1分钟数据计算并更新当前日K线"""
    from datetime import datetime
    global history_cache

    current_time = datetime.strptime(new_1min_data['time'], '%Y-%m-%d %H:%M')

    # 检查是否在交易时间内
    if not is_trading_time(current_time):
        return None

    # 获取1分钟缓存数据用于合成日K线
    cache_key_1min = f"{symbol}_1min"
    if cache_key_1min not in history_cache:
        return None

    cached_1min_df = history_cache[cache_key_1min].copy()
    if len(cached_1min_df) == 0:
        return None

    # 获取日线缓存数据
    cache_key_daily = f"{symbol}_1day"
    if cache_key_daily not in history_cache:
        return None

    cached_daily_df = history_cache[cache_key_daily].copy()
    if len(cached_daily_df) == 0:
        return None

    # 获取当前日期
    current_date = current_time.strftime('%Y-%m-%d')

    # 获取最新一根日K线的时间
    last_daily_time_str = cached_daily_df.iloc[-1]['time']


    # 基于1分钟数据合成日K线
    # 找到当天的开始时间（9:30）
    today_start = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
    today_start_str = today_start.strftime('%Y-%m-%d %H:%M')

    # 从1分钟数据中筛选出当天的所有数据
    today_1min_data = cached_1min_df[
        (cached_1min_df['time'] >= today_start_str) &
        (cached_1min_df['time'].str.startswith(current_date))
    ].copy()

    if len(today_1min_data) == 0:
        return None

    # 合成日K线数据
    synthesized_daily_bar = {
        'time': current_date,
        'open': today_1min_data.iloc[0]['open'],  # 当天第一根1分钟K线的开盘价
        'high': today_1min_data['high'].max(),    # 当天最高价
        'low': today_1min_data['low'].min(),      # 当天最低价
        'close': today_1min_data.iloc[-1]['close'],  # 当天最后一根1分钟K线的收盘价
        'volume': today_1min_data['volume'].sum()     # 当天成交量总和
    }


    # 如果当前日期等于最新日K线时间，则更新该K线
    if current_date == last_daily_time_str:
        # 更新最新一根日K线
        cached_daily_df.iloc[-1, cached_daily_df.columns.get_loc('open')] = synthesized_daily_bar['open']
        cached_daily_df.iloc[-1, cached_daily_df.columns.get_loc('high')] = synthesized_daily_bar['high']
        cached_daily_df.iloc[-1, cached_daily_df.columns.get_loc('low')] = synthesized_daily_bar['low']
        cached_daily_df.iloc[-1, cached_daily_df.columns.get_loc('close')] = synthesized_daily_bar['close']
        cached_daily_df.iloc[-1, cached_daily_df.columns.get_loc('volume')] = synthesized_daily_bar['volume']

        # 更新缓存
        history_cache[cache_key_daily] = cached_daily_df

        # 返回更新后的最新日K线数据
        latest_daily_bar = cached_daily_df.iloc[-1]
        return latest_daily_bar
    else:
        # 如果是新的交易日，需要创建新的日K线
        if current_date > last_daily_time_str:
            # 添加新的日K线
            new_row = pd.DataFrame([synthesized_daily_bar])
            cached_daily_df = pd.concat([cached_daily_df, new_row], ignore_index=True)

            # 保持数据量限制
            if len(cached_daily_df) > DAILY_CYCLE_COUNT:
                cached_daily_df = cached_daily_df.tail(DAILY_CYCLE_COUNT)

            # 更新缓存
            history_cache[cache_key_daily] = cached_daily_df

            return pd.Series(synthesized_daily_bar)
        else:
            return None


def diagnose_kline_data(symbol, timeframe, df):
    """诊断K线数据质量"""
    print(f"DEBUG: ===== 数据诊断开始: {symbol} {timeframe} =====")
    print(f"DEBUG: 原始数据量: {len(df)}")
    print(f"DEBUG: 数据列: {df.columns.tolist()}")

    if len(df) > 0:
        print(f"DEBUG: 时间范围: {df.index[0]} 到 {df.index[-1]}")
        print(f"DEBUG: 价格范围: {df['close'].min():.3f} - {df['close'].max():.3f}")
        print(f"DEBUG: 成交量范围: {df['volume'].min():.0f} - {df['volume'].max():.0f}")

        # 检查重复数据
        duplicates = df.duplicated().sum()
        print(f"DEBUG: 重复行数: {duplicates}")

        # 检查NaN数据
        nan_counts = df.isnull().sum()
        print(f"DEBUG: NaN统计: {nan_counts.to_dict()}")

        # 检查价格变化
        price_changes = df['close'].diff().abs()
        zero_changes = (price_changes == 0).sum()
        print(f"DEBUG: 价格无变化的K线数: {zero_changes}/{len(df)} ({zero_changes/len(df)*100:.1f}%)")

        # 检查成交量
        zero_volume = (df['volume'] == 0).sum()
        print(f"DEBUG: 零成交量K线数: {zero_volume}/{len(df)} ({zero_volume/len(df)*100:.1f}%)")

        # 打印前20根和后20根K线的详细数据
        print(f"DEBUG: ===== 前20根K线数据 =====")
        for i in range(min(20, len(df))):
            row = df.iloc[i]
            print(f"DEBUG: K线{i+1:2d}: 时间={row.get('time', 'N/A')}, O={row['open']:.3f}, H={row['high']:.3f}, L={row['low']:.3f}, C={row['close']:.3f}, V={row['volume']:.0f}")

        if len(df) > 40:
            print(f"DEBUG: ===== ... 中间省略 {len(df)-40} 根K线 ... =====")

        print(f"DEBUG: ===== 后20根K线数据 =====")
        start_idx = max(0, len(df) - 20)
        for i in range(start_idx, len(df)):
            row = df.iloc[i]
            print(f"DEBUG: K线{i+1:2d}: 时间={row.get('time', 'N/A')}, O={row['open']:.3f}, H={row['high']:.3f}, L={row['low']:.3f}, C={row['close']:.3f}, V={row['volume']:.0f}")

    print(f"DEBUG: ===== 数据诊断结束: {symbol} {timeframe} =====")


















































# 将K线图页面添加到stacked_widget
stacked_widget.addWidget(tab_manager)        # 索引 0: 多标签页K线图

# 创建切换按钮
def create_switch_button(text, page_index):
    """创建切换按钮"""
    button = QPushButton(text)
    button.setStyleSheet("""
        QPushButton {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            margin: 2px;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: #e0e0e0;
        }
        QPushButton:pressed {
            background-color: #d0d0d0;
        }
        QPushButton:checked {
            background-color: #007acc;
            color: white;
        }
    """)
    button.setCheckable(True)
    button.clicked.connect(lambda: switch_to_page(page_index))
    return button

def switch_to_page(page_index):
    """切换到指定页面"""
    stacked_widget.setCurrentIndex(page_index)
    # 更新按钮状态
    for i, btn in enumerate(buttons):
        btn.setChecked(i == page_index)

# 创建按钮
chart_button = create_switch_button("K线图", 0)

buttons = [chart_button]



# 添加按钮到按钮面板
button_layout.addWidget(QLabel("功能菜单"))
button_layout.addWidget(chart_button)
button_layout.addStretch()  # 添加弹性空间

# 设置默认选中K线图
chart_button.setChecked(True)
stacked_widget.setCurrentIndex(0)

# 将按钮面板和stacked_widget添加到主布局
main_layout.addWidget(button_panel)
main_layout.addWidget(stacked_widget)

# 程序启动时立即尝试连接
if connect_tdx():
    pass
else:
    pass



# 设置主窗口
window.setCentralWidget(main_widget)
window.resize(1400, 800)  # 增加窗口尺寸以适应四分屏布局
window.setWindowTitle("股票分析系统")
window.show()

# 窗口显示后延迟创建第一个标签页，使用新的预加载策略
def create_initial_tab():
    print("主窗口已显示，开始创建初始标签页...")

    # 如果默认股票是港股，先进行诊断
    if default_symbol.endswith('.HK') or is_likely_hk_code(default_symbol):
        print(f"检测到默认股票为港股: {default_symbol}")
        print("正在进行港股数据诊断...")
        diagnose_hk_data_issue(default_symbol)

    tab_manager.create_first_tab()

QTimer.singleShot(500, create_initial_tab)  # 延迟500ms确保窗口完全显示

# 删除启动时的详细打印信息，保持界面简洁
# print("程序启动完成，将自动创建第一个标签页测试新的预加载策略")
# if XTQUANT_AVAILABLE:
#     print("✓ 支持港股数据 (xtquant)")
#     print("  港股代码格式示例: 输入 700 或 09988 即可（自动补全.HK）")
#     print("  支持功能: 历史数据加载、实时更新、多周期合成")


#
#     if DEBUG_DATA_COMPARISON:
#         print("  🔍 数据格式调试: 已启用")
#         print("    - 将输出xtquant和pytdx数据格式对比信息")

#         print("    - 如需关闭，请修改 DEBUG_DATA_COMPARISON = False")
# else:
#     print("✗ 仅支持A股数据 (pytdx)")
#     print("  如需港股支持，请安装xtquant: pip install xtquant")

# 在程序退出前确保清理线程
def cleanup():
    """程序退出时的清理函数"""
    print("DEBUG: 程序即将退出，开始清理所有资源...")
    try:
        # 清理所有标签页的资源
        tab_manager.cleanup_all()
        print("DEBUG: 程序资源清理完成")
    except Exception as e:
        print(f"ERROR: 程序清理过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

app.aboutToQuit.connect(cleanup)

app.exec_()
