#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股自动监控工具
每10秒自动打印一次全部股票的OHLC和成交量信息
无需用户选择，直接运行监控
"""

import sys
import os
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 获取港股分钟级数据 import (
    get_hk_1min_kline,
    get_multiple_hk_klines,
    HK_STOCKS
)

def print_stock_ohlc(stock_code, data):
    """
    打印单个股票的OHLC数据
    
    参数:
    stock_code: 股票代码
    data: 股票数据
    """
    if not data:
        print(f"❌ {stock_code}: 获取数据失败")
        return
        
    stock_name = HK_STOCKS.get(stock_code, stock_code)
    
    print(f"📊 {stock_name} ({stock_code})")
    print(f"   时间: {data['datetime']}")
    print(f"   开盘: {data['open']:8.3f} HKD")
    print(f"   最高: {data['high']:8.3f} HKD")
    print(f"   最低: {data['low']:8.3f} HKD")
    print(f"   收盘: {data['close']:8.3f} HKD")
    print(f"   成交量: {data['volume']:,.0f}")
    
    # 计算涨跌幅
    if data['open'] != data['close'] and data['open'] > 0:
        change = data['close'] - data['open']
        change_pct = (change / data['open']) * 100
        direction = "📈" if change > 0 else "📉"
        print(f"   涨跌: {direction} {change:+.3f} HKD ({change_pct:+.2f}%)")
    else:
        print(f"   涨跌: 📊 平盘")
    
    print()

def auto_monitor():
    """
    自动监控所有港股 - 每10秒打印一次OHLC数据
    """
    print("🚀 港股自动监控启动")
    print("=" * 80)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔄 更新频率: 每10秒")
    print("📊 监控内容: OHLC + 成交量")
    print("=" * 80)
    print("支持的港股:")
    for code, name in HK_STOCKS.items():
        print(f"  📈 {code} - {name}")
    print("=" * 80)
    print("按 Ctrl+C 停止监控")
    print()
    
    update_count = 0
    start_time = time.time()
    
    try:
        while True:
            update_count += 1
            current_time = datetime.now()
            elapsed_time = time.time() - start_time
            
            print(f"🔄 第{update_count}次更新 - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️  运行时间: {elapsed_time/60:.1f}分钟")
            print("=" * 80)
            
            # 获取所有股票数据
            results = get_multiple_hk_klines(list(HK_STOCKS.keys()))
            
            # 打印每个股票的OHLC数据
            for stock_code, data in results.items():
                print_stock_ohlc(stock_code, data)
            
            print("=" * 80)
            print(f"⏰ 下次更新: 10秒后")
            print()
            
            # 等待10秒
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\n🛑 监控已停止")
        total_time = time.time() - start_time
        print(f"📊 总计更新: {update_count}次")
        print(f"⏱️  总运行时间: {total_time/60:.1f}分钟")
        print("👋 程序退出")

if __name__ == "__main__":
    auto_monitor()
