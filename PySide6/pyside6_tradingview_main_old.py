import pandas as pd
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer, Signal, QObject
from datetime import datetime, timedelta
import threading
import logging
import sys


# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 导入迅投API
try:
    from xtquant import xtdata

    XTQUANT_AVAILABLE = True
    print("成功导入xtquant库")
except ImportError:
    print("未找到xtquant库，请安装: pip install xtquant")
    XTQUANT_AVAILABLE = False

from lightweight_charts.widgets import QtChart


# 信号类，用于线程间通信
class DataSignals(QObject):
    data_received = Signal(object)


class XTDataHandler:
    def __init__(self, symbol='600895.SH', period='tick'):
        self.symbol = symbol
        self.period = period
        self.chart = None
        self.is_subscribed = False
        self.xt_thread = None
        self.signals = DataSignals()
        self.subscription_seq = None  # 存储订阅序列号

        # 用于聚合和控制数据更新频率
        self.last_update_time = None
        self.last_minute_data = None
        self.current_minute = None

        print(f"初始化数据处理器: {symbol}, {period}")

    def set_chart(self, chart):
        """设置图表对象"""
        self.chart = chart
        # 连接信号到更新函数
        self.signals.data_received.connect(self.update_chart)
        print("图表已设置")

    def create_initial_data(self):
        """创建空的初始数据框架"""
        now = datetime.now()
        # 创建过去60分钟的空数据
        times = [(now - timedelta(minutes=i)).strftime('%Y-%m-%d %H:%M:%S') for i in range(60, 0, -1)]

        df = pd.DataFrame({
            'time': times,
            'open': [0] * 60,
            'high': [0] * 60,
            'low': [0] * 60,
            'close': [0] * 60,
            'volume': [0] * 60
        })

        print("已创建空的初始数据框架")
        return df

    def subscribe(self):
        """订阅实时行情"""
        if not XTQUANT_AVAILABLE:
            print("xtquant库未安装，无法订阅")
            return False

        try:
            print(f"开始订阅 {self.symbol} 的实时行情")
            print(self.period)

            # 订阅行情数据，并保存返回的序列号
            self.subscription_seq = xtdata.subscribe_quote(
                stock_code=self.symbol,
                period=self.period,
                count=1,
                callback=self.on_data_callback
            )

            self.is_subscribed = True
            print(f"已订阅 {self.symbol} 的实时行情，序列号: {self.subscription_seq}")

            # 启动xtdata的事件循环
            if self.xt_thread is None or not self.xt_thread.is_alive():
                self.xt_thread = threading.Thread(target=self.run_xtdata, daemon=True)
                self.xt_thread.start()

            return True

        except Exception as e:
            print(f"订阅实时行情出错: {e}")
            return False

    def run_xtdata(self):
        """在单独线程中运行xtdata事件循环"""
        try:
            print("启动xtdata事件循环")
            xtdata.run()
        except Exception as e:
            print(f"xtdata事件循环出错: {e}")
        finally:
            print("xtdata事件循环已结束")

    def unsubscribe(self):
        """取消订阅"""
        if not XTQUANT_AVAILABLE or not self.is_subscribed or self.subscription_seq is None:
            return

        try:
            # 使用序列号取消订阅
            xtdata.unsubscribe_quote(self.subscription_seq)

            self.is_subscribed = False
            print(f"已取消订阅 {self.symbol} 的实时行情，序列号: {self.subscription_seq}")
            self.subscription_seq = None

        except Exception as e:
            print(f"取消订阅出错: {e}")

    def on_data_callback(self, res):
        """数据回调函数 - 在xtdata线程中执行"""
        try:
            if self.symbol not in res:
                return

            data = res[self.symbol][0]

            # 处理K线数据
            time_value = data.get('time')
            if not time_value:
                return

            # 时间戳转换
            dt = datetime.fromtimestamp(time_value / 1000)
            time_str = dt.strftime('%Y-%m-%d %H:%M:%S')

            # 提取当前分钟
            current_minute = dt.replace(second=0, microsecond=0)

            # 检查数据是否有变化
            is_new_data = False

            # 如果是新的一分钟，肯定是新数据
            if self.current_minute is None or current_minute != self.current_minute:
                self.current_minute = current_minute
                is_new_data = True
                print(f"新的分钟K线: {time_str}")
            # 如果是同一分钟，检查数据是否有变化
            elif self.last_minute_data:
                # 检查成交量是否变化
                old_volume = self.last_minute_data.get('volume', 0)
                new_volume = data.get('volume', 0)

                # 检查价格是否变化
                old_close = self.last_minute_data.get('close')
                new_close = data.get('close')

                if old_volume != new_volume or old_close != new_close:
                    is_new_data = True
                    print(f"同分钟数据更新: 成交量 {old_volume} -> {new_volume}, 收盘价 {old_close} -> {new_close}")

            # 只有在数据有变化时才更新图表
            if is_new_data:
                # 创建数据字典
                bar_data = {
                    'time': time_str,
                    'open': data.get('open'),
                    'high': data.get('high'),
                    'low': data.get('low'),
                    'close': data.get('close'),
                    'volume': data.get('volume')
                }

                # 保存当前分钟数据
                self.last_minute_data = bar_data.copy()

                # 通过信号发送数据到主线程
                self.signals.data_received.emit(bar_data)

                print(
                    f"更新图表: {time_str}, O:{data.get('open')} H:{data.get('high')} L:{data.get('low')} C:{data.get('close')} V:{data.get('volume')}")
            else:
                print(f"忽略重复数据: {time_str}")

        except Exception as e:
            print(f"处理回调数据出错: {e}")
            import traceback
            traceback.print_exc()

    def update_chart(self, data):
        """更新图表数据 - 在主线程中执行"""
        if self.chart is None:
            return
        print("更新图表数据")
        print(data)
        try:
            if data is not None:
                # 将字典转换为pandas Series
                series = pd.Series(data)
                print(
                    f"更新图表: 时间={data['time']}, O:{data['open']} H:{data['high']} L:{data['low']} C:{data['close']} V:{data['volume']}"
                )
                
                # 如果图表内部的 _last_bar 还不存在，则初始化数据，否则调用 update
                if not hasattr(self.chart, "_last_bar") or self.chart._last_bar is None:
                    # 将当前数据转换为DataFrame格式，并初始化图表数据
                    df = pd.DataFrame([data])
                    self.chart.set(df)
                    print("图表数据初始化")
                else:
                    self.chart.update(series)
        
        except Exception as e:
            print(f"更新图表出错: {e}")
            import traceback
            traceback.print_exc()

    def change_symbol(self, new_symbol):
        """更改订阅的股票代码"""
        if self.symbol == new_symbol:
            return

        print(f"更改股票代码: {self.symbol} -> {new_symbol}")

        # 取消旧的订阅
        if self.is_subscribed:
            self.unsubscribe()

        # 更新股票代码
        self.symbol = new_symbol
        self.current_minute = None
        self.last_minute_data = None

        # 重新订阅
        self.subscribe()

    def change_period(self, new_period):
        """更改K线周期"""
        if self.period == new_period:
            return

        print(f"更改周期: {self.period} -> {new_period}")

        # 取消旧的订阅
        if self.is_subscribed:
            self.unsubscribe()

        # 更新周期
        self.period = new_period
        self.current_minute = None
        self.last_minute_data = None

        # 重新订阅
        self.subscribe()


def on_search(chart, searched_string):
    """搜索回调函数"""
    if data_handler:
        data_handler.change_symbol(searched_string)


def on_timeframe_selection(chart):
    """时间框架选择回调函数"""
    if data_handler:
        # 将图表的timeframe格式转换为xtquant格式
        timeframe = chart.topbar['timeframe'].value
        period_map = {
            '1min': '1m',
            '5min': '5m',
            '30min': '30m'
        }
        period = period_map.get(timeframe, '1m')
        data_handler.change_period(period)


def on_horizontal_line_move(chart, line):
    logging.info(f'水平线移动到: {line.price}')


# 创建全局数据处理器
data_handler = None


# 主应用
def main():
    global data_handler

    print("程序启动")

    app = QApplication([])
    window = QMainWindow()
    layout = QVBoxLayout()
    widget = QWidget()
    widget.setLayout(layout)

    window.resize(800, 500)
    layout.setContentsMargins(0, 0, 0, 0)

    chart = QtChart(widget, toolbox=True)
    chart.legend(True)

    # 设置价格轴：将下边距调大，确保 candle 的显示区域不会延伸到 volume 区
    chart.price_scale(
        auto_scale=True,
        mode='normal',
        scale_margin_top=0.05,   # 上边距保持0.05
        scale_margin_bottom=0.3, # 下边距调整到0.3，留出充足区域给 volume 成交量显示
        entire_text_only=False  # 允许部分价格标签显示
    )

    # 设置精度为2位小数，但不设置最小移动距离
    chart.precision(2)
    # 通过 time_scale 设置每个 bar（candle）的最小间距，例如设为20 像素，以便 candles 之间留有间隔
    chart.time_scale(min_bar_spacing=20)

    # 添加事件处理
    chart.events.search += on_search

    # 添加顶部工具栏元素
    chart.topbar.textbox('symbol', '600895.SH')  # 默认显示600895.SH
    chart.topbar.switcher('timeframe', ('1min', '5min', '30min'), default='1min',
                          func=on_timeframe_selection)

    # 添加价格轴缩放控制
    chart.topbar.switcher('price_view', ('自动', '百分比', '对数'), default='自动',
                          func=lambda c: chart.price_scale(
                              mode={'自动': 'normal', '百分比': 'percentage', '对数': 'logarithmic'}[
                                  c.topbar['price_view'].value]
                          ))

    # 初始化数据处理器
    if XTQUANT_AVAILABLE:
        print("创建数据处理器")
        data_handler = XTDataHandler('600895.SH', '1m')
        data_handler.set_chart(chart)
        # 启动订阅
        data_handler.subscribe()
    else:
        print("xtquant库未安装，无法显示实时数据")
        # 创建空的数据框架
        now = datetime.now()
        times = [(now - timedelta(minutes=i)).strftime('%Y-%m-%d %H:%M:%S') for i in range(60, 0, -1)]
        df = pd.DataFrame({
            'time': times,
            'open': [0] * 60,
            'high': [0] * 60,
            'low': [0] * 60,
            'close': [0] * 60,
            'volume': [0] * 60
        })

        chart.set(df)

    volume_series = chart.create_histogram(
         name='Volume',
         color='rgba(214, 237, 255, 0.6)',
         price_line=False,
         price_label=False,
         scale_margin_top=0.8,   # volume 占据最下方
         scale_margin_bottom=0.0
    )

    layout.addWidget(chart.get_webview())

    window.setCentralWidget(widget)
    window.show()
    print("窗口已显示")

    # 应用退出时清理资源
    def cleanup():
        print("程序退出，清理资源")
        if data_handler and data_handler.is_subscribed:
            data_handler.unsubscribe()

    app.aboutToQuit.connect(cleanup)

    # 捕获异常，防止程序崩溃
    sys._excepthook = sys.excepthook

    def exception_hook(exctype, value, traceback):
        print(f"未捕获的异常: {exctype.__name__}: {value}")
        sys._excepthook(exctype, value, traceback)

    sys.excepthook = exception_hook

    # 执行应用
    print("开始执行应用")
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
