# coding:utf-8
import time, datetime, traceback, sys
from xtquant import xtdata
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
import os
import csv
import pandas as pd
from collections import defaultdict


# 定义一个类 创建类的实例 作为状态的容器
class _a():
    pass


A = _a()
A.bought_list = []
A.hsa = xtdata.get_stock_list_in_sector('沪深A股')

# 全局数据缓存
kline_cache = defaultdict(lambda: defaultdict(pd.DataFrame))  # {stock: {timeframe: DataFrame}}
last_update_times = defaultdict(lambda: defaultdict(str))     # {stock: {timeframe: last_time}}


def interact():
    """执行后进入repl模式"""
    import code
    code.InteractiveConsole(locals=globals()).interact()


def is_period_closed(current_time, period_minutes):
    """
    检测指定周期是否已闭合

    参数:
    current_time: 当前时间 (datetime对象)
    period_minutes: 周期分钟数 (5, 15, 60)

    返回:
    bool: True表示周期已闭合
    """
    # 获取当前分钟
    current_minute = current_time.minute
    current_hour = current_time.hour

    if period_minutes == 5:
        # 5分钟周期：在0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55分钟闭合
        return current_minute % 5 == 0
    elif period_minutes == 15:
        # 15分钟周期：在0, 15, 30, 45分钟闭合
        return current_minute % 15 == 0
    elif period_minutes == 60:
        # 60分钟周期：在整点闭合
        return current_minute == 0

    return False


def get_period_boundary_time(current_time, period_minutes):
    """
    获取周期边界时间

    参数:
    current_time: 当前时间
    period_minutes: 周期分钟数

    返回:
    datetime: 周期边界时间
    """
    if period_minutes == 5:
        # 5分钟边界
        boundary_minute = (current_time.minute // 5) * 5
        return current_time.replace(minute=boundary_minute, second=0, microsecond=0)
    elif period_minutes == 15:
        # 15分钟边界
        boundary_minute = (current_time.minute // 15) * 15
        return current_time.replace(minute=boundary_minute, second=0, microsecond=0)
    elif period_minutes == 60:
        # 60分钟边界
        return current_time.replace(minute=0, second=0, microsecond=0)

    return current_time


def synthesize_kline_from_1min(stock_code, target_period_minutes, current_time):
    """
    从1分钟K线数据合成指定周期的K线

    参数:
    stock_code: 股票代码
    target_period_minutes: 目标周期分钟数 (5, 15, 60)
    current_time: 当前时间

    返回:
    dict: 合成的K线数据，如果无法合成则返回None
    """
    global kline_cache

    # 获取1分钟K线缓存
    one_min_df = kline_cache[stock_code]['1m']
    if one_min_df.empty:
        return None

    # 获取当前周期的边界时间
    boundary_time = get_period_boundary_time(current_time, target_period_minutes)

    # 对于合成K线，我们需要获取当前正在进行的周期的数据
    # 而不是已经闭合的周期
    period_start = boundary_time
    period_end = period_start + datetime.timedelta(minutes=target_period_minutes)

    # 筛选周期内的1分钟数据
    period_start_str = period_start.strftime('%Y-%m-%d %H:%M')
    period_end_str = period_end.strftime('%Y-%m-%d %H:%M')

    # 筛选数据时使用字符串比较
    period_data = one_min_df[
        (one_min_df['time'] >= period_start_str) &
        (one_min_df['time'] < period_end_str)
    ].copy()

    if period_data.empty:
        return None

    # 合成K线数据
    synthesized_kline = {
        'time': boundary_time.strftime('%Y-%m-%d %H:%M'),
        'open': period_data.iloc[0]['open'],      # 第一根1分钟K线的开盘价
        'high': period_data['high'].max(),        # 周期内最高价
        'low': period_data['low'].min(),          # 周期内最低价
        'close': period_data.iloc[-1]['close'],   # 最后一根1分钟K线的收盘价
        'volume': period_data['volume'].sum()     # 周期内成交量总和
    }

    return synthesized_kline


def update_kline_cache(stock_code, timeframe, kline_data):
    """
    更新K线缓存

    参数:
    stock_code: 股票代码
    timeframe: 时间周期 ('1m', '5m', '15m', '60m')
    kline_data: K线数据 (dict格式)
    """
    global kline_cache, last_update_times

    if kline_data is None:
        return

    # 获取当前缓存
    current_cache = kline_cache[stock_code][timeframe]

    # 创建新的数据行
    new_row = pd.DataFrame([kline_data])

    # 检查是否是新数据
    kline_time = kline_data['time']
    if not current_cache.empty:
        # 检查最新时间是否已存在
        if kline_time in current_cache['time'].values:
            # 更新现有数据
            mask = current_cache['time'] == kline_time
            for col in ['open', 'high', 'low', 'close', 'volume']:
                current_cache.loc[mask, col] = kline_data[col]
        else:
            # 添加新数据
            current_cache = pd.concat([current_cache, new_row], ignore_index=True)
    else:
        # 首次添加数据
        current_cache = new_row.copy()

    # 保持数据量限制（保留最近1000条）
    if len(current_cache) > 1000:
        current_cache = current_cache.tail(1000).reset_index(drop=True)

    # 更新缓存
    kline_cache[stock_code][timeframe] = current_cache
    last_update_times[stock_code][timeframe] = kline_time


def fetch_closed_kline_from_xtquant(stock_code, period, boundary_time):
    """
    从xtquant获取闭合的K线数据

    参数:
    stock_code: 股票代码
    period: 周期 ('5m', '15m', '60m')
    boundary_time: 边界时间

    返回:
    dict: K线数据，如果获取失败则返回None
    """
    try:
        # 获取指定周期的K线数据
        kline_data = xtdata.get_market_data(
            field_list=[],  # 获取所有字段
            stock_list=[stock_code],
            period=period,
            count=5,  # 获取最近5条数据
            dividend_type='none'
        )

        if kline_data and 'time' in kline_data:
            times = kline_data['time'].columns.tolist()

            if times and stock_code in kline_data['time'].index:
                # 查找匹配的时间
                boundary_time_str = boundary_time.strftime('%Y%m%d%H%M%S')

                for time_str in times:
                    if time_str == boundary_time_str:
                        # 找到匹配的K线数据
                        return {
                            'time': boundary_time.strftime('%Y-%m-%d %H:%M'),
                            'open': kline_data['open'].loc[stock_code, time_str],
                            'high': kline_data['high'].loc[stock_code, time_str],
                            'low': kline_data['low'].loc[stock_code, time_str],
                            'close': kline_data['close'].loc[stock_code, time_str],
                            'volume': kline_data['volume'].loc[stock_code, time_str]
                        }

    except Exception as e:
        print(f"从xtquant获取{period}K线数据失败: {e}")

    return None


def get_1min_kline_data(stock_code):
    """
    获取1分钟K线数据

    参数:
    stock_code: 股票代码

    返回:
    dict: K线数据，如果获取失败则返回None
    """
    try:
        # 获取最新的1分钟K线数据
        kline_data = xtdata.get_market_data(
            field_list=[],  # 获取所有字段
            stock_list=[stock_code],
            period='1m',  # 1分钟K线
            count=2,  # 获取最近2条数据
            dividend_type='none'  # 不复权
        )

        if kline_data and 'time' in kline_data:
            times = kline_data['time'].columns.tolist()

            if times and stock_code in kline_data['time'].index:
                latest_time = times[-1]  # 最新时间

                # 转换时间格式
                if isinstance(latest_time, str) and len(latest_time) == 14:
                    # 格式：20231201093000 -> 2023-12-01 09:30
                    time_obj = datetime.datetime.strptime(latest_time, '%Y%m%d%H%M%S')
                    formatted_time = time_obj.strftime('%Y-%m-%d %H:%M')
                else:
                    formatted_time = str(latest_time)

                return {
                    'time': formatted_time,
                    'open': float(kline_data['open'].loc[stock_code, latest_time]),
                    'high': float(kline_data['high'].loc[stock_code, latest_time]),
                    'low': float(kline_data['low'].loc[stock_code, latest_time]),
                    'close': float(kline_data['close'].loc[stock_code, latest_time]),
                    'volume': int(kline_data['volume'].loc[stock_code, latest_time])
                }

    except Exception as e:
        print(f"获取1分钟K线数据失败: {e}")

    return None


def write_kline_to_csv(stock_code, timeframe, kline_data, data_source, csv_folder):
    """
    将K线数据写入CSV文件

    参数:
    stock_code: 股票代码
    timeframe: 时间周期
    kline_data: K线数据
    data_source: 数据来源
    csv_folder: CSV文件夹
    """
    try:
        csv_path = os.path.join(csv_folder, f"{stock_code}_{timeframe}.csv")
        local_time = datetime.datetime.now()

        with open(csv_path, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                kline_data['time'],
                local_time.strftime('%Y-%m-%d %H:%M:%S.%f'),
                data_source,
                kline_data['open'],
                kline_data['high'],
                kline_data['low'],
                kline_data['close'],
                kline_data['volume'],
                timeframe
            ])
    except Exception as e:
        print(f"写入CSV失败: {e}")


# xtdata.download_sector_data()  # 注释掉，避免阻塞程序启动


def download_kline_history(stock_codes):
    """
    下载股票的历史1分钟K线数据

    参数:
    stock_codes: 股票代码列表
    """
    print("开始下载历史1分钟K线数据...")

    for stock in stock_codes:
        try:
            print(f"下载 {stock} 的历史1分钟K线数据...")
            # 下载最近几天的1分钟K线数据
            xtdata.download_history_data(
                stock_code=stock,
                period='1m',
                start_time='',  # 空表示自动确定起始时间
                end_time='',    # 空表示到最新时间
                incrementally=True  # 增量下载
            )
            print(f"{stock} 历史数据下载完成")
        except Exception as e:
            print(f"下载 {stock} 历史数据失败: {e}")

    print("历史1分钟K线数据下载完成")


def download_multi_timeframe_kline_history(stock_codes):
    """
    下载股票的历史多时间周期K线数据

    参数:
    stock_codes: 股票代码列表
    """
    print("开始下载历史多时间周期K线数据...")

    # 定义要下载的时间周期
    timeframes = {
        '1m': {'period': '1m', 'name': '1分钟'},
        '5m': {'period': '5m', 'name': '5分钟'},
        '15m': {'period': '15m', 'name': '15分钟'},
        '60m': {'period': '60m', 'name': '60分钟'}
    }

    for stock in stock_codes:
        for tf_key, tf_info in timeframes.items():
            try:
                print(f"下载 {stock} 的历史{tf_info['name']}K线数据...")
                # 下载指定时间周期的K线数据
                xtdata.download_history_data(
                    stock_code=stock,
                    period=tf_info['period'],
                    start_time='',  # 空表示自动确定起始时间
                    end_time='',    # 空表示到最新时间
                    incrementally=True  # 增量下载
                )
                print(f"{stock} {tf_info['name']}历史数据下载完成")
            except Exception as e:
                print(f"下载 {stock} {tf_info['name']}历史数据失败: {e}")

    print("历史多时间周期K线数据下载完成")


def is_hk_trading_time():
    """
    判断当前是否为港股交易时间段

    港股交易时间段：
    - 早市：9:30 AM - 12:00 PM
    - 午市：1:00 PM - 4:00 PM
    - 午休：12:00 PM - 1:00 PM (休市)

    返回:
    bool: True表示在交易时间段内，False表示不在交易时间段内
    """
    now = datetime.datetime.now()
    current_time = now.time()

    # 定义港股交易时间段
    morning_start = datetime.time(9, 30)   # 9:30 AM
    morning_end = datetime.time(12, 0)     # 12:00 PM
    afternoon_start = datetime.time(13, 0) # 1:00 PM
    afternoon_end = datetime.time(16, 0)   # 4:00 PM

    # 判断是否在交易时间段内
    is_morning_session = morning_start <= current_time <= morning_end
    is_afternoon_session = afternoon_start <= current_time <= afternoon_end

    return is_morning_session or is_afternoon_session


def get_next_trading_time():
    """
    获取下一个港股交易时间段

    返回:
    datetime: 下一个交易时间段的开始时间
    """
    now = datetime.datetime.now()
    current_time = now.time()

    # 如果在早市开始前
    if current_time < datetime.time(9, 30):
        return now.replace(hour=9, minute=30, second=0, microsecond=0)
    # 如果在午休时间
    elif datetime.time(12, 0) <= current_time < datetime.time(13, 0):
        return now.replace(hour=13, minute=0, second=0, microsecond=0)
    # 如果在收市后或其他时间
    else:
        # 明天的早市
        next_day = now + datetime.timedelta(days=1)
        return next_day.replace(hour=9, minute=30, second=0, microsecond=0)


def subscribe_kline_data(stock_codes):
    """
    订阅股票的实时1分钟K线数据

    参数:
    stock_codes: 股票代码列表
    """
    print("开始订阅实时1分钟K线数据...")

    # 定义K线数据回调函数
    def on_kline_data(datas):
        """K线数据回调函数"""
        try:
            # 检查是否在港股交易时间段内
            if not is_hk_trading_time():
                print("收到K线数据，但当前不在港股交易时间段内 (9:30-12:00, 13:00-16:00)，忽略数据")
                return
            csv_folder = "csv_kline"
            if not os.path.exists(csv_folder):
                os.makedirs(csv_folder)

            for stock_code in datas:
                kline_list = datas[stock_code]
                if kline_list:
                    # 获取最新的K线数据
                    latest_kline = kline_list[-1]  # 最后一条数据
                    local_time = datetime.datetime.now()

                    # 解析K线数据
                    data_time = datetime.datetime.fromtimestamp(latest_kline['time'] / 1000)
                    open_price = latest_kline.get('open', 0)
                    high_price = latest_kline.get('high', 0)
                    low_price = latest_kline.get('low', 0)
                    close_price = latest_kline.get('close', 0)
                    volume = latest_kline.get('volume', 0)
                    amount = latest_kline.get('amount', 0)
                    pre_close = latest_kline.get('preClose', 0)
                    suspend_flag = latest_kline.get('suspendFlag', 0)

                    delay_seconds = (local_time - data_time).total_seconds()

                    # 输出K线信息
                    print(f"===== {stock_code} 实时1分钟K线 =====")
                    print(f"数据时间: {data_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"本地时间: {local_time}")
                    print(f"延迟: {delay_seconds:.3f}秒")
                    print(f"开盘价: {open_price:.2f}, 最高价: {high_price:.2f}")
                    print(f"最低价: {low_price:.2f}, 收盘价: {close_price:.2f}")
                    print(f"成交量: {volume}, 成交额: {amount:.2f}")
                    print("-" * 50)

                    # 写入CSV文件
                    csv_path = os.path.join(csv_folder, f"{stock_code}_1m_realtime.csv")

                    # 检查文件是否存在，如果不存在则创建并写入表头
                    if not os.path.exists(csv_path):
                        with open(csv_path, 'w', newline='') as f:
                            writer = csv.writer(f)
                            writer.writerow(['数据时间', '本地时间', '延迟(秒)', '开盘价', '最高价', '最低价',
                                            '收盘价', '成交量', '成交额', '前收价', '停牌标记'])

                    with open(csv_path, 'a', newline='') as f:
                        writer = csv.writer(f)
                        writer.writerow([
                            data_time.strftime('%Y-%m-%d %H:%M:%S'),
                            local_time.strftime('%Y-%m-%d %H:%M:%S.%f'),
                            f"{delay_seconds:.3f}",
                            open_price,
                            high_price,
                            low_price,
                            close_price,
                            volume,
                            amount,
                            pre_close,
                            suspend_flag
                        ])

        except Exception as e:
            print(f"处理K线数据回调出错: {e}")
            traceback.print_exc()

    # 为每只股票订阅1分钟K线数据
    subscription_ids = []
    for stock in stock_codes:
        try:
            seq = xtdata.subscribe_quote(
                stock_code=stock,
                period='1m',  # 1分钟K线
                count=0,      # 只订阅实时数据
                callback=on_kline_data
            )
            if seq > 0:
                subscription_ids.append(seq)
                print(f"成功订阅 {stock} 的1分钟K线数据，订阅号: {seq}")
            else:
                print(f"订阅 {stock} 的1分钟K线数据失败")
        except Exception as e:
            print(f"订阅 {stock} 的1分钟K线数据出错: {e}")

    return subscription_ids


def get_and_process_multi_timeframe_kline_data(stock_codes, interval=30):
    """
    主动获取并处理指定股票列表的多时间周期K线数据

    新逻辑：
    1. 每30秒从xtquant获取1分钟K线数据
    2. 使用1分钟数据合成5min、15min、60min周期的K线形态
    3. 只有在完全闭合时才从xtquant获取该周期的闭合K线数据

    参数:
    stock_codes: 股票代码列表，如 ['600000.SH', '000001.SZ']
    interval: 获取数据的时间间隔(秒)，默认30秒获取一次
    """
    global kline_cache, last_update_times

    # 定义时间周期映射
    timeframes = {
        '1m': {'period': '1m', 'minutes': 1, 'name': '1分钟'},
        '5m': {'period': '5m', 'minutes': 5, 'name': '5分钟'},
        '15m': {'period': '15m', 'minutes': 15, 'name': '15分钟'},
        '60m': {'period': '60m', 'minutes': 60, 'name': '60分钟'}
    }

    # 确保CSV文件夹存在
    csv_folder = "csv_multi_kline_enhanced"
    if not os.path.exists(csv_folder):
        os.makedirs(csv_folder)

    # 为每支股票和每个时间周期创建CSV文件并写入表头
    for stock in stock_codes:
        for tf_key, tf_info in timeframes.items():
            csv_path = os.path.join(csv_folder, f"{stock}_{tf_key}.csv")
            # 检查文件是否存在，如果不存在则创建并写入表头
            if not os.path.exists(csv_path):
                with open(csv_path, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['数据时间', '本地时间', '数据来源', '开盘价', '最高价', '最低价',
                                    '收盘价', '成交量', '时间周期'])

    print("开始增强版多时间周期K线数据获取...")
    print("逻辑：每30秒获取1分钟数据，合成其他周期，周期闭合时获取xtquant数据")

    while True:
        now = datetime.datetime.now()
        print(f"\n检查时间: {now.strftime('%H:%M:%S')}")

        # 检查是否在港股交易时间段内
        if not is_hk_trading_time():
            next_trading_time = get_next_trading_time()
            print(f"当前不在港股交易时间段内 (9:30-12:00, 13:00-16:00)")
            print(f"下一个交易时间段: {next_trading_time.strftime('%H:%M')}")

            # 计算等待时间
            wait_seconds = (next_trading_time - now).total_seconds()
            if wait_seconds > 0:
                wait_minutes = wait_seconds / 60
                if wait_minutes < 60:
                    print(f"等待 {wait_minutes:.0f} 分钟后开始获取数据...")
                else:
                    print(f"等待 {wait_seconds/3600:.1f} 小时后开始获取数据...")
                # 如果等待时间超过1小时，则每小时检查一次
                sleep_time = min(3600, wait_seconds)  # 最多等待1小时
                time.sleep(sleep_time)
                continue

        print(f"港股交易时间段内，开始增强版K线数据处理: {now.strftime('%H:%M:%S')}")

        # 逐个处理股票
        for stock in stock_codes:
            print(f"\n--- 处理 {stock} ---")

            # 步骤1：获取1分钟K线数据
            print(f"1. 获取 {stock} 的1分钟K线数据...")
            one_min_kline = get_1min_kline_data(stock)

            if one_min_kline:
                # 更新1分钟缓存
                update_kline_cache(stock, '1m', one_min_kline)
                print(f"   ✓ 1分钟K线数据已更新: {one_min_kline['time']}")

                # 写入CSV
                write_kline_to_csv(stock, '1m', one_min_kline, 'xtquant_1m', csv_folder)

                # 步骤2：处理其他周期 (5m, 15m, 60m)
                for tf_key, tf_info in timeframes.items():
                    if tf_key == '1m':  # 跳过1分钟，已经处理过了
                        continue

                    print(f"2. 处理 {stock} 的{tf_info['name']}K线...")

                    # 检查是否周期闭合
                    period_minutes = tf_info['minutes']
                    if is_period_closed(now, period_minutes):
                        print(f"   周期闭合，从xtquant获取{tf_info['name']}数据...")

                        # 获取周期边界时间
                        boundary_time = get_period_boundary_time(now, period_minutes)

                        # 从xtquant获取闭合的K线数据
                        closed_kline = fetch_closed_kline_from_xtquant(stock, tf_info['period'], boundary_time)

                        if closed_kline:
                            update_kline_cache(stock, tf_key, closed_kline)
                            write_kline_to_csv(stock, tf_key, closed_kline, f'xtquant_{tf_key}_closed', csv_folder)
                            print(f"   ✓ {tf_info['name']}闭合K线已更新: {closed_kline['time']}")
                        else:
                            print(f"   ✗ 未获取到{tf_info['name']}闭合K线数据")

                    # 无论是否闭合，都尝试从1分钟数据合成当前周期K线
                    synthesized_kline = synthesize_kline_from_1min(stock, period_minutes, now)

                    if synthesized_kline:
                        # 检查是否是新数据或更新数据
                        current_cache = kline_cache[stock][tf_key]
                        should_update = True

                        if not current_cache.empty:
                            # 检查最新时间是否相同
                            latest_cached_time = current_cache.iloc[-1]['time']
                            if latest_cached_time == synthesized_kline['time']:
                                # 时间相同，检查数据是否有变化
                                latest_cached = current_cache.iloc[-1]
                                if (latest_cached['close'] == synthesized_kline['close'] and
                                    latest_cached['volume'] == synthesized_kline['volume']):
                                    should_update = False

                        if should_update:
                            update_kline_cache(stock, tf_key, synthesized_kline)
                            write_kline_to_csv(stock, tf_key, synthesized_kline, f'synthesized_{tf_key}', csv_folder)
                            print(f"   ✓ {tf_info['name']}合成K线已更新: {synthesized_kline['time']}")
                        else:
                            print(f"   - {tf_info['name']}合成K线无变化")
                    else:
                        print(f"   ✗ 无法合成{tf_info['name']}K线")
            else:
                print(f"   ✗ 未获取到1分钟K线数据")
                continue

        # 等待指定间隔时间
        print(f"\n等待 {interval} 秒后继续...")
        time.sleep(interval)


def subscribe_multi_timeframe_kline_data(stock_codes):
    """
    订阅股票的实时多时间周期K线数据

    参数:
    stock_codes: 股票代码列表
    """
    print("开始订阅实时多时间周期K线数据...")

    # 定义要订阅的时间周期
    timeframes = {
        '1m': {'period': '1m', 'name': '1分钟'},
        '5m': {'period': '5m', 'name': '5分钟'},
        '15m': {'period': '15m', 'name': '15分钟'},
        '60m': {'period': '60m', 'name': '60分钟'}
    }

    # 记录上次处理的K线时间，避免重复处理
    last_processed_times = {}

    # 定义K线数据回调函数
    def create_kline_callback(timeframe_key, timeframe_info):
        def on_kline_data(datas):
            """K线数据回调函数"""
            try:
                # 检查是否在港股交易时间段内
                if not is_hk_trading_time():
                    return

                csv_folder = "csv_multi_kline_realtime"
                if not os.path.exists(csv_folder):
                    os.makedirs(csv_folder)

                for stock_code in datas:
                    kline_list = datas[stock_code]
                    if kline_list:
                        # 获取最新的K线数据
                        latest_kline = kline_list[-1]  # 最后一条数据
                        local_time = datetime.datetime.now()

                        # 解析K线数据
                        data_time = datetime.datetime.fromtimestamp(latest_kline['time'] / 1000)

                        # 创建唯一键用于去重
                        unique_key = f"{stock_code}_{timeframe_key}_{latest_kline['time']}"

                        # 检查是否已经处理过这条数据
                        if unique_key in last_processed_times:
                            return  # 跳过重复数据

                        last_processed_times[unique_key] = data_time

                        open_price = latest_kline.get('open', 0)
                        high_price = latest_kline.get('high', 0)
                        low_price = latest_kline.get('low', 0)
                        close_price = latest_kline.get('close', 0)
                        volume = latest_kline.get('volume', 0)
                        amount = latest_kline.get('amount', 0)
                        pre_close = latest_kline.get('preClose', 0)
                        suspend_flag = latest_kline.get('suspendFlag', 0)

                        delay_seconds = (local_time - data_time).total_seconds()

                        # 只在有新数据时输出K线信息
                        print(f"===== {stock_code} 实时{timeframe_info['name']}K线 (新数据) =====")
                        print(f"数据时间: {data_time.strftime('%Y-%m-%d %H:%M:%S')}")
                        print(f"本地时间: {local_time.strftime('%H:%M:%S')}")
                        print(f"延迟: {delay_seconds:.1f}秒")
                        print(f"开盘价: {open_price:.2f}, 最高价: {high_price:.2f}")
                        print(f"最低价: {low_price:.2f}, 收盘价: {close_price:.2f}")
                        print(f"成交量: {volume}, 成交额: {amount:.2f}")
                        print("-" * 40)

                        # 写入CSV文件
                        csv_path = os.path.join(csv_folder, f"{stock_code}_{timeframe_key}_realtime.csv")

                        # 检查文件是否存在，如果不存在则创建并写入表头
                        if not os.path.exists(csv_path):
                            with open(csv_path, 'w', newline='') as f:
                                writer = csv.writer(f)
                                writer.writerow(['数据时间', '本地时间', '延迟(秒)', '开盘价', '最高价', '最低价',
                                                '收盘价', '成交量', '成交额', '前收价', '停牌标记', '时间周期'])

                        with open(csv_path, 'a', newline='') as f:
                            writer = csv.writer(f)
                            writer.writerow([
                                data_time.strftime('%Y-%m-%d %H:%M:%S'),
                                local_time.strftime('%Y-%m-%d %H:%M:%S.%f'),
                                f"{delay_seconds:.3f}",
                                open_price,
                                high_price,
                                low_price,
                                close_price,
                                volume,
                                amount,
                                pre_close,
                                suspend_flag,
                                timeframe_info['name']
                            ])

            except Exception as e:
                print(f"处理{timeframe_info['name']}K线数据回调出错: {e}")
                traceback.print_exc()

        return on_kline_data

    # 为每只股票和每个时间周期订阅K线数据
    subscription_ids = []
    for stock in stock_codes:
        for tf_key, tf_info in timeframes.items():
            try:
                callback = create_kline_callback(tf_key, tf_info)
                seq = xtdata.subscribe_quote(
                    stock_code=stock,
                    period=tf_info['period'],  # 时间周期
                    count=0,                   # 只订阅实时数据
                    callback=callback
                )
                if seq > 0:
                    subscription_ids.append(seq)
                    print(f"成功订阅 {stock} 的{tf_info['name']}K线数据，订阅号: {seq}")
                else:
                    print(f"订阅 {stock} 的{tf_info['name']}K线数据失败")
            except Exception as e:
                print(f"订阅 {stock} 的{tf_info['name']}K线数据出错: {e}")

    return subscription_ids


def get_and_process_kline_data(stock_codes, interval=30):
    """
    主动获取并处理指定股票列表的1分钟K线数据

    参数:
    stock_codes: 股票代码列表，如 ['600000.SH', '000001.SZ']
    interval: 获取数据的时间间隔(秒)，默认30秒获取一次
    """
    # 确保CSV文件夹存在
    csv_folder = "csv_kline"
    if not os.path.exists(csv_folder):
        os.makedirs(csv_folder)

    # 为每支股票创建CSV文件并写入表头
    for stock in stock_codes:
        csv_path = os.path.join(csv_folder, f"{stock}_1m.csv")
        # 检查文件是否存在，如果不存在则创建并写入表头
        if not os.path.exists(csv_path):
            with open(csv_path, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['数据时间', '本地时间', '延迟(秒)', '开盘价', '最高价', '最低价',
                                '收盘价', '成交量', '成交额', '前收价', '停牌标记'])

    # 记录上次获取的K线时间，避免重复记录
    last_kline_times = {}

    while True:
        now = datetime.datetime.now()
        print(f"\n检查时间: {now}")

        # 检查是否在港股交易时间段内
        if not is_hk_trading_time():
            next_trading_time = get_next_trading_time()
            print(f"当前不在港股交易时间段内 (9:30-12:00, 13:00-16:00)")
            print(f"下一个交易时间段: {next_trading_time.strftime('%H:%M')}")

            # 计算等待时间
            wait_seconds = (next_trading_time - now).total_seconds()
            if wait_seconds > 0:
                wait_minutes = wait_seconds / 60
                if wait_minutes < 60:
                    print(f"等待 {wait_minutes:.0f} 分钟后开始获取数据...")
                else:
                    print(f"等待 {wait_seconds/3600:.1f} 小时后开始获取数据...")
                # 如果等待时间超过1小时，则每小时检查一次
                sleep_time = min(3600, wait_seconds)  # 最多等待1小时
                time.sleep(sleep_time)
                continue

        print(f"港股交易时间段内，开始获取1分钟K线数据: {now.strftime('%H:%M:%S')}")

        # 记录成功获取数据的股票数量
        success_count = 0

        # 逐个获取股票K线数据
        for stock in stock_codes:
            try:
                print(f"正在获取 {stock} 的1分钟K线数据...")

                # 获取最新的1分钟K线数据（获取最近2条，确保能获取到最新的）
                kline_data = xtdata.get_market_data(
                    field_list=[],  # 获取所有字段
                    stock_list=[stock],
                    period='1m',  # 1分钟K线
                    count=2,  # 获取最近2条数据
                    dividend_type='none'  # 不复权
                )

                print(f"获取到的数据结构: {type(kline_data)}")
                if kline_data:
                    print(f"数据字段: {list(kline_data.keys())}")

                if kline_data and 'time' in kline_data:
                    # 获取最新的K线数据（最后一条）
                    times = kline_data['time'].columns.tolist()
                    print(f"时间列表: {times}")

                    if times and stock in kline_data['time'].index:
                        latest_time = times[-1]  # 最新时间
                        print(f"最新时间: {latest_time}")

                        # 检查是否是新的K线数据
                        if stock not in last_kline_times or last_kline_times[stock] != latest_time:
                            last_kline_times[stock] = latest_time

                            success_count += 1
                            local_time = datetime.datetime.now()

                            # 获取K线各字段数据
                            data_time = latest_time
                            open_price = kline_data['open'].loc[stock, latest_time] if 'open' in kline_data else 0
                            high_price = kline_data['high'].loc[stock, latest_time] if 'high' in kline_data else 0
                            low_price = kline_data['low'].loc[stock, latest_time] if 'low' in kline_data else 0
                            close_price = kline_data['close'].loc[stock, latest_time] if 'close' in kline_data else 0
                            volume = kline_data['volume'].loc[stock, latest_time] if 'volume' in kline_data else 0
                            amount = kline_data['amount'].loc[stock, latest_time] if 'amount' in kline_data else 0
                            pre_close = kline_data['preClose'].loc[stock, latest_time] if 'preClose' in kline_data else 0
                            suspend_flag = kline_data['suspendFlag'].loc[stock, latest_time] if 'suspendFlag' in kline_data else 0

                            # 计算时间延迟
                            if isinstance(data_time, str):
                                data_datetime = datetime.datetime.strptime(data_time, '%Y%m%d%H%M%S')
                            else:
                                data_datetime = data_time
                            delay_seconds = (local_time - data_datetime).total_seconds()

                            # 输出股票K线信息
                            print(f"===== {stock} 1分钟K线 =====")
                            print(f"数据时间: {data_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
                            print(f"本地时间: {local_time}")
                            print(f"延迟: {delay_seconds:.3f}秒")
                            print(f"开盘价: {open_price:.2f}, 最高价: {high_price:.2f}")
                            print(f"最低价: {low_price:.2f}, 收盘价: {close_price:.2f}")
                            print(f"成交量: {volume}, 成交额: {amount:.2f}")
                            print("-" * 50)

                            # 写入CSV文件
                            csv_path = os.path.join(csv_folder, f"{stock}_1m.csv")
                            with open(csv_path, 'a', newline='') as f:
                                writer = csv.writer(f)
                                writer.writerow([
                                    data_datetime.strftime('%Y-%m-%d %H:%M:%S'),
                                    local_time.strftime('%Y-%m-%d %H:%M:%S.%f'),
                                    f"{delay_seconds:.3f}",
                                    open_price,
                                    high_price,
                                    low_price,
                                    close_price,
                                    volume,
                                    amount,
                                    pre_close,
                                    suspend_flag
                                ])
                        else:
                            print(f"{stock}: 无新的K线数据")
                    else:
                        print(f"{stock}: 未找到时间数据或股票不在索引中")
                else:
                    print(f"{stock}: 未获取到K线数据或缺少time字段")

            except Exception as e:
                print(f"获取股票 {stock} 1分钟K线数据出错: {e}")
                traceback.print_exc()

        print(f"成功获取 {success_count}/{len(stock_codes)} 只股票的1分钟K线数据")

        # 等待指定间隔时间
        time.sleep(interval)


class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def on_disconnected(self):
        """
        连接断开
        :return:
        """
        print(datetime.datetime.now(), '连接断开回调')

    def on_stock_order(self, order):
        """
        委托回报推送
        :param order: XtOrder对象
        :return:
        """
        print(datetime.datetime.now(), '委托回调', order.order_remark)

    def on_stock_trade(self, trade):
        """
        成交变动推送
        :param trade: XtTrade对象
        :return:
        """
        print(datetime.datetime.now(), '成交回调', trade.order_remark)

    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        # print("on order_error callback")
        # print(order_error.order_id, order_error.error_id, order_error.error_msg)
        print(f"委托报错回调 {order_error.order_remark} {order_error.error_msg}")

    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        print(datetime.datetime.now(), sys._getframe().f_code.co_name)

    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        print(f"异步委托回调 {response.order_remark}")

    def on_cancel_order_stock_async_response(self, response):
        """
        :param response: XtCancelOrderResponse 对象
        :return:
        """
        print(datetime.datetime.now(), sys._getframe().f_code.co_name)

    def on_account_status(self, status):
        """
        :param response: XtAccountStatus 对象
        :return:
        """
        print(datetime.datetime.now(), sys._getframe().f_code.co_name)


if __name__ == '__main__':
    print("启动1分钟K线数据获取程序...")

    # 暂时注释掉交易连接部分，专注于K线数据获取
    # # 指定客户端所在路径,
    # # 注意：如果是连接投研端进行交易，文件目录需要指定到f"{安装目录}\userdata"
    # path = r'D:\湘财迅投QMT极速策略交易系统（模拟交易端）\userdata_mini'
    # # 生成session id 整数类型 同时运行的策略不能重复
    # session_id = int(time.time())
    # xt_trader = XtQuantTrader(path, session_id)
    # # 开启主动请求接口的专用线程 开启后在on_stock_xxx回调函数里调用XtQuantTrader.query_xxx函数不会卡住回调线程，但是查询和推送的数据在时序上会变得不确定
    # # 详见: http://docs.thinktrader.net/vip/pages/ee0e9b/#开启主动请求接口的专用线程
    # # xt_trader.set_relaxed_response_order_enabled(True)

    # # 创建资金账号为 800068 的证券账号对象
    # acc = StockAccount('********', 'STOCK')
    # # 创建交易回调类对象，并声明接收回调
    # callback = MyXtQuantTraderCallback()
    # xt_trader.register_callback(callback)
    # # 启动交易线程
    # xt_trader.start()
    # # 建立交易连接，返回0表示连接成功
    # connect_result = xt_trader.connect()
    # print('建立交易连接，返回0表示连接成功', connect_result)
    # # 对交易回调进行订阅，订阅后可以收到交易主推，返回0表示订阅成功
    # subscribe_result = xt_trader.subscribe(acc)
    # print('对交易回调进行订阅，订阅后可以收到交易主推，返回0表示订阅成功', subscribe_result)

    # 使用示例 - 订阅指定的几只股票
    stock_codes = [
        '09988.HK'
    ]

    # 选择模式：单一时间周期 或 多时间周期
    print("请选择K线数据获取模式:")
    print("1. 单一时间周期模式 (仅1分钟K线)")
    print("2. 多时间周期模式 (1分钟、5分钟、15分钟、60分钟K线)")

    while True:
        try:
            choice = input("请输入选择 (1 或 2，默认为2): ").strip()
            if choice == '' or choice == '2':
                mode = 'multi'
                break
            elif choice == '1':
                mode = 'single'
                break
            else:
                print("无效选择，请输入 1 或 2")
        except KeyboardInterrupt:
            print("\n程序退出")
            exit()

    if mode == 'single':
        print(f"选择单一时间周期模式，准备获取 {len(stock_codes)} 只股票的实时1分钟K线数据...")

        # 首先下载历史K线数据
        download_kline_history(stock_codes)

        try:
            # 暂时禁用订阅功能，只使用主动获取
            print("注意：为确保30秒间隔获取，暂时禁用实时订阅功能")

            # 启动K线数据获取线程，每30秒更新一次
            import threading
            kline_thread = threading.Thread(
                target=get_and_process_kline_data,
                args=(stock_codes, 30),  # 每30秒获取一次1分钟K线数据
                daemon=True
            )
            kline_thread.start()

            print("1分钟K线数据获取已启动，每30秒获取一次...")

            # 保持程序运行
            while True:
                time.sleep(1)

        except KeyboardInterrupt:
            print("程序手动终止")
            # 取消订阅
            if 'subscription_ids' in locals():
                for seq in subscription_ids:
                    try:
                        xtdata.unsubscribe_quote(seq)
                        print(f"取消订阅号 {seq}")
                    except Exception as e:
                        print(f"取消订阅失败: {e}")

    else:  # multi mode
        print(f"选择多时间周期模式，准备获取 {len(stock_codes)} 只股票的多时间周期K线数据...")
        print("时间周期包括: 1分钟、5分钟、15分钟、60分钟")

        # 首先下载历史多时间周期K线数据
        download_multi_timeframe_kline_history(stock_codes)

        try:
            # 暂时禁用订阅功能，只使用主动获取
            print("注意：为确保30秒间隔获取，暂时禁用实时订阅功能")

            # 启动多时间周期K线数据获取线程，每30秒更新一次
            import threading
            kline_thread = threading.Thread(
                target=get_and_process_multi_timeframe_kline_data,
                args=(stock_codes, 30),  # 每30秒获取一次多时间周期K线数据
                daemon=True
            )
            kline_thread.start()

            print("多时间周期K线数据获取已启动，每30秒获取一次...")

            # 保持程序运行
            while True:
                time.sleep(1)

        except KeyboardInterrupt:
            print("程序手动终止")
            # 取消订阅
            if 'subscription_ids' in locals():
                for seq in subscription_ids:
                    try:
                        xtdata.unsubscribe_quote(seq)
                        print(f"取消订阅号 {seq}")
                    except Exception as e:
                        print(f"取消订阅失败: {e}")
        except Exception as e:
            print(f"程序运行出错: {e}")
            traceback.print_exc()

    # 如果使用vscode pycharm等本地编辑器 可以进入交互模式 方便调试
    # interact()
