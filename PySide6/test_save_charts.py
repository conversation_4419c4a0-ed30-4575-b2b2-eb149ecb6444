#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试15min和60min支撑阻力图表保存功能
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_save_charts():
    """测试保存15min和60min支撑阻力图表"""
    try:
        from support_and_resistance_zone import save_support_resistance_charts, XTDATA_AVAILABLE
        
        if not XTDATA_AVAILABLE:
            print("❌ xtdata未安装，无法测试图表保存功能")
            return False
        
        print("=" * 80)
        print("测试15min和60min支撑阻力位图表保存功能")
        print("=" * 80)
        
        # 测试股票代码
        test_stock = "600895.SH"  # 张江高科
        
        print(f"测试股票: {test_stock}")
        print(f"时间周期: 15min, 60min")
        print(f"保存位置: PySide6文件夹")
        print()
        
        # 开始保存图表
        print("开始生成和保存图表...")
        start_time = datetime.now()
        
        results = save_support_resistance_charts(
            stock_code=test_stock,
            timeframes=['15m', '60m'],
            swing_length=10
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 显示结果
        print(f"\n生成完成，耗时: {duration:.1f}秒")
        print("=" * 80)
        
        success_count = 0
        total_count = len(results)
        
        for timeframe, save_path in results.items():
            if save_path and os.path.exists(save_path):
                file_size = os.path.getsize(save_path) / 1024  # KB
                print(f"✅ {timeframe}: {os.path.basename(save_path)} ({file_size:.1f}KB)")
                success_count += 1
            else:
                print(f"❌ {timeframe}: 保存失败")
        
        print("=" * 80)
        print(f"结果: {success_count}/{total_count} 个图表保存成功")
        
        if success_count > 0:
            print(f"保存目录: {current_dir}")
            print("\n图表内容说明:")
            print("- 🔴 红色线条: 阻力位 (Swing High点)")
            print("- 🟢 绿色线条: 支撑位 (Swing Low点)")
            print("- 🟣 紫色线条: 看涨FVG区域")
            print("- ⚫ 黑色线条: 看跌FVG区域")
            print("- 📊 成交量20日均线")
            print("- 📈 K线图 (红涨绿跌)")
            
            # 列出生成的文件
            print(f"\n生成的文件:")
            for timeframe, save_path in results.items():
                if save_path and os.path.exists(save_path):
                    print(f"  {os.path.basename(save_path)}")
        else:
            print("❌ 所有图表生成失败，请检查日志")
            
        return success_count > 0
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_save():
    """测试批量保存多个股票的图表"""
    try:
        from support_and_resistance_zone import batch_save_charts, XTDATA_AVAILABLE
        
        if not XTDATA_AVAILABLE:
            print("❌ xtdata未安装，无法测试批量保存功能")
            return False
        
        print("\n" + "=" * 80)
        print("测试批量保存多个股票的支撑阻力位图表")
        print("=" * 80)
        
        # 测试股票代码列表
        test_stocks = ["600895.SH", "000001.SZ"]  # 张江高科, 平安银行
        
        print(f"测试股票: {', '.join(test_stocks)}")
        print(f"时间周期: 15min, 60min")
        print()
        
        # 开始批量保存
        print("开始批量生成和保存图表...")
        start_time = datetime.now()
        
        results = batch_save_charts(
            stock_codes=test_stocks,
            timeframes=['15m', '60m'],
            swing_length=10
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 显示结果
        print(f"\n批量生成完成，耗时: {duration:.1f}秒")
        print("=" * 80)
        
        total_success = 0
        total_count = 0
        
        for stock_code, stock_results in results.items():
            print(f"\n{stock_code}:")
            stock_success = 0
            for timeframe, save_path in stock_results.items():
                total_count += 1
                if save_path and os.path.exists(save_path):
                    file_size = os.path.getsize(save_path) / 1024  # KB
                    print(f"  ✅ {timeframe}: {os.path.basename(save_path)} ({file_size:.1f}KB)")
                    stock_success += 1
                    total_success += 1
                else:
                    print(f"  ❌ {timeframe}: 保存失败")
            print(f"  小计: {stock_success}/{len(stock_results)} 个图表成功")
        
        print("=" * 80)
        print(f"总计: {total_success}/{total_count} 个图表保存成功")
        
        return total_success > 0
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 批量测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试15min和60min支撑阻力图表保存功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试单个股票保存
    success1 = test_save_charts()
    
    # 测试批量保存
    success2 = test_batch_save()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if success1:
        print("✅ 单个股票图表保存测试: 通过")
    else:
        print("❌ 单个股票图表保存测试: 失败")
    
    if success2:
        print("✅ 批量股票图表保存测试: 通过")
    else:
        print("❌ 批量股票图表保存测试: 失败")
    
    if success1 or success2:
        print("\n🎉 至少一个测试通过，功能基本可用！")
        print("\n使用方法:")
        print("```python")
        print("from support_and_resistance_zone import save_support_resistance_charts")
        print("")
        print("# 保存单个股票的15min和60min图表")
        print("results = save_support_resistance_charts('600895.SH')")
        print("")
        print("# 自定义参数")
        print("results = save_support_resistance_charts(")
        print("    stock_code='600895.SH',")
        print("    timeframes=['15m', '60m'],")
        print("    swing_length=10")
        print(")")
        print("```")
    else:
        print("\n❌ 所有测试失败，请检查环境配置")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
