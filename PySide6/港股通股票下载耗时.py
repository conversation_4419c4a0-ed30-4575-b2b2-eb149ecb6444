#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通股票下载耗时分析
使用xtdata.download_history_data分别下载15分钟和60分钟周期的历史数据
并用get_market_data获取这些数据并保存到csv文件中去
"""

import sys
import os
import time
import csv
from datetime import datetime, timedelta
import pandas as pd

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入xtquant
try:
    from xtquant import xtdata
    XTQUANT_AVAILABLE = True
    print("✅ xtquant导入成功")
except ImportError:
    XTQUANT_AVAILABLE = False
    print("❌ xtquant未安装，请先安装: pip install xtquant")
    sys.exit(1)

# 配置参数
STOCK_CODE = "06060.HK"  # 众安在线（测试股票）
TARGET_DATE = "20250725"  # 2025年7月25日
HK_FIFTEEN_MINUTE_CYCLE_COUNT = 2200  # 15分钟周期数据量
HK_SIXTY_MINUTE_CYCLE_COUNT = 1100    # 60分钟周期数据量

# 定义要获取的字段
FIELD_LIST = [
    'time',      # 时间戳
    'open',      # 开盘价
    'high',      # 最高价
    'low',       # 最低价
    'close',     # 收盘价
    'volume',    # 成交量
    'amount'     # 成交额
]

def check_xtquant_connection():
    """
    检查xtquant软件连接状态
    """
    try:
        # 尝试获取一个简单的数据来测试连接
        test_data = xtdata.get_market_data(
            field_list=['close'],
            stock_list=[STOCK_CODE],
            period='1d',
            count=1,
            dividend_type='none'
        )

        if test_data and 'close' in test_data:
            print("✅ xtquant连接正常")
            return True
        else:
            print("❌ xtquant软件未启动或连接失败")
            return False
    except Exception as e:
        print(f"❌ xtquant连接测试失败: {e}")
        return False

def calculate_date_range(target_date, period, count):
    """
    根据目标日期和数据量计算需要的历史数据范围

    参数:
    target_date: 目标日期 (YYYYMMDD格式)
    period: 时间周期 ('15m', '60m')
    count: 需要的K线数量

    返回:
    tuple: (start_date, end_date) YYYYMMDD格式
    """
    target_dt = datetime.strptime(target_date, '%Y%m%d')

    if period == '15m':
        # 15分钟K线，一天约26根（港股交易时间6.5小时），需要约85天
        days_needed = max(90, count // 20)  # 保守估计，确保数据充足
    elif period == '60m':
        # 60分钟K线，一天约6.5根，需要约170天
        days_needed = max(180, count // 5)  # 保守估计，确保数据充足
    else:
        days_needed = 30  # 默认30天

    start_dt = target_dt - timedelta(days=days_needed)

    start_date = start_dt.strftime('%Y%m%d')
    end_date = target_date

    return start_date, end_date


def download_history_data_with_timing(stock_code, period, start_date, end_date, count):
    """
    下载历史数据并记录耗时

    参数:
    stock_code: 股票代码
    period: 时间周期 ('15m', '60m')
    start_date: 开始日期 (YYYYMMDD格式)
    end_date: 结束日期 (YYYYMMDD格式)
    count: 期望的K线数量

    返回:
    tuple: (success, download_time)
    """
    try:
        print(f"\n📥 开始下载 {stock_code} {period} 周期历史数据...")
        print(f"   时间范围: {start_date} - {end_date}")
        print(f"   期望数量: {count} 根K线")

        start_time = time.time()

        # 下载历史数据到本地缓存
        xtdata.download_history_data(
            stock_code=stock_code,
            period=period,
            start_time=start_date,
            end_time=end_date,
            incrementally=True  # 使用增量下载
        )

        download_time = time.time() - start_time

        print(f"✅ {period} 周期历史数据下载完成")
        print(f"⏱️  下载耗时: {download_time:.2f} 秒")

        return True, download_time

    except Exception as e:
        download_time = time.time() - start_time if 'start_time' in locals() else 0
        print(f"❌ {period} 周期历史数据下载失败: {e}")
        return False, download_time


def get_market_data_with_timing(stock_code, period, count):
    """
    获取市场数据并记录耗时

    参数:
    stock_code: 股票代码
    period: 时间周期 ('15m', '60m')
    count: 期望的K线数量

    返回:
    tuple: (data, get_time, actual_count)
    """
    try:
        print(f"\n📊 开始获取 {stock_code} {period} 周期市场数据...")
        print(f"   期望数量: {count} 根K线")

        start_time = time.time()

        # 获取市场数据
        data = xtdata.get_market_data(
            field_list=FIELD_LIST,
            stock_list=[stock_code],
            period=period,
            count=count,
            dividend_type='none'
        )

        get_time = time.time() - start_time

        if not data:
            print(f"❌ {period} 周期数据为空")
            return None, get_time, 0

        # 检查数据结构并获取实际数量
        actual_count = 0
        if 'time' in data and isinstance(data['time'], pd.DataFrame):
            if stock_code in data['time'].index:
                actual_count = len(data['time'].columns)

        print(f"✅ {period} 周期市场数据获取完成")
        print(f"⏱️  获取耗时: {get_time:.2f} 秒")
        print(f"📊 实际获取: {actual_count} 根K线")

        return data, get_time, actual_count

    except Exception as e:
        get_time = time.time() - start_time if 'start_time' in locals() else 0
        print(f"❌ {period} 周期市场数据获取失败: {e}")
        return None, get_time, 0

def save_data_to_csv(data, period, stock_code, actual_count):
    """
    将K线数据保存到CSV文件

    参数:
    data: 从xtdata获取的原始数据
    period: 时间周期
    stock_code: 股票代码
    actual_count: 实际数据量
    """
    if not data:
        print(f"❌ {period} 周期无数据可保存")
        return False

    try:
        # 构造文件名
        date_str = TARGET_DATE[:4] + TARGET_DATE[4:6] + TARGET_DATE[6:8]
        filename = f"{stock_code}_{period}_{actual_count}bars_before_{date_str}.csv"
        filepath = os.path.join(os.path.dirname(__file__), filename)

        print(f"💾 保存 {period} 周期数据到文件: {filename}")

        # 获取时间数据
        if 'time' not in data or not isinstance(data['time'], pd.DataFrame):
            print(f"❌ 时间数据格式错误")
            return False

        time_data = data['time']
        if stock_code not in time_data.index:
            print(f"❌ 股票代码 {stock_code} 不在时间数据中")
            return False

        times = time_data.loc[stock_code].dropna().tolist()

        if not times:
            print(f"❌ {period} 周期无时间数据")
            return False

        # 获取OHLCV数据
        ohlcv_data = {}
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
            if field in data and isinstance(data[field], pd.DataFrame):
                if stock_code in data[field].index:
                    ohlcv_data[field] = data[field].loc[stock_code].dropna().tolist()
                else:
                    ohlcv_data[field] = []
            else:
                ohlcv_data[field] = []

        # 准备CSV数据
        csv_data = []

        # 添加表头
        headers = ['序号', '时间戳', '时间', '开盘', '最高', '最低', '收盘', '成交量', '成交额']
        csv_data.append(headers)

        # 添加数据行
        for i, timestamp in enumerate(times):
            # 转换时间戳
            try:
                if isinstance(timestamp, (int, float)):
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    time_str = str(timestamp)
            except:
                time_str = str(timestamp)

            # 获取OHLCV数据
            open_price = ohlcv_data['open'][i] if i < len(ohlcv_data['open']) else 0
            high_price = ohlcv_data['high'][i] if i < len(ohlcv_data['high']) else 0
            low_price = ohlcv_data['low'][i] if i < len(ohlcv_data['low']) else 0
            close_price = ohlcv_data['close'][i] if i < len(ohlcv_data['close']) else 0
            volume = ohlcv_data['volume'][i] if i < len(ohlcv_data['volume']) else 0
            amount = ohlcv_data['amount'][i] if i < len(ohlcv_data['amount']) else 0

            # 添加数据行
            row = [
                i + 1,  # 序号
                timestamp,  # 原始时间戳
                time_str,  # 格式化时间
                f"{open_price:.3f}",  # 开盘价
                f"{high_price:.3f}",  # 最高价
                f"{low_price:.3f}",   # 最低价
                f"{close_price:.3f}", # 收盘价
                f"{volume:.0f}",      # 成交量
                f"{amount:.0f}"       # 成交额
            ]
            csv_data.append(row)

        # 写入CSV文件
        with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(csv_data)

        print(f"✅ {period} 周期数据已保存到: {filepath}")
        print(f"   文件包含 {len(csv_data)-1} 条K线数据")

        return True

    except Exception as e:
        print(f"❌ 保存 {period} 周期CSV文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 港股通股票下载耗时分析")
    print("=" * 80)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📈 测试股票: {STOCK_CODE}")
    print(f"📊 目标日期: {TARGET_DATE[:4]}-{TARGET_DATE[4:6]}-{TARGET_DATE[6:8]} 之前")
    print(f"⏰ 测试周期: 15分钟({HK_FIFTEEN_MINUTE_CYCLE_COUNT}根) + 60分钟({HK_SIXTY_MINUTE_CYCLE_COUNT}根)")
    print("=" * 80)

    # 检查xtquant连接
    if not check_xtquant_connection():
        print("❌ xtquant连接失败，程序退出")
        return

    # 记录总开始时间
    total_start_time = time.time()

    # 存储结果
    results = {}

    # 测试周期列表
    test_periods = [
        ('15m', HK_FIFTEEN_MINUTE_CYCLE_COUNT),
        ('60m', HK_SIXTY_MINUTE_CYCLE_COUNT)
    ]

    for period, count in test_periods:
        print(f"\n{'='*60}")
        print(f"🔄 开始测试 {period} 周期数据 (目标: {count} 根K线)")
        print(f"{'='*60}")

        # 计算日期范围
        start_date, end_date = calculate_date_range(TARGET_DATE, period, count)
        print(f"📅 计算的数据范围: {start_date} - {end_date}")

        # 第一步：下载历史数据
        download_success, download_time = download_history_data_with_timing(
            STOCK_CODE, period, start_date, end_date, count
        )

        # 第二步：获取市场数据
        data, get_time, actual_count = get_market_data_with_timing(
            STOCK_CODE, period, count
        )

        # 第三步：保存到CSV
        save_success = False
        if data:
            save_success = save_data_to_csv(data, period, STOCK_CODE, actual_count)

        # 记录结果
        results[period] = {
            'target_count': count,
            'actual_count': actual_count,
            'download_success': download_success,
            'download_time': download_time,
            'get_time': get_time,
            'save_success': save_success,
            'total_time': download_time + get_time
        }

        print(f"\n📊 {period} 周期测试结果:")
        print(f"   下载状态: {'✅' if download_success else '❌'}")
        print(f"   下载耗时: {download_time:.2f} 秒")
        print(f"   获取耗时: {get_time:.2f} 秒")
        print(f"   总计耗时: {download_time + get_time:.2f} 秒")
        print(f"   目标数量: {count} 根")
        print(f"   实际数量: {actual_count} 根")
        print(f"   数据完整度: {(actual_count/count*100):.1f}%" if count > 0 else "N/A")
        print(f"   保存状态: {'✅' if save_success else '❌'}")

    # 计算总耗时
    total_time = time.time() - total_start_time

    # 打印总结报告
    print(f"\n{'='*80}")
    print("📋 测试总结报告")
    print(f"{'='*80}")

    for period, result in results.items():
        print(f"\n🔸 {period} 周期:")
        print(f"   目标/实际: {result['target_count']}/{result['actual_count']} 根")
        print(f"   下载耗时: {result['download_time']:.2f} 秒")
        print(f"   获取耗时: {result['get_time']:.2f} 秒")
        print(f"   周期总耗时: {result['total_time']:.2f} 秒")

    print(f"\n⏱️  程序总耗时: {total_time:.2f} 秒")
    print("✅ 程序执行完成")


if __name__ == "__main__":
    main()