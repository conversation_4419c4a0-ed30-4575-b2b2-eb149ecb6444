#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线绘制工具
用于绘制D盘stock_data文件夹中15分钟周期股票数据的K线图
从2025年2月开始任意一天，绘制前半年的数据
"""

import pandas as pd
import mplfinance as mpf
import os
import datetime
import numpy as np
from pathlib import Path


class KLinePlotter:
    """K线绘制器"""
    
    def __init__(self, data_folder="D:/stock_data"):
        """
        初始化K线绘制器
        
        Args:
            data_folder (str): 股票数据文件夹路径
        """
        self.data_folder = data_folder
        
    def load_stock_data(self, stock_file):
        """
        加载股票数据
        
        Args:
            stock_file (str): 股票数据文件名
            
        Returns:
            pd.DataFrame: 处理后的股票数据
        """
        file_path = os.path.join(self.data_folder, stock_file)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        # 读取CSV文件
        df = pd.read_csv(file_path)
        
        # 合并date和time列为datetime索引
        df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['time'])
        df.set_index('datetime', inplace=True)
        
        # 删除原始的date和time列
        df.drop(['date', 'time'], axis=1, inplace=True)
        
        # 重命名列以符合mplfinance要求
        df.rename(columns={
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        }, inplace=True)
        
        # 确保数据类型正确
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            
        # 删除包含NaN的行
        df.dropna(inplace=True)
        
        return df
    
    def filter_data_by_date(self, df, target_date, months_before=6):
        """
        根据目标日期筛选前半年的数据
        
        Args:
            df (pd.DataFrame): 股票数据
            target_date (str): 目标日期，格式：'2025-02-15'
            months_before (int): 向前取多少个月的数据
            
        Returns:
            pd.DataFrame: 筛选后的数据
        """
        target_dt = pd.to_datetime(target_date)
        start_date = target_dt - pd.DateOffset(months=months_before)
        
        # 筛选数据
        filtered_df = df[(df.index >= start_date) & (df.index <= target_dt)]
        
        print(f"数据筛选范围: {start_date.strftime('%Y-%m-%d')} 到 {target_dt.strftime('%Y-%m-%d')}")
        print(f"筛选后数据条数: {len(filtered_df)}")
        
        return filtered_df

    def find_swing_points(self, df, swing_length=10):
        """
        检测swing high和swing low点

        Args:
            df (pd.DataFrame): 股票数据，包含High和Low列
            swing_length (int): 左右确认周期长度，默认10

        Returns:
            tuple: (swing_highs, swing_lows) 两个包含swing点信息的列表
        """
        swing_highs = []
        swing_lows = []

        # 确保数据足够长
        if len(df) < swing_length * 2 + 1:
            return swing_highs, swing_lows

        # 遍历可能的swing点（排除两端不足swing_length的部分）
        for i in range(swing_length, len(df) - swing_length):
            current_high = df.iloc[i]['High']
            current_low = df.iloc[i]['Low']
            current_time = df.index[i]

            # 检查swing high: 当前high必须严格高于左右各swing_length个K线的high
            is_swing_high = True
            for j in range(i - swing_length, i + swing_length + 1):
                if j != i and df.iloc[j]['High'] >= current_high:  # 注意是>=，确保严格高于
                    is_swing_high = False
                    break

            if is_swing_high:
                swing_highs.append({
                    'index': i,
                    'time': current_time,
                    'price': current_high
                })

            # 检查swing low: 当前low必须严格低于左右各swing_length个K线的low
            is_swing_low = True
            for j in range(i - swing_length, i + swing_length + 1):
                if j != i and df.iloc[j]['Low'] <= current_low:  # 注意是<=，确保严格低于
                    is_swing_low = False
                    break

            if is_swing_low:
                swing_lows.append({
                    'index': i,
                    'time': current_time,
                    'price': current_low
                })

        return swing_highs, swing_lows

    def find_breakthrough_point(self, df, swing_point, is_resistance=True):
        """
        查找水平线被突破的位置

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息，包含index和price
            is_resistance (bool): True表示阻力线，False表示支撑线

        Returns:
            int: 突破位置的索引，如果没有突破则返回None
        """
        pivot_index = swing_point['index']
        pivot_price = swing_point['price']

        # 从枢轴点之后开始检查突破
        for i in range(pivot_index + 1, len(df)):
            if is_resistance:
                # 阻力线被上穿：K线最高价超过阻力线价格
                if df.iloc[i]['High'] > pivot_price:
                    return i
            else:
                # 支撑线被下穿：K线最低价低于支撑线价格
                if df.iloc[i]['Low'] < pivot_price:
                    return i

        # 没有找到突破点
        return None

    def detect_fvg(self, df, start_index=0, end_index=None):
        """
        检测FVG (Fair Value Gap) 公允价值缺口

        Args:
            df (pd.DataFrame): 股票数据
            start_index (int): 开始检测的索引
            end_index (int): 结束检测的索引，如果为None则检测到末尾

        Returns:
            list: FVG列表，每个FVG包含类型、价格范围、时间等信息
        """
        if end_index is None:
            end_index = len(df)

        fvgs = []

        # 需要至少3根K线才能检测FVG
        for i in range(start_index, min(end_index - 2, len(df) - 2)):
            k1 = df.iloc[i]
            k2 = df.iloc[i + 1]
            k3 = df.iloc[i + 2]

            # 看涨FVG检测：第一根K线最高价 < 第三根K线最低价
            if k1['High'] < k3['Low']:
                fvg_bottom = k1['High']
                fvg_top = k3['Low']
                fvgs.append({
                    'type': 'bullish',
                    'start_index': i,
                    'end_index': i + 2,
                    'start_time': df.index[i],
                    'end_time': df.index[i + 2],
                    'bottom': fvg_bottom,
                    'top': fvg_top
                })

            # 看跌FVG检测：第一根K线最低价 > 第三根K线最高价
            elif k1['Low'] > k3['High']:
                fvg_bottom = k3['High']
                fvg_top = k1['Low']
                fvgs.append({
                    'type': 'bearish',
                    'start_index': i,
                    'end_index': i + 2,
                    'start_time': df.index[i],
                    'end_time': df.index[i + 2],
                    'bottom': fvg_bottom,
                    'top': fvg_top
                })

        return fvgs

    def check_fvg_breakthrough(self, df, fvg):
        """
        检查FVG是否被价格完全穿过

        新定义：
        1. 看跌FVG之后的某根K线的最高价只要全部穿过该FVG区域，该FVG区域就应该被删除
        2. 看涨FVG之后的某根K线的最低价只要全部穿过该FVG区域，该FVG区域就应该被删除

        Args:
            df (pd.DataFrame): 股票数据
            fvg (dict): FVG信息，包含类型、价格范围等

        Returns:
            bool: True表示FVG被完全穿过，False表示未被完全穿过
        """
        fvg_end_index = fvg['end_index']
        fvg_type = fvg['type']
        fvg_top = fvg['top']
        fvg_bottom = fvg['bottom']

        # 从FVG结束后开始检查突破
        for i in range(fvg_end_index + 1, len(df)):
            current_candle = df.iloc[i]

            if fvg_type == 'bullish':
                # 看涨FVG被完全下穿：K线最低价全部穿过FVG区域（低于FVG底部）
                # 意思是价格从FVG顶部以上完全穿过到FVG底部以下
                if current_candle['Low'] < fvg_bottom:
                    return True
            elif fvg_type == 'bearish':
                # 看跌FVG被完全上穿：K线最高价全部穿过FVG区域（高于FVG顶部）
                # 意思是价格从FVG底部以下完全穿过到FVG顶部以上
                if current_candle['High'] > fvg_top:
                    return True

        # 没有被完全穿过
        return False

    def find_fvg_in_range(self, df, swing_point, fvg_type, range_limit=10):
        """
        在swing点后的指定范围内寻找特定类型的FVG，并过滤掉被突破的FVG

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'
            range_limit (int): 搜索范围限制（K线数量）

        Returns:
            list: 找到的未被突破的FVG列表
        """
        pivot_index = swing_point['index']
        start_index = pivot_index + 1
        end_index = min(start_index + range_limit, len(df))

        # 在指定范围内检测FVG
        all_fvgs = self.detect_fvg(df, start_index, end_index)

        # 筛选指定类型的FVG
        filtered_fvgs = [fvg for fvg in all_fvgs if fvg['type'] == fvg_type]

        # 进一步过滤掉被突破的FVG
        valid_fvgs = []
        for fvg in filtered_fvgs:
            if not self.check_fvg_breakthrough(df, fvg):
                valid_fvgs.append(fvg)

        return valid_fvgs

    def find_extreme_fvg_in_range(self, df, swing_point, fvg_type, range_limit=10):
        """
        在swing点后的指定范围内寻找价格最极端的FVG区域

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'
            range_limit (int): 搜索范围限制（K线数量）

        Returns:
            dict: 最极端的FVG区域，如果没有找到则返回None
        """
        # 获取所有有效的FVG
        valid_fvgs = self.find_fvg_in_range(df, swing_point, fvg_type, range_limit)

        if not valid_fvgs:
            return None

        # 根据FVG类型选择最极端的FVG
        if fvg_type == 'bearish':
            # 对于看跌FVG，选择价格最高的（top价格最高的）
            extreme_fvg = max(valid_fvgs, key=lambda fvg: fvg['top'])
        elif fvg_type == 'bullish':
            # 对于看涨FVG，选择价格最低的（bottom价格最低的）
            extreme_fvg = min(valid_fvgs, key=lambda fvg: fvg['bottom'])
        else:
            return None

        return extreme_fvg

    def create_rectangle_zone(self, swing_point, extreme_fvg, is_resistance=True):
        """
        创建矩形支撑/阻力区域

        Args:
            swing_point (dict): swing点信息
            extreme_fvg (dict): 最极端的FVG区域
            is_resistance (bool): True表示阻力区域，False表示支撑区域

        Returns:
            dict: 矩形区域信息，包含top、bottom、start_index等
        """
        if extreme_fvg is None:
            return None

        swing_price = swing_point['price']
        swing_index = swing_point['index']

        if is_resistance:
            # 阻力区域：swing high价格和看跌FVG的最高价格
            zone_top = max(swing_price, extreme_fvg['top'])
            zone_bottom = min(swing_price, extreme_fvg['bottom'])
        else:
            # 支撑区域：swing low价格和看涨FVG的最低价格
            zone_top = max(swing_price, extreme_fvg['top'])
            zone_bottom = min(swing_price, extreme_fvg['bottom'])

        return {
            'top': zone_top,
            'bottom': zone_bottom,
            'start_index': swing_index,
            'swing_point': swing_point,
            'extreme_fvg': extreme_fvg,
            'is_resistance': is_resistance
        }

    def check_swing_had_fvg(self, df, swing_point, fvg_type, range_limit=10):
        """
        检查swing点是否曾经有过FVG（包括已被突破的）

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'
            range_limit (int): 搜索范围限制（K线数量）

        Returns:
            bool: True表示曾经有过FVG，False表示从未有过FVG
        """
        pivot_index = swing_point['index']
        start_index = pivot_index + 1
        end_index = min(start_index + range_limit, len(df))

        # 在指定范围内检测所有FVG（不过滤突破）
        all_fvgs = self.detect_fvg(df, start_index, end_index)

        # 筛选指定类型的FVG
        filtered_fvgs = [fvg for fvg in all_fvgs if fvg['type'] == fvg_type]

        # 只要找到过FVG就返回True，不管是否被突破
        return len(filtered_fvgs) > 0

    def plot_kline(self, df, stock_code, target_date, save_path=None, show_swing_points=True, show_horizontal_lines=True, show_fvg=True, swing_length=10):
        """
        绘制K线图

        Args:
            df (pd.DataFrame): 股票数据
            stock_code (str): 股票代码
            target_date (str): 目标日期
            save_path (str): 保存路径，如果为None则显示图表
            show_swing_points (bool): 是否显示swing点标记，默认True
            show_horizontal_lines (bool): 是否显示水平支撑阻力线，默认True
            show_fvg (bool): 是否显示FVG区域，默认True
            swing_length (int): swing检测的左右确认周期，默认10
        """
        if df.empty:
            print("数据为空，无法绘制K线图")
            return

        # 设置中文字体和禁用警告
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        import warnings

        # 禁用字体相关警告
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
        warnings.filterwarnings('ignore', category=UserWarning, module='mplfinance')

        # 尝试设置中文字体
        chinese_font_found = False
        try:
            # 优先使用系统中文字体
            chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong']
            available_fonts = [f.name for f in fm.fontManager.ttflist]

            for font in chinese_fonts:
                if font in available_fonts:
                    plt.rcParams['font.sans-serif'] = [font, 'DejaVu Sans']
                    chinese_font_found = True
                    break

            if not chinese_font_found:
                # 使用英文标题避免字体警告
                print("提示: 使用英文标题以避免字体显示问题")

        except Exception as e:
            print(f"字体设置提示: 将使用默认字体")

        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置K线图样式
        mc = mpf.make_marketcolors(
            up='red',      # 上涨为红色
            down='green',  # 下跌为绿色
            edge='inherit',
            wick={'up': 'red', 'down': 'green'},
            volume='in'
        )
        
        style = mpf.make_mpf_style(
            marketcolors=mc,
            gridstyle='-',
            y_on_right=True
        )
        
        # 创建图表标题 - 根据字体支持情况选择中英文
        if chinese_font_found:
            title = f"{stock_code} K线图 (15分钟) - 截至{target_date}前半年数据"
            ylabel = '价格'
            ylabel_lower = '成交量'
        else:
            title = f"{stock_code} Candlestick Chart (15min) - 6 months before {target_date}"
            ylabel = 'Price'
            ylabel_lower = 'Volume'

        # 准备附加绘图数据
        addplot_data = []

        if show_swing_points or show_horizontal_lines:
            # 检测swing点
            swing_highs, swing_lows = self.find_swing_points(df, swing_length)

            # 如果启用FVG过滤，则只保留在深色区域内有FVG的swing点
            # 但是如果swing点是全图的最大值或最小值，则无论是否有FVG都要保留
            if show_fvg:
                # 找到全图的最高价和最低价
                global_high = df['High'].max()
                global_low = df['Low'].min()

                # 过滤swing high点：保留曾经有过下跌FVG的点，或者是全图最高点
                filtered_swing_highs = []
                for swing in swing_highs:
                    # 检查是否曾经有过下跌FVG（包括已被突破的）
                    had_bearish_fvg = self.check_swing_had_fvg(df, swing, 'bearish', 10)
                    # 如果曾经有过下跌FVG，或者是全图最高点，保留这个swing high点
                    if had_bearish_fvg or swing['price'] == global_high:
                        filtered_swing_highs.append(swing)
                swing_highs = filtered_swing_highs

                # 过滤swing low点：保留曾经有过上涨FVG的点，或者是全图最低点
                filtered_swing_lows = []
                for swing in swing_lows:
                    # 检查是否曾经有过上涨FVG（包括已被突破的）
                    had_bullish_fvg = self.check_swing_had_fvg(df, swing, 'bullish', 10)
                    # 如果曾经有过上涨FVG，或者是全图最低点，保留这个swing low点
                    if had_bullish_fvg or swing['price'] == global_low:
                        filtered_swing_lows.append(swing)
                swing_lows = filtered_swing_lows

            if swing_highs or swing_lows:
                # 创建用于标记swing点的Series
                swing_high_markers = pd.Series(index=df.index, dtype=float)
                swing_low_markers = pd.Series(index=df.index, dtype=float)

                if show_swing_points:
                    # 只标记未被突破的swing点
                    # 标记swing high点（在K线上方）- 只显示未被突破的
                    for swing in swing_highs:
                        # 检查阻力线是否被突破
                        breakthrough_index = self.find_breakthrough_point(df, swing, is_resistance=True)
                        # 只有未被突破的swing high点才显示标记
                        if breakthrough_index is None:
                            # 在high价格上方一点位置标记
                            marker_price = swing['price'] * 1.002  # 上方0.2%位置
                            swing_high_markers.loc[swing['time']] = marker_price

                    # 标记swing low点（在K线下方）- 只显示未被突破的
                    for swing in swing_lows:
                        # 检查支撑线是否被突破
                        breakthrough_index = self.find_breakthrough_point(df, swing, is_resistance=False)
                        # 只有未被突破的swing low点才显示标记
                        if breakthrough_index is None:
                            # 在low价格下方一点位置标记
                            marker_price = swing['price'] * 0.998  # 下方0.2%位置
                            swing_low_markers.loc[swing['time']] = marker_price

                    # 添加swing high标记（红色圆圈，阻力位）- 只显示有效的
                    if not swing_high_markers.isna().all():
                        addplot_data.append(mpf.make_addplot(
                            swing_high_markers,
                            type='scatter',
                            markersize=80,
                            marker='o',
                            color='red',
                            alpha=0.7,
                            secondary_y=False
                        ))

                    # 添加swing low标记（绿色圆圈，支撑位）- 只显示有效的
                    if not swing_low_markers.isna().all():
                        addplot_data.append(mpf.make_addplot(
                            swing_low_markers,
                            type='scatter',
                            markersize=80,
                            marker='o',
                            color='green',
                            alpha=0.7,
                            secondary_y=False
                        ))

                if show_horizontal_lines:
                    # 绘制水平支撑阻力线（只显示未被突破的线）
                    # 如果水平线被突破，则整条线都不显示
                    # 水平线分为两部分：前10根K线用深色，10根K线之后用浅色

                    # 处理swing high的水平阻力线
                    for swing in swing_highs:
                        pivot_index = swing['index']

                        # 检查阻力线是否被突破
                        breakthrough_index = self.find_breakthrough_point(df, swing, is_resistance=True)

                        # 只有未被突破的阻力线才显示
                        if breakthrough_index is None:
                            # 计算线段的结束位置
                            end_index = len(df) - 1

                            # 第一部分：前10根K线（深色）
                            first_part_end = min(pivot_index + 10, end_index)
                            if first_part_end > pivot_index:
                                resistance_line_dark = pd.Series(index=df.index, dtype=float)
                                for j in range(pivot_index, first_part_end + 1):
                                    resistance_line_dark.iloc[j] = swing['price']

                                # 添加深色阻力线（深红色虚线）
                                addplot_data.append(mpf.make_addplot(
                                    resistance_line_dark,
                                    type='line',
                                    color='darkred',
                                    linestyle='--',
                                    width=1.5,
                                    alpha=0.8,
                                    secondary_y=False
                                ))

                                # 在深色区域内绘制下跌FVG（swing high阻力位）
                                if show_fvg:
                                    bearish_fvgs = self.find_fvg_in_range(df, swing, 'bearish', 10)

                                    # 找到价格最高的FVG区域，创建矩形阻力位区域
                                    extreme_fvg = self.find_extreme_fvg_in_range(df, swing, 'bearish', 10)
                                    if extreme_fvg:
                                        rectangle_zone = self.create_rectangle_zone(swing, extreme_fvg, is_resistance=True)
                                        if rectangle_zone:
                                            # 绘制矩形阻力位区域（从swing点开始到图表结束）
                                            rect_top_line = pd.Series(index=df.index, dtype=float)
                                            rect_bottom_line = pd.Series(index=df.index, dtype=float)
                                            rect_fill_line = pd.Series(index=df.index, dtype=float)

                                            # 矩形区域从swing点开始延伸到图表末尾
                                            for j in range(pivot_index, len(df)):
                                                rect_top_line.iloc[j] = rectangle_zone['top']
                                                rect_bottom_line.iloc[j] = rectangle_zone['bottom']
                                                # 填充区域使用中间价格
                                                rect_fill_line.iloc[j] = (rectangle_zone['top'] + rectangle_zone['bottom']) / 2

                                            # 创建多条填充线来模拟填充效果（浅红色）
                                            fill_steps = 20  # 填充线条数
                                            step_size = (rectangle_zone['top'] - rectangle_zone['bottom']) / fill_steps
                                            for step in range(fill_steps):
                                                fill_price = rectangle_zone['bottom'] + step * step_size
                                                rect_fill_step = pd.Series(index=df.index, dtype=float)
                                                for j in range(pivot_index, len(df)):
                                                    rect_fill_step.iloc[j] = fill_price

                                                addplot_data.append(mpf.make_addplot(
                                                    rect_fill_step,
                                                    type='line',
                                                    color='lightcoral',
                                                    linestyle='-',
                                                    width=0.5,
                                                    alpha=0.15,
                                                    secondary_y=False
                                                ))

                                            # 添加矩形区域上边界（深红色粗实线）
                                            addplot_data.append(mpf.make_addplot(
                                                rect_top_line,
                                                type='line',
                                                color='darkred',
                                                linestyle='-',
                                                width=3,
                                                alpha=0.8,
                                                secondary_y=False
                                            ))

                                            # 添加矩形区域下边界（深红色粗实线）
                                            addplot_data.append(mpf.make_addplot(
                                                rect_bottom_line,
                                                type='line',
                                                color='darkred',
                                                linestyle='-',
                                                width=3,
                                                alpha=0.8,
                                                secondary_y=False
                                            ))

                                    # 继续绘制原有的FVG区域（在深色区域内）
                                    for fvg in bearish_fvgs:
                                        # 创建FVG区域的上下边界线
                                        fvg_top_line = pd.Series(index=df.index, dtype=float)
                                        fvg_bottom_line = pd.Series(index=df.index, dtype=float)

                                        # 只在深色区域范围内绘制FVG
                                        fvg_start = max(fvg['start_index'], pivot_index)
                                        fvg_end = min(fvg['end_index'], first_part_end)

                                        if fvg_start <= fvg_end:
                                            for j in range(fvg_start, fvg_end + 1):
                                                fvg_top_line.iloc[j] = fvg['top']
                                                fvg_bottom_line.iloc[j] = fvg['bottom']

                                            # 添加FVG上边界（深红色实线）
                                            addplot_data.append(mpf.make_addplot(
                                                fvg_top_line,
                                                type='line',
                                                color='darkred',
                                                linestyle='-',
                                                width=2,
                                                alpha=0.9,
                                                secondary_y=False
                                            ))

                                            # 添加FVG下边界（深红色实线）
                                            addplot_data.append(mpf.make_addplot(
                                                fvg_bottom_line,
                                                type='line',
                                                color='darkred',
                                                linestyle='-',
                                                width=2,
                                                alpha=0.9,
                                                secondary_y=False
                                            ))

                            # 第二部分：10根K线之后（浅色）
                            if first_part_end < end_index:
                                resistance_line_light = pd.Series(index=df.index, dtype=float)
                                for j in range(first_part_end + 1, end_index + 1):
                                    resistance_line_light.iloc[j] = swing['price']

                                # 添加浅色阻力线（浅红色虚线）
                                addplot_data.append(mpf.make_addplot(
                                    resistance_line_light,
                                    type='line',
                                    color='lightcoral',
                                    linestyle='--',
                                    width=1,
                                    alpha=0.5,
                                    secondary_y=False
                                ))

                    # 处理swing low的水平支撑线
                    for swing in swing_lows:
                        pivot_index = swing['index']

                        # 检查支撑线是否被突破
                        breakthrough_index = self.find_breakthrough_point(df, swing, is_resistance=False)

                        # 只有未被突破的支撑线才显示
                        if breakthrough_index is None:
                            # 计算线段的结束位置
                            end_index = len(df) - 1

                            # 第一部分：前10根K线（深色）
                            first_part_end = min(pivot_index + 10, end_index)
                            if first_part_end > pivot_index:
                                support_line_dark = pd.Series(index=df.index, dtype=float)
                                for j in range(pivot_index, first_part_end + 1):
                                    support_line_dark.iloc[j] = swing['price']

                                # 添加深色支撑线（深绿色虚线）
                                addplot_data.append(mpf.make_addplot(
                                    support_line_dark,
                                    type='line',
                                    color='darkgreen',
                                    linestyle='--',
                                    width=1.5,
                                    alpha=0.8,
                                    secondary_y=False
                                ))

                                # 在深色区域内绘制上涨FVG（swing low支撑位）
                                if show_fvg:
                                    bullish_fvgs = self.find_fvg_in_range(df, swing, 'bullish', 10)

                                    # 找到价格最低的FVG区域，创建矩形支撑位区域
                                    extreme_fvg = self.find_extreme_fvg_in_range(df, swing, 'bullish', 10)
                                    if extreme_fvg:
                                        rectangle_zone = self.create_rectangle_zone(swing, extreme_fvg, is_resistance=False)
                                        if rectangle_zone:
                                            # 绘制矩形支撑位区域（从swing点开始到图表结束）
                                            rect_top_line = pd.Series(index=df.index, dtype=float)
                                            rect_bottom_line = pd.Series(index=df.index, dtype=float)

                                            # 矩形区域从swing点开始延伸到图表末尾
                                            for j in range(pivot_index, len(df)):
                                                rect_top_line.iloc[j] = rectangle_zone['top']
                                                rect_bottom_line.iloc[j] = rectangle_zone['bottom']

                                            # 创建多条填充线来模拟填充效果（浅绿色）
                                            fill_steps = 20  # 填充线条数
                                            step_size = (rectangle_zone['top'] - rectangle_zone['bottom']) / fill_steps
                                            for step in range(fill_steps):
                                                fill_price = rectangle_zone['bottom'] + step * step_size
                                                rect_fill_step = pd.Series(index=df.index, dtype=float)
                                                for j in range(pivot_index, len(df)):
                                                    rect_fill_step.iloc[j] = fill_price

                                                addplot_data.append(mpf.make_addplot(
                                                    rect_fill_step,
                                                    type='line',
                                                    color='lightgreen',
                                                    linestyle='-',
                                                    width=0.5,
                                                    alpha=0.15,
                                                    secondary_y=False
                                                ))

                                            # 添加矩形区域上边界（深绿色粗实线）
                                            addplot_data.append(mpf.make_addplot(
                                                rect_top_line,
                                                type='line',
                                                color='darkgreen',
                                                linestyle='-',
                                                width=3,
                                                alpha=0.8,
                                                secondary_y=False
                                            ))

                                            # 添加矩形区域下边界（深绿色粗实线）
                                            addplot_data.append(mpf.make_addplot(
                                                rect_bottom_line,
                                                type='line',
                                                color='darkgreen',
                                                linestyle='-',
                                                width=3,
                                                alpha=0.8,
                                                secondary_y=False
                                            ))

                                    # 继续绘制原有的FVG区域（在深色区域内）
                                    for fvg in bullish_fvgs:
                                        # 创建FVG区域的上下边界线
                                        fvg_top_line = pd.Series(index=df.index, dtype=float)
                                        fvg_bottom_line = pd.Series(index=df.index, dtype=float)

                                        # 只在深色区域范围内绘制FVG
                                        fvg_start = max(fvg['start_index'], pivot_index)
                                        fvg_end = min(fvg['end_index'], first_part_end)

                                        if fvg_start <= fvg_end:
                                            for j in range(fvg_start, fvg_end + 1):
                                                fvg_top_line.iloc[j] = fvg['top']
                                                fvg_bottom_line.iloc[j] = fvg['bottom']

                                            # 添加FVG上边界（深绿色实线）
                                            addplot_data.append(mpf.make_addplot(
                                                fvg_top_line,
                                                type='line',
                                                color='darkgreen',
                                                linestyle='-',
                                                width=2,
                                                alpha=0.9,
                                                secondary_y=False
                                            ))

                                            # 添加FVG下边界（深绿色实线）
                                            addplot_data.append(mpf.make_addplot(
                                                fvg_bottom_line,
                                                type='line',
                                                color='darkgreen',
                                                linestyle='-',
                                                width=2,
                                                alpha=0.9,
                                                secondary_y=False
                                            ))

                            # 第二部分：10根K线之后（浅色）
                            if first_part_end < end_index:
                                support_line_light = pd.Series(index=df.index, dtype=float)
                                for j in range(first_part_end + 1, end_index + 1):
                                    support_line_light.iloc[j] = swing['price']

                                # 添加浅色支撑线（浅绿色虚线）
                                addplot_data.append(mpf.make_addplot(
                                    support_line_light,
                                    type='line',
                                    color='lightgreen',
                                    linestyle='--',
                                    width=1,
                                    alpha=0.5,
                                    secondary_y=False
                                ))
        
        # 绘制K线图
        plot_kwargs = {
            'type': 'candle',
            'style': style,
            'title': title,
            'ylabel': ylabel,
            'ylabel_lower': ylabel_lower,
            'volume': True,
            'figsize': (16, 10),
            'show_nontrading': False,
            'warn_too_much_data': len(df) + 100  # 避免数据量警告
        }

        # 添加swing点标记
        if addplot_data:
            plot_kwargs['addplot'] = addplot_data

        # 只有当save_path不为None时才添加savefig参数
        if save_path:
            plot_kwargs['savefig'] = save_path

        mpf.plot(df, **plot_kwargs)
        
        if not save_path:
            import matplotlib.pyplot as plt
            plt.show()
    
    def plot_stock_kline(self, stock_file, target_date="2025-02-15", save_path=None, show_swing_points=True, show_horizontal_lines=True, show_fvg=True, swing_length=10):
        """
        绘制指定股票的K线图

        Args:
            stock_file (str): 股票文件名，如 "600895.SH_15m_20240701_20250701_all_front.csv"
            target_date (str): 目标日期，格式：'2025-02-15'
            save_path (str): 保存路径，如果为None则显示图表
            show_swing_points (bool): 是否显示swing点标记，默认True
            show_horizontal_lines (bool): 是否显示水平支撑阻力线，默认True
            show_fvg (bool): 是否显示FVG区域，默认True
            swing_length (int): swing检测的左右确认周期，默认10
        """
        try:
            # 从文件名提取股票代码
            stock_code = stock_file.split('_')[0]
            
            print(f"正在加载股票数据: {stock_file}")
            print(f"股票代码: {stock_code}")
            print(f"目标日期: {target_date}")
            
            # 加载数据
            df = self.load_stock_data(stock_file)
            print(f"原始数据条数: {len(df)}")
            print(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")
            
            # 筛选前半年数据
            filtered_df = self.filter_data_by_date(df, target_date)
            
            if filtered_df.empty:
                print("筛选后数据为空，请检查目标日期是否在数据范围内")
                return

            # 绘制K线图
            self.plot_kline(filtered_df, stock_code, target_date, save_path, show_swing_points, show_horizontal_lines, show_fvg, swing_length)
            
            print("K线图绘制完成！")
            
        except Exception as e:
            print(f"绘制K线图时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """主函数 - 示例用法"""
    # 创建K线绘制器
    plotter = KLinePlotter()
    
    # 示例：绘制600895.SH的K线图
    stock_file = "000021.SZ_15m_20240701_20250701_all_front.csv"
    target_date = "2025-02-15"  # 可以修改为2025年2月的任意日期
    
    # 绘制K线图（显示）
    plotter.plot_stock_kline(stock_file, target_date)
    
    # 如果要保存图片，可以指定保存路径
    # save_path = "kline_chart.png"
    # plotter.plot_stock_kline(stock_file, target_date, save_path)


if __name__ == "__main__":
    main()
