# 多标签页支撑阻力位独立性测试说明

## 问题描述
用户反馈：多个标签页中，只有一个标签页含有支撑阻力位水平线，但需求是所有标签页的股票都有属于自己的支撑阻力位水平线。

## 问题原因
原来的支撑阻力位缓存机制使用全局变量，缓存键格式为 `symbol_timeframe`，在多标签页环境下会导致：
1. 不同标签页的相同股票会共享缓存
2. `clear_all_old_stocks_support_resistance` 函数会清除所有标签页的旧股票支撑阻力位
3. 支撑阻力位线条对象被误删，导致其他标签页失去支撑阻力位显示

## 解决方案

### 1. 为每个图表添加唯一标识
```python
# 在 ChartTab.create_charts() 中
self.tab_id = f"tab_{int(time.time() * 1000)}"  # 标签页唯一ID
chart.chart_id = f"{self.tab_id}_chart_{i}"     # 图表唯一ID
```

### 2. 修改缓存键格式
```python
# 原来：symbol_timeframe
# 现在：chart_id_symbol_timeframe
cache_key = f"{chart_id}_{symbol}_{timeframe}"
```

### 3. 独立的支撑阻力位清理
```python
# 原来：清除所有标签页的旧股票支撑阻力位
clear_all_old_stocks_support_resistance(searched_string)

# 现在：只清除当前标签页的旧股票支撑阻力位
self.clear_current_tab_support_resistance(searched_string)
```

### 4. 图表级别的缓存管理
每个图表现在使用独立的缓存空间，不会互相干扰。

## 测试步骤

### 步骤1：启动程序
```bash
python "PySide6/pyside6_2.28优秀不卡实时数据版.py"
```

### 步骤2：验证第一个标签页
1. 程序启动后，默认显示 600895.SH 的四分屏图表
2. 观察15分钟和60分钟图表是否显示支撑阻力位水平线
3. 确认支撑阻力位线条正常显示

### 步骤3：添加第二个标签页
1. 点击右上角的 "+" 按钮
2. 新标签页会自动创建并显示相同的股票代码
3. 观察新标签页的15分钟和60分钟图表是否也显示支撑阻力位

### 步骤4：在第二个标签页输入不同股票
1. 在第二个标签页的任意图表中输入新的股票代码（如：000001.SZ）
2. 等待数据加载完成
3. 观察第二个标签页是否显示新股票的支撑阻力位
4. **关键验证**：切换回第一个标签页，确认原股票的支撑阻力位仍然存在

### 步骤5：添加第三个标签页
1. 再次点击 "+" 按钮添加第三个标签页
2. 在第三个标签页输入另一个股票代码（如：002027.SZ）
3. 验证三个标签页都有各自独立的支撑阻力位显示

## 预期结果

✅ **每个标签页独立显示支撑阻力位**
- 第一个标签页：600895.SH 的支撑阻力位
- 第二个标签页：000001.SZ 的支撑阻力位  
- 第三个标签页：002027.SZ 的支撑阻力位

✅ **标签页间不互相干扰**
- 在任意标签页切换股票时，其他标签页的支撑阻力位保持不变
- 每个标签页的15分钟和60分钟图表都正常显示支撑阻力位

✅ **缓存机制正常工作**
- 控制台输出显示每个图表的唯一标识
- 支撑阻力位缓存键包含图表ID，确保独立性

## 技术验证点

### 控制台输出验证
程序运行时应该看到类似输出：
```
添加新标签页: 600895.SH
DEBUG: 清除图表 tab_1753161260178_chart_0 的旧股票支撑阻力位，新股票: 600895.SH
DEBUG: 初始化图表 tab_1753161260178_chart_0 的线条缓存: 600895.SH_15min
```

### 缓存键格式验证
新的缓存键应该包含图表ID：
- `tab_1753161260178_chart_0_600895.SH_15min`
- `tab_1753161260178_chart_2_600895.SH_60min`

## 修复状态
✅ **已修复** - 多标签页支撑阻力位独立性问题已解决
- 每个标签页的图表都有独立的支撑阻力位显示
- 标签页间的支撑阻力位不会互相干扰
- 缓存机制正确区分不同图表的数据
