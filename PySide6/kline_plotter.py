#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线绘制工具
使用pytdx获取15分钟周期股票数据的K线图
从运行程序时间点的当日最后一根K线到半年前的所有15分钟周期K线
"""

import pandas as pd
import mplfinance as mpf
import os
import datetime
import numpy as np
import time
import logging
from pathlib import Path
from pytdx.hq import TdxHq_API

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KLinePlotter:
    """K线绘制器"""

    def __init__(self):
        """
        初始化K线绘制器
        """
        # 初始化pytdx API
        try:
            self.api = TdxHq_API(
                multithread=True,      # 启用多线程支持
                heartbeat=True,        # 启用心跳包
                auto_retry=True,       # 启用自动重试
                raise_exception=False  # 不抛出异常，返回None
            )
        except Exception as e:
            logger.warning(f"使用高级参数初始化API失败: {e}，使用基本参数")
            self.api = TdxHq_API()

        self.is_connected = False
        self.last_connect_time = 0

        # 股票代码缓存
        self.stock_codes = {}

    def connect_tdx(self):
        """连接通达信服务器"""
        try:
            # 优先使用测试过的可用服务器
            servers = [
                ('**************', 7709),  # 上海电信主站Z1 (最快)
                ('**************', 80),    # 上海电信主站Z80 (最快)
                ('************', 7709),    # 上证云成都电信一
                ('**************', 7709),  # 上证云北京联通一
            ]

            # 添加更多备用服务器
            try:
                from pytdx.config.hosts import hq_hosts
                # 添加其他服务器作为备用
                backup_servers = [(host[1], host[2]) for host in hq_hosts[4:15]]
                servers.extend(backup_servers)
            except ImportError:
                # 如果导入失败，使用更多备用服务器
                backup_servers = [
                    ('*************', 7709),   # 长城国瑞电信1
                    ('*************', 7709),   # 长城国瑞电信2
                    ('*************', 7709),   # 长城国瑞网通
                    ('**************', 7709),  # 上海电信主站Z2
                    ('***************', 7709), # 北京联通主站Z1
                    ('***************', 7709), # 北京联通主站Z2
                ]
                servers.extend(backup_servers)

            logger.info(f"尝试连接 {len(servers)} 个通达信服务器...")

            for i, (host, port) in enumerate(servers):
                try:
                    logger.info(f"正在尝试连接服务器 {i+1}/{len(servers)}: {host}:{port}")

                    # 设置连接超时
                    if self.api.connect(host, port, time_out=5):
                        self.is_connected = True
                        logger.info(f"成功连接到通达信服务器: {host}:{port}")

                        # 测试连接是否正常工作
                        try:
                            test_data = self.api.get_security_count(0)  # 测试获取深圳市场股票数量
                            if test_data and test_data > 0:
                                logger.info(f"连接测试成功，深圳市场股票数量: {test_data}")
                                return True
                            else:
                                logger.warning(f"连接测试失败，断开连接: {host}:{port}")
                                self.api.disconnect()
                                self.is_connected = False
                        except Exception as test_e:
                            logger.warning(f"连接测试异常: {test_e}")
                            self.api.disconnect()
                            self.is_connected = False

                except Exception as e:
                    logger.warning(f"连接服务器 {host}:{port} 失败: {e}")
                    continue

            logger.error("无法连接到任何通达信服务器")
            return False
        except Exception as e:
            logger.error(f"连接通达信服务器异常: {e}")
            return False

    def ensure_connection(self):
        """确保连接可用，如果断开则重连"""
        current_time = time.time()

        # 如果未连接或距离上次连接超过5分钟，尝试重连
        if not self.is_connected or (current_time - self.last_connect_time) > 300:
            logger.info("检查连接状态...")

            # 测试当前连接
            if self.is_connected:
                try:
                    test_result = self.api.get_security_count(0)
                    if test_result and test_result > 0:
                        self.last_connect_time = current_time
                        return True
                    else:
                        logger.warning("连接测试失败，需要重连")
                        self.is_connected = False
                except Exception as e:
                    logger.warning(f"连接测试异常: {e}，需要重连")
                    self.is_connected = False

            # 重新连接
            if self.connect_tdx():
                self.last_connect_time = current_time
                return True
            else:
                return False

        return True

    def parse_stock_code(self, symbol: str):
        """
        解析股票代码，自动判断市场
        返回: {'code': 股票代码, 'market': 市场代码, 'name': 股票名称}
        """
        # 移除可能的前缀和后缀
        clean_symbol = symbol.upper()
        if ':' in clean_symbol:
            clean_symbol = clean_symbol.split(':')[-1]

        # 移除.SZ或.SH后缀
        if clean_symbol.endswith('.SZ') or clean_symbol.endswith('.SH'):
            clean_symbol = clean_symbol.split('.')[0]

        # 判断市场
        if clean_symbol.startswith('6'):
            # 上海市场：6开头
            market = 1
            exchange = 'SSE'
        elif clean_symbol.startswith(('0', '3')):
            # 深圳市场：0开头（主板、中小板）、3开头（创业板）
            market = 0
            exchange = 'SZSE'
        elif clean_symbol.startswith('8') or clean_symbol.startswith('4'):
            # 北交所：8开头（精选层）、4开头（创新层）
            market = 0  # 暂时归类到深圳市场
            exchange = 'BSE'
        else:
            # 默认深圳市场
            market = 0
            exchange = 'SZSE'

        # 尝试获取股票名称（可选）
        stock_name = f"股票 {clean_symbol}"

        result = {
            'code': clean_symbol,
            'market': market,
            'name': stock_name,
            'exchange': exchange
        }

        # 缓存结果
        self.stock_codes[symbol] = result  # 使用原始symbol作为key
        logger.info(f"解析股票代码: {symbol} -> {result}")

        return result

    def load_stock_data_from_pytdx(self, stock_code, months_before=6):
        """
        从pytdx获取股票数据

        Args:
            stock_code (str): 股票代码，如 "000021.SZ" 或 "000021"
            months_before (int): 向前取多少个月的数据，默认6个月

        Returns:
            tuple: (pd.DataFrame, str) 处理后的股票数据和周期名称
        """
        if not self.ensure_connection():
            raise ConnectionError("无法建立连接，获取股票数据失败")

        try:
            # 获取或解析股票信息
            if stock_code not in self.stock_codes:
                stock_info = self.parse_stock_code(stock_code)
                if not stock_info:
                    raise ValueError(f"无法解析股票代码: {stock_code}")
            else:
                stock_info = self.stock_codes[stock_code]

            market = stock_info['market']
            clean_symbol = stock_info['code']

            # 计算需要获取的数据量
            # 15分钟K线，一天约26根（9:30-11:30, 13:00-15:00），一个月约520根，半年约3120根
            # 为了确保获取足够的半年数据，我们获取3500根
            count = 3500

            logger.info(f"开始获取股票数据: {clean_symbol}, 市场: {market}, 数量: {count}")

            # 尝试获取不同周期的K线数据
            # period: 0 5分钟K线 1 15分钟K线 2 30分钟K线 3 1小时K线 4 日K线
            periods_to_try = [
                (1, "15分钟"),
                (0, "5分钟"),
                (2, "30分钟"),
                (3, "1小时"),
                (4, "日K线")
            ]

            data = None
            used_period_name = None

            for period, period_name in periods_to_try:
                logger.info(f"尝试获取{period_name}K线数据")

                # 如果需要大量数据，分批获取
                if count > 800:
                    logger.info(f"获取大量数据: {count} 条，将分批获取")
                    all_data = []
                    batch_size = 800  # 每批最多800条
                    current_pos = 0
                    max_attempts = 10  # 最多尝试10批
                    attempt = 0

                    while len(all_data) < count and attempt < max_attempts:
                        current_batch_size = min(batch_size, count - len(all_data))
                        logger.info(f"第{attempt + 1}批: 位置={current_pos}, 请求={current_batch_size}条")

                        batch_data = self.api.get_security_bars(period, market, clean_symbol, current_pos, current_batch_size)

                        if not batch_data:
                            logger.warning(f"第{attempt + 1}批数据获取失败，可能已到达历史数据边界")
                            break

                        # 检查是否有重复数据（时间戳相同）
                        new_data = []
                        existing_times = set()

                        # 先收集已有数据的时间戳
                        for existing_bar in all_data:
                            try:
                                existing_time = existing_bar.get('datetime')
                                if existing_time:
                                    existing_times.add(str(existing_time))
                            except Exception as e:
                                logger.warning(f"处理已有数据时间戳失败: {e}")

                        # 处理新批次数据
                        for bar in batch_data:
                            try:
                                bar_time = bar.get('datetime')
                                if bar_time and str(bar_time) not in existing_times:
                                    new_data.append(bar)
                                    existing_times.add(str(bar_time))
                            except Exception as e:
                                logger.warning(f"处理批次数据时间戳失败: {e}")

                        if new_data:
                            all_data.extend(new_data)
                            logger.info(f"第{attempt + 1}批: 获取{len(batch_data)}条，去重后{len(new_data)}条，累计{len(all_data)}条")
                        else:
                            logger.info(f"第{attempt + 1}批: 无新数据，可能已到达历史数据边界")
                            break

                        current_pos += batch_size
                        attempt += 1

                        # 如果返回的数据少于请求的数量，说明没有更多数据了
                        if len(batch_data) < current_batch_size:
                            logger.info("已获取所有可用历史数据")
                            break

                    data = all_data
                    logger.info(f"分批获取完成，总计 {len(data)} 条数据，尝试了 {attempt + 1} 批")
                else:
                    # 普通获取
                    logger.info(f"普通获取: {count} 条数据")
                    data = self.api.get_security_bars(period, market, clean_symbol, 0, count)

                if data and len(data) > 0:
                    logger.info(f"成功获取{period_name}K线数据，数量: {len(data)}")
                    if len(data) > 0:
                        logger.info(f"第一条数据样例: {data[0]}")
                    used_period_name = period_name
                    break
                else:
                    logger.info(f"{period_name}K线数据获取失败，尝试下一个周期")

            if not data:
                raise ValueError(f"未获取到任何K线数据: {stock_code}，请检查股票代码是否正确")

            logger.info(f"成功获取 {len(data)} 条{used_period_name}K线数据")

            # 转换为DataFrame格式
            bars = []
            for i, bar in enumerate(data):
                try:
                    # 检查数据结构
                    if not isinstance(bar, dict):
                        logger.warning(f"第{i+1}条数据不是字典格式: {type(bar)} - {bar}")
                        continue

                    # 检查必需字段
                    required_fields = ['datetime', 'open', 'high', 'low', 'close', 'vol']
                    missing_fields = [field for field in required_fields if field not in bar]
                    if missing_fields:
                        logger.warning(f"第{i+1}条数据缺少字段 {missing_fields}: {bar}")
                        continue

                    # 处理时间戳
                    datetime_value = bar['datetime']
                    if isinstance(datetime_value, str):
                        # pytdx返回格式: '2025-06-18 14:58' 或 '2025-06-18 15:00'
                        dt = datetime.datetime.strptime(datetime_value, '%Y-%m-%d %H:%M')
                    elif hasattr(datetime_value, 'timestamp'):
                        # 如果是datetime对象
                        dt = datetime_value
                    else:
                        logger.warning(f"第{i+1}条数据时间格式未知: {type(datetime_value)}, 值: {datetime_value}")
                        continue

                    # 处理价格和成交量数据
                    bar_data = {
                        'datetime': dt,
                        'Open': float(bar['open']),
                        'High': float(bar['high']),
                        'Low': float(bar['low']),
                        'Close': float(bar['close']),
                        'Volume': int(bar['vol'])
                    }
                    bars.append(bar_data)

                except Exception as data_e:
                    logger.warning(f"第{i+1}条数据处理失败: {data_e}, 原始数据: {bar}")
                    continue

            if not bars:
                raise ValueError("没有有效的K线数据")

            # 创建DataFrame
            df = pd.DataFrame(bars)
            df.set_index('datetime', inplace=True)

            # 按时间排序（从早到晚）
            df.sort_index(inplace=True)

            # 删除包含NaN的行
            df.dropna(inplace=True)

            logger.info(f"数据处理完成，有效数据条数: {len(df)}")
            logger.info(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")

            return df, used_period_name

        except Exception as e:
            logger.error(f"获取股票数据异常: {e}")
            raise
    
    def filter_data_by_current_date(self, df, months_before=6):
        """
        从当前时间点向前筛选指定月份的数据

        Args:
            df (pd.DataFrame): 股票数据
            months_before (int): 向前取多少个月的数据

        Returns:
            pd.DataFrame: 筛选后的数据
        """
        # 获取当前时间
        current_time = datetime.datetime.now()
        start_date = current_time - pd.DateOffset(months=months_before)

        # 筛选数据：从半年前到现在
        filtered_df = df[(df.index >= start_date) & (df.index <= current_time)]

        logger.info(f"数据筛选范围: {start_date.strftime('%Y-%m-%d %H:%M')} 到 {current_time.strftime('%Y-%m-%d %H:%M')}")
        logger.info(f"筛选后数据条数: {len(filtered_df)}")

        return filtered_df

    def find_swing_points(self, df, swing_length=10):
        """
        检测swing high和swing low点

        Args:
            df (pd.DataFrame): 股票数据，包含High和Low列
            swing_length (int): 左右确认周期长度，默认10

        Returns:
            tuple: (swing_highs, swing_lows) 两个包含swing点信息的列表
        """
        swing_highs = []
        swing_lows = []

        # 确保数据足够长
        if len(df) < swing_length * 2 + 1:
            return swing_highs, swing_lows

        # 遍历可能的swing点（排除两端不足swing_length的部分）
        for i in range(swing_length, len(df) - swing_length):
            current_high = df.iloc[i]['High']
            current_low = df.iloc[i]['Low']
            current_time = df.index[i]

            # 检查swing high: 当前high必须严格高于左右各swing_length个K线的high
            is_swing_high = True
            for j in range(i - swing_length, i + swing_length + 1):
                if j != i and df.iloc[j]['High'] >= current_high:  # 注意是>=，确保严格高于
                    is_swing_high = False
                    break

            if is_swing_high:
                swing_highs.append({
                    'index': i,
                    'time': current_time,
                    'price': current_high
                })

            # 检查swing low: 当前low必须严格低于左右各swing_length个K线的low
            is_swing_low = True
            for j in range(i - swing_length, i + swing_length + 1):
                if j != i and df.iloc[j]['Low'] <= current_low:  # 注意是<=，确保严格低于
                    is_swing_low = False
                    break

            if is_swing_low:
                swing_lows.append({
                    'index': i,
                    'time': current_time,
                    'price': current_low
                })

        return swing_highs, swing_lows

    def find_breakthrough_point(self, df, swing_point, is_resistance=True):
        """
        查找水平线被突破的位置

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息，包含index和price
            is_resistance (bool): True表示阻力线，False表示支撑线

        Returns:
            int: 突破位置的索引，如果没有突破则返回None
        """
        pivot_index = swing_point['index']
        pivot_price = swing_point['price']

        # 从枢轴点之后开始检查突破
        for i in range(pivot_index + 1, len(df)):
            if is_resistance:
                # 阻力线被上穿：K线最高价超过阻力线价格
                if df.iloc[i]['High'] > pivot_price:
                    return i
            else:
                # 支撑线被下穿：K线最低价低于支撑线价格
                if df.iloc[i]['Low'] < pivot_price:
                    return i

        # 没有找到突破点
        return None

    def detect_fvg(self, df, start_index=0, end_index=None):
        """
        检测FVG (Fair Value Gap) 公允价值缺口

        Args:
            df (pd.DataFrame): 股票数据
            start_index (int): 开始检测的索引
            end_index (int): 结束检测的索引，如果为None则检测到末尾

        Returns:
            list: FVG列表，每个FVG包含类型、价格范围、时间等信息
        """
        if end_index is None:
            end_index = len(df)

        fvgs = []

        # 需要至少3根K线才能检测FVG
        for i in range(start_index, min(end_index - 2, len(df) - 2)):
            k1 = df.iloc[i]
            k2 = df.iloc[i + 1]
            k3 = df.iloc[i + 2]

            # 看涨FVG检测：第一根K线最高价 < 第三根K线最低价
            if k1['High'] < k3['Low']:
                fvg_bottom = k1['High']
                fvg_top = k3['Low']
                fvgs.append({
                    'type': 'bullish',
                    'start_index': i,
                    'end_index': i + 2,
                    'start_time': df.index[i],
                    'end_time': df.index[i + 2],
                    'bottom': fvg_bottom,
                    'top': fvg_top
                })

            # 看跌FVG检测：第一根K线最低价 > 第三根K线最高价
            elif k1['Low'] > k3['High']:
                fvg_bottom = k3['High']
                fvg_top = k1['Low']
                fvgs.append({
                    'type': 'bearish',
                    'start_index': i,
                    'end_index': i + 2,
                    'start_time': df.index[i],
                    'end_time': df.index[i + 2],
                    'bottom': fvg_bottom,
                    'top': fvg_top
                })

        return fvgs

    def check_fvg_breakthrough(self, df, fvg):
        """
        检查FVG是否被价格完全穿过

        新定义：
        1. 看跌FVG之后的某根K线的最高价只要全部穿过该FVG区域，该FVG区域就应该被删除
        2. 看涨FVG之后的某根K线的最低价只要全部穿过该FVG区域，该FVG区域就应该被删除

        Args:
            df (pd.DataFrame): 股票数据
            fvg (dict): FVG信息，包含类型、价格范围等

        Returns:
            bool: True表示FVG被完全穿过，False表示未被完全穿过
        """
        fvg_end_index = fvg['end_index']
        fvg_type = fvg['type']
        fvg_top = fvg['top']
        fvg_bottom = fvg['bottom']

        # 从FVG结束后开始检查突破
        for i in range(fvg_end_index + 1, len(df)):
            current_candle = df.iloc[i]

            if fvg_type == 'bullish':
                # 看涨FVG被完全下穿：K线最低价全部穿过FVG区域（低于FVG底部）
                # 意思是价格从FVG顶部以上完全穿过到FVG底部以下
                if current_candle['Low'] < fvg_bottom:
                    return True
            elif fvg_type == 'bearish':
                # 看跌FVG被完全上穿：K线最高价全部穿过FVG区域（高于FVG顶部）
                # 意思是价格从FVG底部以下完全穿过到FVG顶部以上
                if current_candle['High'] > fvg_top:
                    return True

        # 没有被完全穿过
        return False

    def find_fvg_in_range(self, df, swing_point, fvg_type, range_limit=10):
        """
        在swing点后的指定范围内寻找特定类型的FVG，并过滤掉被突破的FVG

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'
            range_limit (int): 搜索范围限制（K线数量）

        Returns:
            list: 找到的未被突破的FVG列表
        """
        pivot_index = swing_point['index']
        start_index = pivot_index + 1
        end_index = min(start_index + range_limit, len(df))

        # 在指定范围内检测FVG
        all_fvgs = self.detect_fvg(df, start_index, end_index)

        # 筛选指定类型的FVG
        filtered_fvgs = [fvg for fvg in all_fvgs if fvg['type'] == fvg_type]

        # 进一步过滤掉被突破的FVG
        valid_fvgs = []
        for fvg in filtered_fvgs:
            if not self.check_fvg_breakthrough(df, fvg):
                valid_fvgs.append(fvg)

        return valid_fvgs

    def find_extreme_fvg_in_range(self, df, swing_point, fvg_type, range_limit=10):
        """
        在swing点后的指定范围内寻找价格最极端的FVG区域

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'
            range_limit (int): 搜索范围限制（K线数量）

        Returns:
            dict: 最极端的FVG区域，如果没有找到则返回None
        """
        # 获取所有有效的FVG
        valid_fvgs = self.find_fvg_in_range(df, swing_point, fvg_type, range_limit)

        if not valid_fvgs:
            return None

        # 根据FVG类型选择最极端的FVG
        if fvg_type == 'bearish':
            # 对于看跌FVG，选择价格最高的（top价格最高的）
            extreme_fvg = max(valid_fvgs, key=lambda fvg: fvg['top'])
        elif fvg_type == 'bullish':
            # 对于看涨FVG，选择价格最低的（bottom价格最低的）
            extreme_fvg = min(valid_fvgs, key=lambda fvg: fvg['bottom'])
        else:
            return None

        return extreme_fvg

    def create_rectangle_zone(self, swing_point, extreme_fvg, is_resistance=True):
        """
        创建矩形支撑/阻力区域

        Args:
            swing_point (dict): swing点信息
            extreme_fvg (dict): 最极端的FVG区域
            is_resistance (bool): True表示阻力区域，False表示支撑区域

        Returns:
            dict: 矩形区域信息，包含top、bottom、start_index等
        """
        if extreme_fvg is None:
            return None

        swing_price = swing_point['price']
        swing_index = swing_point['index']

        if is_resistance:
            # 阻力区域：swing high价格和看跌FVG的最高价格
            zone_top = max(swing_price, extreme_fvg['top'])
            zone_bottom = min(swing_price, extreme_fvg['bottom'])
        else:
            # 支撑区域：swing low价格和看涨FVG的最低价格
            zone_top = max(swing_price, extreme_fvg['top'])
            zone_bottom = min(swing_price, extreme_fvg['bottom'])

        return {
            'top': zone_top,
            'bottom': zone_bottom,
            'start_index': swing_index,
            'swing_point': swing_point,
            'extreme_fvg': extreme_fvg,
            'is_resistance': is_resistance
        }

    def check_swing_had_fvg(self, df, swing_point, fvg_type, range_limit=10):
        """
        检查swing点是否曾经有过FVG（包括已被突破的）

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'
            range_limit (int): 搜索范围限制（K线数量）

        Returns:
            bool: True表示曾经有过FVG，False表示从未有过FVG
        """
        pivot_index = swing_point['index']
        start_index = pivot_index + 1
        end_index = min(start_index + range_limit, len(df))

        # 在指定范围内检测所有FVG（不过滤突破）
        all_fvgs = self.detect_fvg(df, start_index, end_index)

        # 筛选指定类型的FVG
        filtered_fvgs = [fvg for fvg in all_fvgs if fvg['type'] == fvg_type]

        # 只要找到过FVG就返回True，不管是否被突破
        return len(filtered_fvgs) > 0

    def plot_kline(self, df, stock_code, period_name="K线", save_path=None, show_swing_points=True, show_horizontal_lines=True, show_fvg=True, swing_length=10):
        """
        绘制K线图

        Args:
            df (pd.DataFrame): 股票数据
            stock_code (str): 股票代码
            period_name (str): 数据周期名称，如"15分钟"、"5分钟"等
            save_path (str): 保存路径，如果为None则显示图表
            show_swing_points (bool): 是否显示swing点标记，默认True
            show_horizontal_lines (bool): 是否显示水平支撑阻力线，默认True
            show_fvg (bool): 是否显示FVG区域，默认True
            swing_length (int): swing检测的左右确认周期，默认10
        """
        if df.empty:
            print("数据为空，无法绘制K线图")
            return

        # 设置中文字体和禁用警告
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        import warnings

        # 禁用字体相关警告
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
        warnings.filterwarnings('ignore', category=UserWarning, module='mplfinance')

        # 尝试设置中文字体
        chinese_font_found = False
        try:
            # 优先使用系统中文字体
            chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong']
            available_fonts = [f.name for f in fm.fontManager.ttflist]

            for font in chinese_fonts:
                if font in available_fonts:
                    plt.rcParams['font.sans-serif'] = [font, 'DejaVu Sans']
                    chinese_font_found = True
                    break

            if not chinese_font_found:
                # 使用英文标题避免字体警告
                print("提示: 使用英文标题以避免字体显示问题")

        except Exception as e:
            print(f"字体设置提示: 将使用默认字体")

        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置K线图样式
        mc = mpf.make_marketcolors(
            up='red',      # 上涨为红色
            down='green',  # 下跌为绿色
            edge='inherit',
            wick={'up': 'red', 'down': 'green'},
            volume='in'
        )
        
        style = mpf.make_mpf_style(
            marketcolors=mc,
            gridstyle='-',
            y_on_right=True
        )
        
        # 创建图表标题 - 根据字体支持情况选择中英文
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
        if chinese_font_found:
            title = f"{stock_code} K线图 ({period_name}) - 截至{current_time}前半年数据"
            ylabel = '价格'
            ylabel_lower = '成交量'
        else:
            title = f"{stock_code} Candlestick Chart ({period_name}) - 6 months before {current_time}"
            ylabel = 'Price'
            ylabel_lower = 'Volume'

        # 准备附加绘图数据
        addplot_data = []

        if show_swing_points or show_horizontal_lines:
            # 检测swing点
            swing_highs, swing_lows = self.find_swing_points(df, swing_length)

            # 如果启用FVG过滤，则只保留在深色区域内有FVG的swing点
            # 但是如果swing点是全图的最大值或最小值，则无论是否有FVG都要保留
            if show_fvg:
                # 找到全图的最高价和最低价
                global_high = df['High'].max()
                global_low = df['Low'].min()

                # 过滤swing high点：保留曾经有过下跌FVG的点，或者是全图最高点
                filtered_swing_highs = []
                for swing in swing_highs:
                    # 检查是否曾经有过下跌FVG（包括已被突破的）
                    had_bearish_fvg = self.check_swing_had_fvg(df, swing, 'bearish', 10)
                    # 如果曾经有过下跌FVG，或者是全图最高点，保留这个swing high点
                    if had_bearish_fvg or swing['price'] == global_high:
                        filtered_swing_highs.append(swing)
                swing_highs = filtered_swing_highs

                # 过滤swing low点：保留曾经有过上涨FVG的点，或者是全图最低点
                filtered_swing_lows = []
                for swing in swing_lows:
                    # 检查是否曾经有过上涨FVG（包括已被突破的）
                    had_bullish_fvg = self.check_swing_had_fvg(df, swing, 'bullish', 10)
                    # 如果曾经有过上涨FVG，或者是全图最低点，保留这个swing low点
                    if had_bullish_fvg or swing['price'] == global_low:
                        filtered_swing_lows.append(swing)
                swing_lows = filtered_swing_lows

            if swing_highs or swing_lows:
                # 创建用于标记swing点的Series
                swing_high_markers = pd.Series(index=df.index, dtype=float)
                swing_low_markers = pd.Series(index=df.index, dtype=float)

                if show_swing_points:
                    # 只标记未被突破的swing点
                    # 标记swing high点（在K线上方）- 只显示未被突破的
                    for swing in swing_highs:
                        # 检查阻力线是否被突破
                        breakthrough_index = self.find_breakthrough_point(df, swing, is_resistance=True)
                        # 只有未被突破的swing high点才显示标记
                        if breakthrough_index is None:
                            # 在high价格上方一点位置标记
                            marker_price = swing['price'] * 1.002  # 上方0.2%位置
                            swing_high_markers.loc[swing['time']] = marker_price

                    # 标记swing low点（在K线下方）- 只显示未被突破的
                    for swing in swing_lows:
                        # 检查支撑线是否被突破
                        breakthrough_index = self.find_breakthrough_point(df, swing, is_resistance=False)
                        # 只有未被突破的swing low点才显示标记
                        if breakthrough_index is None:
                            # 在low价格下方一点位置标记
                            marker_price = swing['price'] * 0.998  # 下方0.2%位置
                            swing_low_markers.loc[swing['time']] = marker_price

                    # 添加swing high标记（红色圆圈，阻力位）- 只显示有效的
                    if not swing_high_markers.isna().all():
                        addplot_data.append(mpf.make_addplot(
                            swing_high_markers,
                            type='scatter',
                            markersize=80,
                            marker='o',
                            color='red',
                            alpha=0.7,
                            secondary_y=False
                        ))

                    # 添加swing low标记（绿色圆圈，支撑位）- 只显示有效的
                    if not swing_low_markers.isna().all():
                        addplot_data.append(mpf.make_addplot(
                            swing_low_markers,
                            type='scatter',
                            markersize=80,
                            marker='o',
                            color='green',
                            alpha=0.7,
                            secondary_y=False
                        ))

                if show_horizontal_lines:
                    # 绘制水平支撑阻力线（只显示未被突破的线）
                    # 如果水平线被突破，则整条线都不显示
                    # 水平线分为两部分：前10根K线用深色，10根K线之后用浅色

                    # 处理swing high的水平阻力线
                    for swing in swing_highs:
                        pivot_index = swing['index']

                        # 检查阻力线是否被突破
                        breakthrough_index = self.find_breakthrough_point(df, swing, is_resistance=True)

                        # 只有未被突破的阻力线才显示
                        if breakthrough_index is None:
                            # 计算线段的结束位置
                            end_index = len(df) - 1

                            # 第一部分：前10根K线（深色）
                            first_part_end = min(pivot_index + 10, end_index)
                            if first_part_end > pivot_index:
                                resistance_line_dark = pd.Series(index=df.index, dtype=float)
                                for j in range(pivot_index, first_part_end + 1):
                                    resistance_line_dark.iloc[j] = swing['price']

                                # 添加深色阻力线（深红色虚线）
                                addplot_data.append(mpf.make_addplot(
                                    resistance_line_dark,
                                    type='line',
                                    color='darkred',
                                    linestyle='--',
                                    width=1.5,
                                    alpha=0.8,
                                    secondary_y=False
                                ))

                                # 在深色区域内绘制下跌FVG（swing high阻力位）
                                if show_fvg:
                                    bearish_fvgs = self.find_fvg_in_range(df, swing, 'bearish', 10)

                                    # 找到价格最高的FVG区域，创建矩形阻力位区域
                                    extreme_fvg = self.find_extreme_fvg_in_range(df, swing, 'bearish', 10)
                                    if extreme_fvg:
                                        rectangle_zone = self.create_rectangle_zone(swing, extreme_fvg, is_resistance=True)
                                        if rectangle_zone:
                                            # 绘制矩形阻力位区域（从swing点开始到图表结束）
                                            rect_top_line = pd.Series(index=df.index, dtype=float)
                                            rect_bottom_line = pd.Series(index=df.index, dtype=float)
                                            rect_fill_line = pd.Series(index=df.index, dtype=float)

                                            # 矩形区域从swing点开始延伸到图表末尾
                                            for j in range(pivot_index, len(df)):
                                                rect_top_line.iloc[j] = rectangle_zone['top']
                                                rect_bottom_line.iloc[j] = rectangle_zone['bottom']
                                                # 填充区域使用中间价格
                                                rect_fill_line.iloc[j] = (rectangle_zone['top'] + rectangle_zone['bottom']) / 2

                                            # 创建多条填充线来模拟填充效果（浅红色）
                                            fill_steps = 20  # 填充线条数
                                            step_size = (rectangle_zone['top'] - rectangle_zone['bottom']) / fill_steps
                                            for step in range(fill_steps):
                                                fill_price = rectangle_zone['bottom'] + step * step_size
                                                rect_fill_step = pd.Series(index=df.index, dtype=float)
                                                for j in range(pivot_index, len(df)):
                                                    rect_fill_step.iloc[j] = fill_price

                                                addplot_data.append(mpf.make_addplot(
                                                    rect_fill_step,
                                                    type='line',
                                                    color='lightcoral',
                                                    linestyle='-',
                                                    width=0.5,
                                                    alpha=0.15,
                                                    secondary_y=False
                                                ))

                                            # 添加矩形区域上边界（深红色粗实线）
                                            addplot_data.append(mpf.make_addplot(
                                                rect_top_line,
                                                type='line',
                                                color='darkred',
                                                linestyle='-',
                                                width=3,
                                                alpha=0.8,
                                                secondary_y=False
                                            ))

                                            # 添加矩形区域下边界（深红色粗实线）
                                            addplot_data.append(mpf.make_addplot(
                                                rect_bottom_line,
                                                type='line',
                                                color='darkred',
                                                linestyle='-',
                                                width=3,
                                                alpha=0.8,
                                                secondary_y=False
                                            ))

                                    # 继续绘制原有的FVG区域（在深色区域内）
                                    for fvg in bearish_fvgs:
                                        # 创建FVG区域的上下边界线
                                        fvg_top_line = pd.Series(index=df.index, dtype=float)
                                        fvg_bottom_line = pd.Series(index=df.index, dtype=float)

                                        # 只在深色区域范围内绘制FVG
                                        fvg_start = max(fvg['start_index'], pivot_index)
                                        fvg_end = min(fvg['end_index'], first_part_end)

                                        if fvg_start <= fvg_end:
                                            for j in range(fvg_start, fvg_end + 1):
                                                fvg_top_line.iloc[j] = fvg['top']
                                                fvg_bottom_line.iloc[j] = fvg['bottom']

                                            # 添加FVG上边界（深红色实线）
                                            addplot_data.append(mpf.make_addplot(
                                                fvg_top_line,
                                                type='line',
                                                color='darkred',
                                                linestyle='-',
                                                width=2,
                                                alpha=0.9,
                                                secondary_y=False
                                            ))

                                            # 添加FVG下边界（深红色实线）
                                            addplot_data.append(mpf.make_addplot(
                                                fvg_bottom_line,
                                                type='line',
                                                color='darkred',
                                                linestyle='-',
                                                width=2,
                                                alpha=0.9,
                                                secondary_y=False
                                            ))

                            # 第二部分：10根K线之后（浅色）
                            if first_part_end < end_index:
                                resistance_line_light = pd.Series(index=df.index, dtype=float)
                                for j in range(first_part_end + 1, end_index + 1):
                                    resistance_line_light.iloc[j] = swing['price']

                                # 添加浅色阻力线（浅红色虚线）
                                addplot_data.append(mpf.make_addplot(
                                    resistance_line_light,
                                    type='line',
                                    color='lightcoral',
                                    linestyle='--',
                                    width=1,
                                    alpha=0.5,
                                    secondary_y=False
                                ))

                    # 处理swing low的水平支撑线
                    for swing in swing_lows:
                        pivot_index = swing['index']

                        # 检查支撑线是否被突破
                        breakthrough_index = self.find_breakthrough_point(df, swing, is_resistance=False)

                        # 只有未被突破的支撑线才显示
                        if breakthrough_index is None:
                            # 计算线段的结束位置
                            end_index = len(df) - 1

                            # 第一部分：前10根K线（深色）
                            first_part_end = min(pivot_index + 10, end_index)
                            if first_part_end > pivot_index:
                                support_line_dark = pd.Series(index=df.index, dtype=float)
                                for j in range(pivot_index, first_part_end + 1):
                                    support_line_dark.iloc[j] = swing['price']

                                # 添加深色支撑线（深绿色虚线）
                                addplot_data.append(mpf.make_addplot(
                                    support_line_dark,
                                    type='line',
                                    color='darkgreen',
                                    linestyle='--',
                                    width=1.5,
                                    alpha=0.8,
                                    secondary_y=False
                                ))

                                # 在深色区域内绘制上涨FVG（swing low支撑位）
                                if show_fvg:
                                    bullish_fvgs = self.find_fvg_in_range(df, swing, 'bullish', 10)

                                    # 找到价格最低的FVG区域，创建矩形支撑位区域
                                    extreme_fvg = self.find_extreme_fvg_in_range(df, swing, 'bullish', 10)
                                    if extreme_fvg:
                                        rectangle_zone = self.create_rectangle_zone(swing, extreme_fvg, is_resistance=False)
                                        if rectangle_zone:
                                            # 绘制矩形支撑位区域（从swing点开始到图表结束）
                                            rect_top_line = pd.Series(index=df.index, dtype=float)
                                            rect_bottom_line = pd.Series(index=df.index, dtype=float)

                                            # 矩形区域从swing点开始延伸到图表末尾
                                            for j in range(pivot_index, len(df)):
                                                rect_top_line.iloc[j] = rectangle_zone['top']
                                                rect_bottom_line.iloc[j] = rectangle_zone['bottom']

                                            # 创建多条填充线来模拟填充效果（浅绿色）
                                            fill_steps = 20  # 填充线条数
                                            step_size = (rectangle_zone['top'] - rectangle_zone['bottom']) / fill_steps
                                            for step in range(fill_steps):
                                                fill_price = rectangle_zone['bottom'] + step * step_size
                                                rect_fill_step = pd.Series(index=df.index, dtype=float)
                                                for j in range(pivot_index, len(df)):
                                                    rect_fill_step.iloc[j] = fill_price

                                                addplot_data.append(mpf.make_addplot(
                                                    rect_fill_step,
                                                    type='line',
                                                    color='lightgreen',
                                                    linestyle='-',
                                                    width=0.5,
                                                    alpha=0.15,
                                                    secondary_y=False
                                                ))

                                            # 添加矩形区域上边界（深绿色粗实线）
                                            addplot_data.append(mpf.make_addplot(
                                                rect_top_line,
                                                type='line',
                                                color='darkgreen',
                                                linestyle='-',
                                                width=3,
                                                alpha=0.8,
                                                secondary_y=False
                                            ))

                                            # 添加矩形区域下边界（深绿色粗实线）
                                            addplot_data.append(mpf.make_addplot(
                                                rect_bottom_line,
                                                type='line',
                                                color='darkgreen',
                                                linestyle='-',
                                                width=3,
                                                alpha=0.8,
                                                secondary_y=False
                                            ))

                                    # 继续绘制原有的FVG区域（在深色区域内）
                                    for fvg in bullish_fvgs:
                                        # 创建FVG区域的上下边界线
                                        fvg_top_line = pd.Series(index=df.index, dtype=float)
                                        fvg_bottom_line = pd.Series(index=df.index, dtype=float)

                                        # 只在深色区域范围内绘制FVG
                                        fvg_start = max(fvg['start_index'], pivot_index)
                                        fvg_end = min(fvg['end_index'], first_part_end)

                                        if fvg_start <= fvg_end:
                                            for j in range(fvg_start, fvg_end + 1):
                                                fvg_top_line.iloc[j] = fvg['top']
                                                fvg_bottom_line.iloc[j] = fvg['bottom']

                                            # 添加FVG上边界（深绿色实线）
                                            addplot_data.append(mpf.make_addplot(
                                                fvg_top_line,
                                                type='line',
                                                color='darkgreen',
                                                linestyle='-',
                                                width=2,
                                                alpha=0.9,
                                                secondary_y=False
                                            ))

                                            # 添加FVG下边界（深绿色实线）
                                            addplot_data.append(mpf.make_addplot(
                                                fvg_bottom_line,
                                                type='line',
                                                color='darkgreen',
                                                linestyle='-',
                                                width=2,
                                                alpha=0.9,
                                                secondary_y=False
                                            ))

                            # 第二部分：10根K线之后（浅色）
                            if first_part_end < end_index:
                                support_line_light = pd.Series(index=df.index, dtype=float)
                                for j in range(first_part_end + 1, end_index + 1):
                                    support_line_light.iloc[j] = swing['price']

                                # 添加浅色支撑线（浅绿色虚线）
                                addplot_data.append(mpf.make_addplot(
                                    support_line_light,
                                    type='line',
                                    color='lightgreen',
                                    linestyle='--',
                                    width=1,
                                    alpha=0.5,
                                    secondary_y=False
                                ))
        
        # 绘制K线图
        plot_kwargs = {
            'type': 'candle',
            'style': style,
            'title': title,
            'ylabel': ylabel,
            'ylabel_lower': ylabel_lower,
            'volume': True,
            'figsize': (16, 10),
            'show_nontrading': False,
            'warn_too_much_data': len(df) + 100  # 避免数据量警告
        }

        # 添加swing点标记
        if addplot_data:
            plot_kwargs['addplot'] = addplot_data

        # 只有当save_path不为None时才添加savefig参数
        if save_path:
            plot_kwargs['savefig'] = save_path

        mpf.plot(df, **plot_kwargs)
        
        if not save_path:
            import matplotlib.pyplot as plt
            plt.show()
    
    def plot_stock_kline(self, stock_code, save_path=None, show_swing_points=True, show_horizontal_lines=True, show_fvg=True, swing_length=10, months_before=6):
        """
        绘制指定股票的K线图

        Args:
            stock_code (str): 股票代码，如 "000021.SZ" 或 "000021"
            save_path (str): 保存路径，如果为None则显示图表
            show_swing_points (bool): 是否显示swing点标记，默认True
            show_horizontal_lines (bool): 是否显示水平支撑阻力线，默认True
            show_fvg (bool): 是否显示FVG区域，默认True
            swing_length (int): swing检测的左右确认周期，默认10
            months_before (int): 向前取多少个月的数据，默认6个月
        """
        try:
            logger.info(f"开始绘制股票K线图: {stock_code}")

            # 从pytdx加载数据
            df, period_name = self.load_stock_data_from_pytdx(stock_code, months_before)
            logger.info(f"原始数据条数: {len(df)}")
            logger.info(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")

            # 筛选前半年数据（从当前时间向前）
            filtered_df = self.filter_data_by_current_date(df, months_before)

            if filtered_df.empty:
                logger.error("筛选后数据为空，请检查股票代码是否正确")
                return

            # 绘制K线图
            self.plot_kline(filtered_df, stock_code, period_name, save_path, show_swing_points, show_horizontal_lines, show_fvg, swing_length)

            logger.info("K线图绘制完成！")

        except Exception as e:
            logger.error(f"绘制K线图时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """主函数 - 示例用法"""
    # 创建K线绘制器
    plotter = KLinePlotter()

    # 示例：绘制600000.SH的K线图（从当前时间向前半年的K线数据）
    stock_code = "600895.SH"  # 浦发银行，可以修改为其他股票代码，如 "000001.SZ"

    # 绘制K线图（显示）
    plotter.plot_stock_kline(stock_code)

    # 如果要保存图片，可以指定保存路径
    # save_path = "kline_chart.png"
    # plotter.plot_stock_kline(stock_code, save_path=save_path)


if __name__ == "__main__":
    main()
