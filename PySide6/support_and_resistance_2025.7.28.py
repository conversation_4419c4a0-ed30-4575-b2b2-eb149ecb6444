#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支撑阻力位分析API
从K线数据中通过层层过滤得到最终的支撑阻力位和区域
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional, Union

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SupportResistanceAnalyzer:
    """支撑阻力位分析器"""
    
    def __init__(self, swing_length: int = 10, fvg_range_limit: int = 20):
        """
        初始化分析器
        
        Args:
            swing_length (int): swing点检测的左右确认周期，默认10
            fvg_range_limit (int): FVG搜索范围限制，默认10
        """
        self.swing_length = swing_length
        self.fvg_range_limit = fvg_range_limit
    
    def find_swing_points(self, df: pd.DataFrame) -> <PERSON><PERSON>[List[Dict], List[Dict]]:
        """
        检测swing high和swing low点
        
        Args:
            df (pd.DataFrame): 股票数据，包含High和Low列，索引为时间
            
        Returns:
            tuple: (swing_highs, swing_lows) 两个包含swing点信息的列表
        """
        swing_highs = []
        swing_lows = []
        
        # 确保数据足够长
        if len(df) < self.swing_length * 2 + 1:
            logger.warning(f"数据长度不足，需要至少{self.swing_length * 2 + 1}条数据")
            return swing_highs, swing_lows
        
        # 遍历可能的swing点（排除两端不足swing_length的部分）
        for i in range(self.swing_length, len(df) - self.swing_length):
            current_high = df.iloc[i]['High']
            current_low = df.iloc[i]['Low']
            current_time = df.index[i]
            
            # 检查swing high: 当前high必须严格高于左右各swing_length个K线的high
            is_swing_high = True
            for j in range(i - self.swing_length, i + self.swing_length + 1):
                if j != i and df.iloc[j]['High'] >= current_high:
                    is_swing_high = False
                    break
            
            if is_swing_high:
                swing_highs.append({
                    'index': i,
                    'time': current_time,
                    'price': current_high,
                    'type': 'resistance'
                })
            
            # 检查swing low: 当前low必须严格低于左右各swing_length个K线的low
            is_swing_low = True
            for j in range(i - self.swing_length, i + self.swing_length + 1):
                if j != i and df.iloc[j]['Low'] <= current_low:
                    is_swing_low = False
                    break
            
            if is_swing_low:
                swing_lows.append({
                    'index': i,
                    'time': current_time,
                    'price': current_low,
                    'type': 'support'
                })
        
        logger.info(f"检测到 {len(swing_highs)} 个swing high点，{len(swing_lows)} 个swing low点")
        return swing_highs, swing_lows
    
    def detect_fvg(self, df: pd.DataFrame, start_index: int = 0, end_index: Optional[int] = None) -> List[Dict]:
        """
        检测FVG (Fair Value Gap) 公允价值缺口
        
        Args:
            df (pd.DataFrame): 股票数据
            start_index (int): 开始检测的索引
            end_index (int): 结束检测的索引，如果为None则检测到末尾
            
        Returns:
            list: FVG列表，每个FVG包含类型、价格范围、时间等信息
        """
        if end_index is None:
            end_index = len(df)
        
        fvgs = []
        
        # 需要至少3根K线才能检测FVG
        for i in range(start_index, min(end_index - 2, len(df) - 2)):
            k1 = df.iloc[i]
            k2 = df.iloc[i + 1]
            k3 = df.iloc[i + 2]
            
            # 看涨FVG检测：第一根K线最高价 < 第三根K线最低价
            if k1['High'] < k3['Low']:
                fvg_bottom = k1['High']
                fvg_top = k3['Low']
                fvgs.append({
                    'type': 'bullish',
                    'start_index': i,
                    'end_index': i + 2,
                    'start_time': df.index[i],
                    'end_time': df.index[i + 2],
                    'bottom': fvg_bottom,
                    'top': fvg_top
                })
            
            # 看跌FVG检测：第一根K线最低价 > 第三根K线最高价
            elif k1['Low'] > k3['High']:
                fvg_bottom = k3['High']
                fvg_top = k1['Low']
                fvgs.append({
                    'type': 'bearish',
                    'start_index': i,
                    'end_index': i + 2,
                    'start_time': df.index[i],
                    'end_time': df.index[i + 2],
                    'bottom': fvg_bottom,
                    'top': fvg_top
                })
        
        return fvgs
    
    def check_fvg_breakthrough(self, df: pd.DataFrame, fvg: Dict) -> bool:
        """
        检查FVG是否被价格完全穿过
        
        Args:
            df (pd.DataFrame): 股票数据
            fvg (dict): FVG信息，包含类型、价格范围等
            
        Returns:
            bool: True表示FVG被完全穿过，False表示未被完全穿过
        """
        fvg_end_index = fvg['end_index']
        fvg_type = fvg['type']
        fvg_top = fvg['top']
        fvg_bottom = fvg['bottom']
        
        # 从FVG结束后开始检查突破
        for i in range(fvg_end_index + 1, len(df)):
            current_candle = df.iloc[i]
            
            if fvg_type == 'bullish':
                # 看涨FVG被完全下穿：K线最低价全部穿过FVG区域（低于FVG底部）
                if current_candle['Low'] < fvg_bottom:
                    return True
            elif fvg_type == 'bearish':
                # 看跌FVG被完全上穿：K线最高价全部穿过FVG区域（高于FVG顶部）
                if current_candle['High'] > fvg_top:
                    return True
        
        return False
    
    def find_breakthrough_point(self, df: pd.DataFrame, swing_point: Dict) -> Optional[int]:
        """
        查找水平线被突破的位置

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息，包含index和price

        Returns:
            int: 突破位置的索引，如果没有突破则返回None
        """
        pivot_index = swing_point['index']
        pivot_price = swing_point['price']
        is_resistance = swing_point['type'] == 'resistance'
        pivot_time = swing_point['time']

        # 添加详细的突破检测日志
        line_type = "阻力线" if is_resistance else "支撑线"
        logger.info(f"🔍 检查{line_type}突破: 价格={pivot_price:.3f}, 枢轴时间={pivot_time}, 枢轴索引={pivot_index}")

        # 从枢轴点之后开始检查突破
        breakthrough_found = False
        for i in range(pivot_index + 1, len(df)):
            current_bar = df.iloc[i]
            current_time = df.index[i]

            if is_resistance:
                # 阻力线被上穿：K线最高价超过阻力线价格
                if current_bar['High'] > pivot_price:
                    logger.info(f"🚨 阻力线突破! 时间={current_time}, 最高价={current_bar['High']:.3f} > 阻力价格={pivot_price:.3f}")
                    return i
            else:
                # 支撑线被下穿：K线最低价低于支撑线价格
                if current_bar['Low'] < pivot_price:
                    logger.info(f"🚨 支撑线突破! 时间={current_time}, 最低价={current_bar['Low']:.3f} < 支撑价格={pivot_price:.3f}")
                    return i

        logger.info(f"✅ {line_type}未被突破: 价格={pivot_price:.3f}, 检查了{len(df) - pivot_index - 1}根后续K线")
        return None
    
    def find_fvg_in_range(self, df: pd.DataFrame, swing_point: Dict, fvg_type: str) -> List[Dict]:
        """
        在swing点后的指定范围内寻找特定类型的FVG，并过滤掉被突破的FVG
        
        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'
            
        Returns:
            list: 找到的未被突破的FVG列表
        """
        pivot_index = swing_point['index']
        start_index = pivot_index + 1
        end_index = min(start_index + self.fvg_range_limit, len(df))
        
        # 在指定范围内检测FVG
        all_fvgs = self.detect_fvg(df, start_index, end_index)
        
        # 筛选指定类型的FVG
        filtered_fvgs = [fvg for fvg in all_fvgs if fvg['type'] == fvg_type]
        
        # 进一步过滤掉被突破的FVG
        valid_fvgs = []
        for fvg in filtered_fvgs:
            if not self.check_fvg_breakthrough(df, fvg):
                valid_fvgs.append(fvg)
        
        return valid_fvgs
    
    def check_swing_had_fvg(self, df: pd.DataFrame, swing_point: Dict, fvg_type: str) -> bool:
        """
        检查swing点是否曾经有过FVG（包括已被突破的）

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'

        Returns:
            bool: True表示曾经有过FVG，False表示从未有过FVG
        """
        # 如果是全局极值点，直接返回True，不需要FVG验证
        if swing_point.get('is_global_extreme', False):
            return True

        pivot_index = swing_point['index']
        start_index = pivot_index + 1
        end_index = min(start_index + self.fvg_range_limit, len(df))

        # 在指定范围内检测所有FVG（不过滤突破）
        all_fvgs = self.detect_fvg(df, start_index, end_index)

        # 筛选指定类型的FVG
        filtered_fvgs = [fvg for fvg in all_fvgs if fvg['type'] == fvg_type]

        # 只要找到过FVG就返回True，不管是否被突破
        return len(filtered_fvgs) > 0

    def find_extreme_fvg_in_range(self, df: pd.DataFrame, swing_point: Dict, fvg_type: str) -> Optional[Dict]:
        """
        在swing点后的指定范围内寻找价格最极端的FVG区域

        Args:
            df (pd.DataFrame): 股票数据
            swing_point (dict): swing点信息
            fvg_type (str): 'bullish' 或 'bearish'

        Returns:
            dict: 最极端的FVG区域，如果没有找到则返回None
        """
        # 获取所有有效的FVG
        valid_fvgs = self.find_fvg_in_range(df, swing_point, fvg_type)

        if not valid_fvgs:
            # 对于全局极值点，即使没有FVG也可以创建一个虚拟的FVG区域
            if swing_point.get('is_global_extreme', False):
                swing_price = swing_point['price']
                swing_index = swing_point['index']
                swing_time = swing_point['time']

                # 创建一个基于swing点价格的虚拟FVG区域
                # 区域大小可以基于价格的一个小百分比
                price_range = swing_price * 0.001  # 0.1%的价格范围

                if fvg_type == 'bearish':
                    # 阻力区域：在swing high价格上下创建区域
                    virtual_fvg = {
                        'type': 'bearish',
                        'start_index': swing_index,
                        'end_index': swing_index,
                        'start_time': swing_time,
                        'end_time': swing_time,
                        'bottom': swing_price - price_range,
                        'top': swing_price + price_range,
                        'is_virtual': True  # 标记为虚拟FVG
                    }
                elif fvg_type == 'bullish':
                    # 支撑区域：在swing low价格上下创建区域
                    virtual_fvg = {
                        'type': 'bullish',
                        'start_index': swing_index,
                        'end_index': swing_index,
                        'start_time': swing_time,
                        'end_time': swing_time,
                        'bottom': swing_price - price_range,
                        'top': swing_price + price_range,
                        'is_virtual': True  # 标记为虚拟FVG
                    }
                else:
                    return None

                return virtual_fvg
            else:
                return None

        # 根据FVG类型选择最极端的FVG
        if fvg_type == 'bearish':
            # 对于看跌FVG，选择价格最高的（top价格最高的）
            extreme_fvg = max(valid_fvgs, key=lambda fvg: fvg['top'])
        elif fvg_type == 'bullish':
            # 对于看涨FVG，选择价格最低的（bottom价格最低的）
            extreme_fvg = min(valid_fvgs, key=lambda fvg: fvg['bottom'])
        else:
            return None

        return extreme_fvg

    def create_rectangle_zone(self, swing_point: Dict, extreme_fvg: Dict) -> Optional[Dict]:
        """
        创建矩形支撑/阻力区域

        Args:
            swing_point (dict): swing点信息
            extreme_fvg (dict): 最极端的FVG区域

        Returns:
            dict: 矩形区域信息，包含top、bottom、start_index等
        """
        if extreme_fvg is None:
            return None

        swing_price = swing_point['price']
        swing_index = swing_point['index']
        is_resistance = swing_point['type'] == 'resistance'

        if is_resistance:
            # 阻力区域：swing high价格和看跌FVG的价格范围
            zone_top = max(swing_price, extreme_fvg['top'])
            zone_bottom = min(swing_price, extreme_fvg['bottom'])
        else:
            # 支撑区域：swing low价格和看涨FVG的价格范围
            zone_top = max(swing_price, extreme_fvg['top'])
            zone_bottom = min(swing_price, extreme_fvg['bottom'])

        return {
            'type': 'resistance_zone' if is_resistance else 'support_zone',
            'top': zone_top,
            'bottom': zone_bottom,
            'start_index': swing_index,
            'start_time': swing_point['time'],
            'swing_point': swing_point,
            'extreme_fvg': extreme_fvg,
            'is_resistance': is_resistance
        }

    def filter_swing_points_by_fvg(self, df: pd.DataFrame, swing_highs: List[Dict], swing_lows: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """
        根据FVG过滤swing点，只保留在指定范围内有对应FVG的swing点，或者是全图极值的swing点

        Args:
            df (pd.DataFrame): 股票数据
            swing_highs (list): swing high点列表
            swing_lows (list): swing low点列表

        Returns:
            tuple: (filtered_swing_highs, filtered_swing_lows) 过滤后的swing点列表
        """
        # 找到全图的最高价和最低价
        global_high = df['High'].max()
        global_low = df['Low'].min()

        # 找到全图最高价和最低价对应的索引位置
        global_high_indices = df[df['High'] == global_high].index
        global_low_indices = df[df['Low'] == global_low].index

        # 过滤swing high点：保留曾经有过下跌FVG的点，或者是全图最高点
        filtered_swing_highs = []
        for swing in swing_highs:
            # 检查是否曾经有过下跌FVG（包括已被突破的）
            had_bearish_fvg = self.check_swing_had_fvg(df, swing, 'bearish')
            # 如果曾经有过下跌FVG，或者是全图最高点，保留这个swing high点
            if had_bearish_fvg or swing['price'] == global_high:
                filtered_swing_highs.append(swing)

        # 为全图最高点创建额外的支撑阻力点（如果不在swing点中）
        # 全局最高点只允许有一个，即使有多根K线都达到最高价
        if len(global_high_indices) > 0:
            # 检查是否已经有任何全局最高点在swing点中
            global_high_already_exists = any(
                swing['price'] == global_high for swing in filtered_swing_highs
            )

            if not global_high_already_exists:
                # 只取第一个全图最高点时间，创建唯一的全局最高点
                high_time = global_high_indices[0]
                high_index = df.index.get_loc(high_time)
                filtered_swing_highs.append({
                    'index': high_index,
                    'time': high_time,
                    'price': global_high,
                    'type': 'resistance',
                    'is_global_extreme': True  # 标记为全局极值点
                })

        # 过滤swing low点：保留曾经有过上涨FVG的点，或者是全图最低点
        filtered_swing_lows = []
        for swing in swing_lows:
            # 检查是否曾经有过上涨FVG（包括已被突破的）
            had_bullish_fvg = self.check_swing_had_fvg(df, swing, 'bullish')
            # 如果曾经有过上涨FVG，或者是全图最低点，保留这个swing low点
            if had_bullish_fvg or swing['price'] == global_low:
                filtered_swing_lows.append(swing)

        # 为全图最低点创建额外的支撑阻力点（如果不在swing点中）
        # 全局最低点只允许有一个，即使有多根K线都达到最低价
        if len(global_low_indices) > 0:
            # 检查是否已经有任何全局最低点在swing点中
            global_low_already_exists = any(
                swing['price'] == global_low for swing in filtered_swing_lows
            )

            if not global_low_already_exists:
                # 只取第一个全图最低点时间，创建唯一的全局最低点
                low_time = global_low_indices[0]
                low_index = df.index.get_loc(low_time)
                filtered_swing_lows.append({
                    'index': low_index,
                    'time': low_time,
                    'price': global_low,
                    'type': 'support',
                    'is_global_extreme': True  # 标记为全局极值点
                })

        logger.info(f"FVG过滤后: {len(filtered_swing_highs)} 个swing high点，{len(filtered_swing_lows)} 个swing low点")
        return filtered_swing_highs, filtered_swing_lows

    def create_horizontal_lines(self, df: pd.DataFrame, swing_points: List[Dict]) -> List[Dict]:
        """
        为swing点创建水平支撑阻力线，过滤掉被突破的线

        Args:
            df (pd.DataFrame): 股票数据
            swing_points (list): swing点列表

        Returns:
            list: 水平线信息列表
        """
        horizontal_lines = []

        for i, swing in enumerate(swing_points):
            line_type = "阻力线" if swing['type'] == 'resistance' else "支撑线"
            price = swing['price']

            # 只对422.2支撑线进行详细调试
            if abs(price - 422.2) < 0.1 and swing['type'] == 'support':
                logger.info(f"🔍 [422.2调试] 处理422.2支撑点: 价格={price:.1f}, 时间={swing['time']}")

            # 检查水平线是否被突破
            breakthrough_index = self.find_breakthrough_point(df, swing)

            if breakthrough_index is None:
                # 未被突破，创建水平线
                horizontal_lines.append({
                    'type': 'horizontal_line',
                    'price': swing['price'],
                    'start_index': swing['index'],
                    'start_time': swing['time'],
                    'end_index': len(df) - 1,
                    'end_time': df.index[-1],
                    'swing_point': swing,
                    'is_resistance': swing['type'] == 'resistance',
                    'breakthrough_index': None
                })

                # 只对422.2支撑线进行详细调试
                if abs(price - 422.2) < 0.1 and swing['type'] == 'support':
                    logger.info(f"🚨 [422.2调试] 422.2支撑线被添加到水平线列表！这不应该发生！")

            else:
                breakthrough_time = df.index[breakthrough_index]

                # 只对422.2支撑线进行详细调试
                if abs(price - 422.2) < 0.1 and swing['type'] == 'support':
                    logger.info(f"✅ [422.2调试] 422.2支撑线被正确过滤: 突破时间={breakthrough_time}")

        return horizontal_lines

    def create_fvg_horizontal_lines(self, df: pd.DataFrame, swing_points: List[Dict], fvg_type: str) -> List[Dict]:
        """
        为swing点范围内的所有FVG创建水平支撑阻力线

        Args:
            df (pd.DataFrame): 股票数据
            swing_points (list): swing点列表
            fvg_type (str): 'bullish' 或 'bearish'

        Returns:
            list: FVG水平线信息列表
        """
        fvg_horizontal_lines = []

        for swing in swing_points:
            # 获取该swing点范围内的所有有效FVG
            valid_fvgs = self.find_fvg_in_range(df, swing, fvg_type)

            for fvg in valid_fvgs:
                # 根据FVG类型选择价格
                if fvg_type == 'bearish':
                    # 看跌FVG取价格低的作为阻力水平线
                    fvg_price = fvg['bottom']
                    line_type = 'resistance'
                elif fvg_type == 'bullish':
                    # 看涨FVG取价格高的作为支撑水平线
                    fvg_price = fvg['top']
                    line_type = 'support'
                else:
                    continue

                # 创建FVG水平线
                fvg_horizontal_lines.append({
                    'type': 'fvg_horizontal_line',
                    'price': fvg_price,
                    'start_index': fvg['end_index'],  # 从FVG结束位置开始画线
                    'start_time': fvg['end_time'],
                    'end_index': len(df) - 1,
                    'end_time': df.index[-1],
                    'swing_point': swing,
                    'fvg': fvg,
                    'is_resistance': line_type == 'resistance',
                    'fvg_type': fvg_type,
                    'breakthrough_index': None
                })

        return fvg_horizontal_lines

    def analyze_support_resistance(self, df: pd.DataFrame, enable_fvg_filter: bool = True,
                                 enable_rectangle_zones: bool = True,
                                 enable_fvg_horizontal_lines: bool = True) -> Dict:
        """
        完整的支撑阻力位分析API

        Args:
            df (pd.DataFrame): 股票数据，必须包含Open、High、Low、Close、Volume列，索引为时间
            enable_fvg_filter (bool): 是否启用FVG过滤，默认True
            enable_rectangle_zones (bool): 是否创建矩形区域，默认True
            enable_fvg_horizontal_lines (bool): 是否为所有FVG创建水平线，默认True

        Returns:
            dict: 包含所有支撑阻力位信息的字典
        """
        logger.info("开始支撑阻力位分析")

        # 验证数据格式
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必需的列: {missing_columns}")

        if df.empty:
            raise ValueError("数据为空")

        # 第一步：检测所有swing点
        swing_highs, swing_lows = self.find_swing_points(df)

        # 第二步：如果启用FVG过滤，则过滤swing点
        if enable_fvg_filter:
            swing_highs, swing_lows = self.filter_swing_points_by_fvg(df, swing_highs, swing_lows)

        # 第三步：创建水平支撑阻力线
        resistance_lines = self.create_horizontal_lines(df, swing_highs)
        support_lines = self.create_horizontal_lines(df, swing_lows)

        # 第四步：如果启用矩形区域，则创建矩形支撑阻力区域
        resistance_zones = []
        support_zones = []

        if enable_rectangle_zones:
            # 为每个有效的swing high创建阻力区域
            for swing in swing_highs:
                extreme_fvg = self.find_extreme_fvg_in_range(df, swing, 'bearish')
                if extreme_fvg:
                    rectangle_zone = self.create_rectangle_zone(swing, extreme_fvg)
                    if rectangle_zone:
                        resistance_zones.append(rectangle_zone)

            # 为每个有效的swing low创建支撑区域
            for swing in swing_lows:
                extreme_fvg = self.find_extreme_fvg_in_range(df, swing, 'bullish')
                if extreme_fvg:
                    rectangle_zone = self.create_rectangle_zone(swing, extreme_fvg)
                    if rectangle_zone:
                        support_zones.append(rectangle_zone)

        # 第五步：收集所有FVG信息（用于可视化）
        all_fvgs = []
        fvg_resistance_lines = []
        fvg_support_lines = []

        if enable_fvg_filter:
            # 收集所有FVG
            for swing in swing_highs:
                fvgs = self.find_fvg_in_range(df, swing, 'bearish')
                all_fvgs.extend(fvgs)

            for swing in swing_lows:
                fvgs = self.find_fvg_in_range(df, swing, 'bullish')
                all_fvgs.extend(fvgs)

            # 如果启用FVG水平线，则为所有FVG创建水平线
            if enable_fvg_horizontal_lines:
                fvg_resistance_lines = self.create_fvg_horizontal_lines(df, swing_highs, 'bearish')
                fvg_support_lines = self.create_fvg_horizontal_lines(df, swing_lows, 'bullish')

        # 组装结果
        result = {
            'swing_points': {
                'swing_highs': swing_highs,
                'swing_lows': swing_lows,
                'total_swing_highs': len(swing_highs),
                'total_swing_lows': len(swing_lows)
            },
            'horizontal_lines': {
                'resistance_lines': resistance_lines,
                'support_lines': support_lines,
                'total_resistance_lines': len(resistance_lines),
                'total_support_lines': len(support_lines)
            },
            'fvg_horizontal_lines': {
                'fvg_resistance_lines': fvg_resistance_lines,
                'fvg_support_lines': fvg_support_lines,
                'total_fvg_resistance_lines': len(fvg_resistance_lines),
                'total_fvg_support_lines': len(fvg_support_lines)
            },
            'rectangle_zones': {
                'resistance_zones': resistance_zones,
                'support_zones': support_zones,
                'total_resistance_zones': len(resistance_zones),
                'total_support_zones': len(support_zones)
            },
            'fvg_regions': {
                'all_fvgs': all_fvgs,
                'total_fvgs': len(all_fvgs)
            },
            'analysis_params': {
                'swing_length': self.swing_length,
                'fvg_range_limit': self.fvg_range_limit,
                'enable_fvg_filter': enable_fvg_filter,
                'enable_rectangle_zones': enable_rectangle_zones,
                'enable_fvg_horizontal_lines': enable_fvg_horizontal_lines
            },
            'data_info': {
                'total_candles': len(df),
                'time_range': {
                    'start': df.index[0],
                    'end': df.index[-1]
                },
                'price_range': {
                    'high': df['High'].max(),
                    'low': df['Low'].min()
                }
            }
        }

        logger.info(f"分析完成: {len(swing_highs)}个阻力点, {len(swing_lows)}个支撑点, "
                   f"{len(resistance_lines)}条阻力线, {len(support_lines)}条支撑线, "
                   f"{len(fvg_resistance_lines)}条FVG阻力线, {len(fvg_support_lines)}条FVG支撑线, "
                   f"{len(resistance_zones)}个阻力区域, {len(support_zones)}个支撑区域")

        return result

    def get_current_support_resistance_levels(self, df: pd.DataFrame, current_price: Optional[float] = None) -> Dict:
        """
        获取当前价格附近的关键支撑阻力位

        Args:
            df (pd.DataFrame): 股票数据
            current_price (float): 当前价格，如果为None则使用最后一根K线的收盘价

        Returns:
            dict: 当前关键支撑阻力位信息
        """
        if current_price is None:
            current_price = df['Close'].iloc[-1]

        # 进行完整分析
        analysis_result = self.analyze_support_resistance(df)

        # 提取关键支撑阻力位
        resistance_levels = []
        support_levels = []

        # 从水平线中提取
        for line in analysis_result['horizontal_lines']['resistance_lines']:
            if line['price'] > current_price:
                resistance_levels.append({
                    'price': line['price'],
                    'type': 'horizontal_resistance',
                    'distance': line['price'] - current_price,
                    'distance_percent': ((line['price'] - current_price) / current_price) * 100,
                    'source': line
                })

        for line in analysis_result['horizontal_lines']['support_lines']:
            if line['price'] < current_price:
                support_levels.append({
                    'price': line['price'],
                    'type': 'horizontal_support',
                    'distance': current_price - line['price'],
                    'distance_percent': ((current_price - line['price']) / current_price) * 100,
                    'source': line
                })

        # 从FVG水平线中提取
        for line in analysis_result['fvg_horizontal_lines']['fvg_resistance_lines']:
            if line['price'] > current_price:
                resistance_levels.append({
                    'price': line['price'],
                    'type': 'fvg_resistance',
                    'distance': line['price'] - current_price,
                    'distance_percent': ((line['price'] - current_price) / current_price) * 100,
                    'source': line
                })

        for line in analysis_result['fvg_horizontal_lines']['fvg_support_lines']:
            if line['price'] < current_price:
                support_levels.append({
                    'price': line['price'],
                    'type': 'fvg_support',
                    'distance': current_price - line['price'],
                    'distance_percent': ((current_price - line['price']) / current_price) * 100,
                    'source': line
                })

        # 从矩形区域中提取
        for zone in analysis_result['rectangle_zones']['resistance_zones']:
            if zone['bottom'] > current_price:
                resistance_levels.append({
                    'price': zone['bottom'],
                    'price_range': (zone['bottom'], zone['top']),
                    'type': 'zone_resistance',
                    'distance': zone['bottom'] - current_price,
                    'distance_percent': ((zone['bottom'] - current_price) / current_price) * 100,
                    'source': zone
                })

        for zone in analysis_result['rectangle_zones']['support_zones']:
            if zone['top'] < current_price:
                support_levels.append({
                    'price': zone['top'],
                    'price_range': (zone['bottom'], zone['top']),
                    'type': 'zone_support',
                    'distance': current_price - zone['top'],
                    'distance_percent': ((current_price - zone['top']) / current_price) * 100,
                    'source': zone
                })

        # 按距离排序
        resistance_levels.sort(key=lambda x: x['distance'])
        support_levels.sort(key=lambda x: x['distance'])

        return {
            'current_price': current_price,
            'nearest_resistance': resistance_levels[:3] if resistance_levels else [],
            'nearest_support': support_levels[:3] if support_levels else [],
            'all_resistance': resistance_levels,
            'all_support': support_levels
        }

    def export_to_dict(self, analysis_result: Dict) -> Dict:
        """
        将分析结果导出为简化的字典格式，便于JSON序列化

        Args:
            analysis_result (dict): analyze_support_resistance的返回结果

        Returns:
            dict: 简化的字典格式
        """
        def convert_timestamp(obj):
            """转换时间戳为字符串"""
            if hasattr(obj, 'strftime'):
                return obj.strftime('%Y-%m-%d %H:%M:%S')
            return obj

        def clean_dict(d):
            """递归清理字典中的时间戳"""
            if isinstance(d, dict):
                return {k: clean_dict(v) for k, v in d.items()}
            elif isinstance(d, list):
                return [clean_dict(item) for item in d]
            else:
                return convert_timestamp(d)

        return clean_dict(analysis_result)


def create_analyzer(swing_length: int = 10, fvg_range_limit: int = 20) -> SupportResistanceAnalyzer:
    """
    创建支撑阻力位分析器的便捷函数

    Args:
        swing_length (int): swing点检测的左右确认周期，默认10
        fvg_range_limit (int): FVG搜索范围限制，默认10

    Returns:
        SupportResistanceAnalyzer: 分析器实例
    """
    return SupportResistanceAnalyzer(swing_length, fvg_range_limit)


def analyze_kline_data(df: pd.DataFrame, swing_length: int = 10, fvg_range_limit: int = 20,
                      enable_fvg_filter: bool = True, enable_rectangle_zones: bool = True,
                      enable_fvg_horizontal_lines: bool = True) -> Dict:
    """
    一键分析K线数据的支撑阻力位

    Args:
        df (pd.DataFrame): 股票数据，必须包含Open、High、Low、Close、Volume列，索引为时间
        swing_length (int): swing点检测的左右确认周期，默认10
        fvg_range_limit (int): FVG搜索范围限制，默认10
        enable_fvg_filter (bool): 是否启用FVG过滤，默认True
        enable_rectangle_zones (bool): 是否创建矩形区域，默认True
        enable_fvg_horizontal_lines (bool): 是否为所有FVG创建水平线，默认True

    Returns:
        dict: 完整的支撑阻力位分析结果
    """
    analyzer = SupportResistanceAnalyzer(swing_length, fvg_range_limit)
    return analyzer.analyze_support_resistance(df, enable_fvg_filter, enable_rectangle_zones, enable_fvg_horizontal_lines)


# 示例用法
if __name__ == "__main__":
    import sys
    import os

    # 添加当前目录到路径，以便导入kline_plotter
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))

    def demo_with_sample_data():
        """使用示例数据演示API功能"""
        print("=== 支撑阻力位分析API演示 ===")

        try:
            # 尝试从kline_plotter获取真实数据
            from kline_plotter import KLinePlotter

            print("正在获取真实股票数据...")
            plotter = KLinePlotter()
            df, period_name = plotter.load_stock_data_from_pytdx("600000.SH", 6)
            filtered_df = plotter.filter_data_by_current_date(df, 6)

            print(f"数据获取成功: {len(filtered_df)}条{period_name}K线数据")
            print(f"时间范围: {filtered_df.index[0]} 到 {filtered_df.index[-1]}")

            # 创建分析器
            analyzer = create_analyzer(swing_length=8, fvg_range_limit=20)

            # 进行完整分析
            print("\n正在进行支撑阻力位分析...")
            result = analyzer.analyze_support_resistance(filtered_df)

            # 显示分析结果摘要
            print(f"\n=== 分析结果摘要 ===")
            print(f"Swing点: {result['swing_points']['total_swing_highs']}个阻力点, {result['swing_points']['total_swing_lows']}个支撑点")
            print(f"水平线: {result['horizontal_lines']['total_resistance_lines']}条阻力线, {result['horizontal_lines']['total_support_lines']}条支撑线")
            print(f"矩形区域: {result['rectangle_zones']['total_resistance_zones']}个阻力区域, {result['rectangle_zones']['total_support_zones']}个支撑区域")
            print(f"FVG区域: {result['fvg_regions']['total_fvgs']}个")

            # 获取当前关键支撑阻力位
            current_levels = analyzer.get_current_support_resistance_levels(filtered_df)
            current_price = current_levels['current_price']

            print(f"\n=== 当前价格分析 ===")
            print(f"当前价格: {current_price:.2f}")

            print(f"\n最近的阻力位:")
            for i, resistance in enumerate(current_levels['nearest_resistance'][:3], 1):
                print(f"  {i}. {resistance['price']:.2f} (距离: +{resistance['distance_percent']:.2f}%) - {resistance['type']}")

            print(f"\n最近的支撑位:")
            for i, support in enumerate(current_levels['nearest_support'][:3], 1):
                print(f"  {i}. {support['price']:.2f} (距离: -{support['distance_percent']:.2f}%) - {support['type']}")

            # 显示一些具体的swing点信息
            print(f"\n=== 关键Swing点详情 ===")
            if result['swing_points']['swing_highs']:
                print("阻力点 (Swing Highs):")
                for swing in result['swing_points']['swing_highs'][:3]:
                    print(f"  时间: {swing['time']}, 价格: {swing['price']:.2f}")

            if result['swing_points']['swing_lows']:
                print("支撑点 (Swing Lows):")
                for swing in result['swing_points']['swing_lows'][:3]:
                    print(f"  时间: {swing['time']}, 价格: {swing['price']:.2f}")

            print(f"\n=== API调用示例 ===")
            print("# 基本用法:")
            print("from support_and_resistance import analyze_kline_data")
            print("result = analyze_kline_data(df)")
            print("")
            print("# 高级用法:")
            print("from support_and_resistance import create_analyzer")
            print("analyzer = create_analyzer(swing_length=10)")
            print("result = analyzer.analyze_support_resistance(df)")
            print("current_levels = analyzer.get_current_support_resistance_levels(df)")

        except ImportError:
            print("无法导入kline_plotter，请确保文件在同一目录下")
        except Exception as e:
            print(f"演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

    # 运行演示
    demo_with_sample_data()
