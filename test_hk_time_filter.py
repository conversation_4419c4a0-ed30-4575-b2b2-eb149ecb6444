#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股时间过滤功能测试脚本

测试新增的港股K线时间过滤功能：
1. 禁止16:30等收盘后的K线出现
2. 禁止12:00-13:00午休时间的K线
3. 禁止9:30之前开盘前的K线
4. 验证时间过滤逻辑的正确性
"""

import pandas as pd
from datetime import datetime


def is_valid_hk_kline_time(time_str, period):
    """
    验证港股K线时间是否有效（测试版本）
    禁止16:30等收盘后的K线出现
    """
    try:
        # 解析时间字符串
        if isinstance(time_str, str):
            if len(time_str) == 14:  # 格式：20250725163000
                dt = pd.to_datetime(time_str, format='%Y%m%d%H%M%S')
            elif '-' in time_str and ':' in time_str:  # 格式：2025-07-25 16:30
                dt = pd.to_datetime(time_str)
            else:
                dt = pd.to_datetime(time_str)
        else:
            dt = pd.to_datetime(time_str)
        
        # 获取时间部分
        time_part = dt.time()
        hour = dt.hour
        minute = dt.minute
        
        # 定义港股交易时间段
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("12:00", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("16:00", "%H:%M").time()
        
        # 检查是否在交易时间段内
        is_morning_session = morning_start <= time_part <= morning_end
        is_afternoon_session = afternoon_start <= time_part <= afternoon_end
        
        is_valid = is_morning_session or is_afternoon_session
        
        # 特别检查：禁止16:30等收盘后时间
        if hour == 16 and minute > 0:
            print(f"WARNING: [港股时间过滤] 检测到收盘后K线时间: {time_str} {period}, 已过滤")
            return False
        
        # 特别检查：禁止12:00-13:00午休时间
        if hour == 12 and minute > 0:
            print(f"WARNING: [港股时间过滤] 检测到午休时间K线: {time_str} {period}, 已过滤")
            return False
        
        if hour == 12 and minute == 0:
            # 12:00是允许的（午市结束）
            pass
        elif 12 < hour < 13:
            print(f"WARNING: [港股时间过滤] 检测到午休时间K线: {time_str} {period}, 已过滤")
            return False
        
        # 特别检查：禁止9:30之前的时间
        if hour < 9 or (hour == 9 and minute < 30):
            print(f"WARNING: [港股时间过滤] 检测到开盘前K线时间: {time_str} {period}, 已过滤")
            return False
        
        # 特别检查：禁止16:00之后的时间
        if hour > 16:
            print(f"WARNING: [港股时间过滤] 检测到收盘后K线时间: {time_str} {period}, 已过滤")
            return False
        
        if not is_valid:
            print(f"DEBUG: [港股时间过滤] K线时间不在交易时间段内: {time_str} {period}, 已过滤")
        
        return is_valid
        
    except Exception as e:
        print(f"ERROR: [港股时间过滤] 时间解析失败: {time_str} {period} - {e}")
        return False


def test_hk_time_filter():
    """测试港股时间过滤功能"""
    print("=" * 80)
    print("港股时间过滤功能测试")
    print("=" * 80)
    
    # 测试用例：[时间字符串, 周期, 期望结果, 描述]
    test_cases = [
        # 正常交易时间（应该通过）
        ("2025-07-25 09:30", "60m", True, "开盘时间"),
        ("2025-07-25 10:00", "60m", True, "早市正常时间"),
        ("2025-07-25 11:30", "60m", True, "早市正常时间"),
        ("2025-07-25 12:00", "60m", True, "早市结束时间"),
        ("2025-07-25 13:00", "60m", True, "午市开始时间"),
        ("2025-07-25 14:30", "60m", True, "午市正常时间"),
        ("2025-07-25 15:30", "60m", True, "午市正常时间"),
        ("2025-07-25 16:00", "60m", True, "收盘时间"),
        
        # 应该被过滤的时间
        ("2025-07-25 16:30", "60m", False, "收盘后时间（问题时间）"),
        ("2025-07-25 16:15", "60m", False, "收盘后时间"),
        ("2025-07-25 17:00", "60m", False, "收盘后时间"),
        ("2025-07-25 12:30", "60m", False, "午休时间"),
        ("2025-07-25 12:15", "60m", False, "午休时间"),
        ("2025-07-25 09:00", "60m", False, "开盘前时间"),
        ("2025-07-25 09:15", "60m", False, "开盘前时间"),
        ("2025-07-25 08:30", "60m", False, "开盘前时间"),
        
        # 边界情况
        ("2025-07-25 09:29", "60m", False, "开盘前1分钟"),
        ("2025-07-25 12:01", "60m", False, "午休开始1分钟"),
        ("2025-07-25 12:59", "60m", False, "午休结束前1分钟"),
        ("2025-07-25 16:01", "60m", False, "收盘后1分钟"),
        
        # 不同格式的时间字符串
        ("20250725163000", "60m", False, "数字格式的收盘后时间"),
        ("20250725140000", "60m", True, "数字格式的正常时间"),
    ]
    
    print("测试港股K线时间过滤逻辑：")
    print("✓ 正常交易时间应该通过")
    print("✗ 收盘后、午休、开盘前时间应该被过滤")
    print()
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, (time_str, period, expected, description) in enumerate(test_cases, 1):
        print(f"测试 {i:2d}: {description}")
        print(f"        时间: {time_str}")
        print(f"        周期: {period}")
        print(f"        期望: {'通过' if expected else '过滤'}")
        
        # 执行测试
        result = is_valid_hk_kline_time(time_str, period)
        
        # 检查结果
        if result == expected:
            status = "✓ 通过"
            passed_tests += 1
        else:
            status = "✗ 失败"
        
        print(f"        结果: {'通过' if result else '过滤'} - {status}")
        print()
    
    # 测试总结
    print("=" * 80)
    print("测试总结")
    print("=" * 80)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("✓ 所有测试通过！港股时间过滤功能正常工作")
        print("✓ 16:30等收盘后K线将被成功过滤")
    else:
        print("✗ 部分测试失败，需要检查时间过滤逻辑")
    
    print("=" * 80)
    
    return passed_tests == total_tests


def test_specific_problem():
    """测试具体的16:30问题"""
    print("\n" + "=" * 80)
    print("测试具体的16:30问题")
    print("=" * 80)
    
    problem_times = [
        "2025-07-25 16:30",
        "2025-07-25 16:15", 
        "2025-07-25 16:45",
        "2025-07-25 17:00",
    ]
    
    print("测试收盘后的问题时间：")
    
    for time_str in problem_times:
        print(f"\n测试时间: {time_str}")
        result = is_valid_hk_kline_time(time_str, "60m")
        if result:
            print(f"✗ 错误：{time_str} 应该被过滤但没有被过滤")
        else:
            print(f"✓ 正确：{time_str} 已被成功过滤")
    
    print("\n" + "=" * 80)


if __name__ == "__main__":
    # 运行基本测试
    success = test_hk_time_filter()
    
    # 运行具体问题测试
    test_specific_problem()
    
    if success:
        print("\n🎉 港股时间过滤功能测试完成，所有测试通过！")
        print("🚫 16:30等收盘后K线将被成功禁止")
    else:
        print("\n⚠️ 港股时间过滤功能测试完成，但存在问题需要修复")
