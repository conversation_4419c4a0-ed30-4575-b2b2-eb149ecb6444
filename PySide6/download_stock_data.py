#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独的股票数据下载脚本
使用xtquant库下载600895.SH的15分钟K线数据
时间范围：2024-07-01 到 2025-07-01
"""

import os
import pandas as pd
from datetime import datetime
import logging

try:
    from xtquant import xtdata
except ImportError:
    print("错误：未找到xtquant库，请确保已正确安装xtquant")
    exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('download_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def download_stock_data():
    """
    下载600895.SH的5分钟K线数据并保存为CSV文件
    """
    # 股票代码
    stock_code = "09988.HK"
    
    # 时间参数
    start_date = "20240701"
    end_date = "20250701"
    period = "60m"
    
    # 输出文件名
    output_filename = f"{stock_code}_{period}_{start_date}_{end_date}.csv"
    
    print(f"开始下载股票数据...")
    print(f"股票代码: {stock_code}")
    print(f"周期类型: {period}")
    print(f"开始日期: {start_date}")
    print(f"结束日期: {end_date}")
    print(f"输出文件: {output_filename}")
    print("-" * 50)
    
    try:
        # 第一步：下载历史数据到本地缓存
        print("正在下载历史数据到本地缓存...")
        logging.info(f"开始下载 {stock_code} 的历史数据")
        
        xtdata.download_history_data(
            stock_code, 
            period=period,
            start_time=start_date, 
            end_time=end_date
        )
        
        print("历史数据下载完成！")
        logging.info("历史数据下载完成")
        
        # 第二步：从本地缓存读取数据
        print("正在从本地缓存读取数据...")
        
        # 定义要获取的字段
        field_list = [
            'time',      # 时间戳
            'open',      # 开盘价
            'high',      # 最高价
            'low',       # 最低价
            'close',     # 收盘价
            'volume',    # 成交量
            'amount',    # 成交额
            'settelementPrice',  # 结算价
            'openInterest'       # 持仓量
        ]
        
        # 获取市场数据
        data = xtdata.get_market_data_ex(
            field_list=field_list,
            stock_list=[stock_code],
            period=period,
            start_time=start_date,
            end_time=end_date,
            count=-1,  # -1表示获取所有数据
            dividend_type='front',
            fill_data=True
        )
        
        if not data or stock_code not in data:
            raise Exception(f"未能获取到 {stock_code} 的数据")
        
        # 获取股票数据
        df = data[stock_code]
        
        if df.empty:
            raise Exception(f"获取到的 {stock_code} 数据为空")
        
        print(f"成功获取数据，共 {len(df)} 条记录")
        logging.info(f"成功获取数据，共 {len(df)} 条记录")
        
        # 第三步：数据处理
        print("正在处理数据...")
        
        # 转换时间戳为可读格式
        df["time"] = pd.to_datetime(df["time"].astype(float), unit='ms') + pd.Timedelta(hours=8)
        
        # 添加日期和时间列
        df["date"] = df["time"].dt.strftime("%Y-%m-%d")
        df["time_str"] = df["time"].dt.strftime("%H:%M:%S")
        
        # 重新排列列的顺序
        columns_order = ["date", "time_str", "open", "high", "low", "close", "volume", "amount"]
        
        # 只保留存在的列
        available_columns = [col for col in columns_order if col in df.columns]
        df_final = df[available_columns].copy()
        
        # 第四步：保存数据到CSV文件
        print(f"正在保存数据到文件: {output_filename}")
        
        df_final.to_csv(output_filename, index=False, encoding='utf-8-sig')
        
        # 验证文件是否保存成功
        if os.path.exists(output_filename):
            file_size = os.path.getsize(output_filename)
            print(f"数据保存成功！")
            print(f"文件大小: {file_size:,} 字节")
            print(f"数据条数: {len(df_final):,} 条")
            print(f"数据时间范围: {df_final['date'].min()} 到 {df_final['date'].max()}")
            
            # 显示前几行数据作为预览
            print("\n数据预览（前5行）:")
            print(df_final.head().to_string(index=False))
            
            logging.info(f"数据保存成功，文件: {output_filename}, 大小: {file_size} 字节")
            
        else:
            raise Exception("文件保存失败")
            
    except Exception as e:
        error_msg = f"下载数据时出错: {str(e)}"
        print(f"错误: {error_msg}")
        logging.error(error_msg)
        return False
    
    return True

def main():
    """
    主函数
    """
    print("=" * 60)
    print("股票数据下载工具")
    print("=" * 60)
    
    # 检查xtquant连接状态
    try:
        print("正在检查xtquant连接状态...")
        # 这里可以添加连接检查的代码
        print("xtquant库加载成功")
    except Exception as e:
        print(f"xtquant库检查失败: {str(e)}")
        return
    
    # 开始下载数据
    success = download_stock_data()
    
    if success:
        print("\n" + "=" * 60)
        print("数据下载完成！")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("数据下载失败！请查看错误信息。")
        print("=" * 60)

if __name__ == "__main__":
    main()
