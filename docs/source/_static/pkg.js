/*!
 * @license
 * TradingView Lightweight Charts™ v4.1.0-dev+202306102016
 * Copyright (c) 2023 TradingView, Inc.
 * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
 */
!function(){"use strict";var t,i;function s(t,i){const s={0:[],1:[t.lineWidth,t.lineWidth],2:[2*t.lineWidth,2*t.lineWidth],3:[6*t.lineWidth,6*t.lineWidth],4:[t.lineWidth,4*t.lineWidth]}[i];t.setLineDash(s)}function e(t,i,s,e){t.beginPath();const h=t.lineWidth%2?.5:0;t.moveTo(s,i+h),t.lineTo(e,i+h),t.stroke()}function h(t,i){if(!t)throw new Error("Assertion failed"+(i?": "+i:""))}function n(t){if(void 0===t)throw new Error("Value is undefined");return t}function r(t){if(null===t)throw new Error("Value is null");return t}function o(t){return r(n(t))}!function(t){t[t.Simple=0]="Simple",t[t.WithSteps=1]="WithSteps",t[t.Curved=2]="Curved"}(t||(t={})),function(t){t[t.Solid=0]="Solid",t[t.Dotted=1]="Dotted",t[t.Dashed=2]="Dashed",t[t.LargeDashed=3]="LargeDashed",t[t.SparseDotted=4]="SparseDotted"}(i||(i={}));const l={khaki:"#f0e68c",azure:"#f0ffff",aliceblue:"#f0f8ff",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",gray:"#808080",green:"#008000",honeydew:"#f0fff0",floralwhite:"#fffaf0",lightblue:"#add8e6",lightcoral:"#f08080",lemonchiffon:"#fffacd",hotpink:"#ff69b4",lightyellow:"#ffffe0",greenyellow:"#adff2f",lightgoldenrodyellow:"#fafad2",limegreen:"#32cd32",linen:"#faf0e6",lightcyan:"#e0ffff",magenta:"#f0f",maroon:"#800000",olive:"#808000",orange:"#ffa500",oldlace:"#fdf5e6",mediumblue:"#0000cd",transparent:"#0000",lime:"#0f0",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",midnightblue:"#191970",orchid:"#da70d6",mediumorchid:"#ba55d3",mediumturquoise:"#48d1cc",orangered:"#ff4500",royalblue:"#4169e1",powderblue:"#b0e0e6",red:"#f00",coral:"#ff7f50",turquoise:"#40e0d0",white:"#fff",whitesmoke:"#f5f5f5",wheat:"#f5deb3",teal:"#008080",steelblue:"#4682b4",bisque:"#ffe4c4",aquamarine:"#7fffd4",aqua:"#0ff",sienna:"#a0522d",silver:"#c0c0c0",springgreen:"#00ff7f",antiquewhite:"#faebd7",burlywood:"#deb887",brown:"#a52a2a",beige:"#f5f5dc",chocolate:"#d2691e",chartreuse:"#7fff00",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cadetblue:"#5f9ea0",tomato:"#ff6347",fuchsia:"#f0f",blue:"#00f",salmon:"#fa8072",blanchedalmond:"#ffebcd",slateblue:"#6a5acd",slategray:"#708090",thistle:"#d8bfd8",tan:"#d2b48c",cyan:"#0ff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",blueviolet:"#8a2be2",black:"#000",darkmagenta:"#8b008b",darkslateblue:"#483d8b",darkkhaki:"#bdb76b",darkorchid:"#9932cc",darkorange:"#ff8c00",darkgreen:"#006400",darkred:"#8b0000",dodgerblue:"#1e90ff",darkslategray:"#2f4f4f",dimgray:"#696969",deepskyblue:"#00bfff",firebrick:"#b22222",forestgreen:"#228b22",indigo:"#4b0082",ivory:"#fffff0",lavenderblush:"#fff0f5",feldspar:"#d19275",indianred:"#cd5c5c",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightskyblue:"#87cefa",lightslategray:"#789",lightslateblue:"#8470ff",snow:"#fffafa",lightseagreen:"#20b2aa",lightsalmon:"#ffa07a",darksalmon:"#e9967a",darkviolet:"#9400d3",mediumpurple:"#9370d8",mediumaquamarine:"#66cdaa",skyblue:"#87ceeb",lavender:"#e6e6fa",lightsteelblue:"#b0c4de",mediumvioletred:"#c71585",mintcream:"#f5fffa",navajowhite:"#ffdead",navy:"#000080",olivedrab:"#6b8e23",palevioletred:"#d87093",violetred:"#d02090",yellow:"#ff0",yellowgreen:"#9acd32",lawngreen:"#7cfc00",pink:"#ffc0cb",paleturquoise:"#afeeee",palegoldenrod:"#eee8aa",darkolivegreen:"#556b2f",darkseagreen:"#8fbc8f",darkturquoise:"#00ced1",peachpuff:"#ffdab9",deeppink:"#ff1493",violet:"#ee82ee",palegreen:"#98fb98",mediumseagreen:"#3cb371",peru:"#cd853f",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",purple:"#800080",seagreen:"#2e8b57",seashell:"#fff5ee",papayawhip:"#ffefd5",mediumslateblue:"#7b68ee",plum:"#dda0dd",mediumspringgreen:"#00fa9a"};function a(t){return t<0?0:t>255?255:Math.round(t)||0}function u(t){return t<=0||t>0?t<0?0:t>1?1:Math.round(1e4*t)/1e4:0}const c=/^#([0-9a-f])([0-9a-f])([0-9a-f])([0-9a-f])?$/i,d=/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})?$/i,f=/^rgb\(\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*\)$/,p=/^rgba\(\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?[\d]{0,10}(?:\.\d+)?)\s*\)$/;function m(t){(t=t.toLowerCase())in l&&(t=l[t]);{const i=p.exec(t)||f.exec(t);if(i)return[a(parseInt(i[1],10)),a(parseInt(i[2],10)),a(parseInt(i[3],10)),u(i.length<5?1:parseFloat(i[4]))]}{const i=d.exec(t);if(i)return[a(parseInt(i[1],16)),a(parseInt(i[2],16)),a(parseInt(i[3],16)),1]}{const i=c.exec(t);if(i)return[a(17*parseInt(i[1],16)),a(17*parseInt(i[2],16)),a(17*parseInt(i[3],16)),1]}throw new Error(`Cannot parse color: ${t}`)}function v(t){const i=m(t);return{background:`rgb(${i[0]}, ${i[1]}, ${i[2]})`,foreground:(s=i,.199*s[0]+.687*s[1]+.114*s[2]>160?"black":"white")};var s}class b{constructor(){this._listeners=[]}subscribe(t,i,s){const e={callback:t,linkedObject:i,singleshot:!0===s};this._listeners.push(e)}unsubscribe(t){const i=this._listeners.findIndex((i=>t===i.callback));i>-1&&this._listeners.splice(i,1)}unsubscribeAll(t){this._listeners=this._listeners.filter((i=>i.linkedObject!==t))}fire(t,i,s){const e=[...this._listeners];this._listeners=this._listeners.filter((t=>!t.singleshot)),e.forEach((e=>e.callback(t,i,s)))}hasListeners(){return this._listeners.length>0}destroy(){this._listeners=[]}}function g(t,...i){for(const s of i)for(const i in s)void 0!==s[i]&&("object"!=typeof s[i]||void 0===t[i]?t[i]=s[i]:g(t[i],s[i]));return t}function w(t){return"number"==typeof t&&isFinite(t)}function M(t){return"number"==typeof t&&t%1==0}function S(t){return"string"==typeof t}function x(t){return"boolean"==typeof t}function _(t){const i=t;if(!i||"object"!=typeof i)return i;let s,e,h;for(e in s=Array.isArray(i)?[]:{},i)i.hasOwnProperty(e)&&(h=i[e],s[e]=h&&"object"==typeof h?_(h):h);return s}function y(t){return null!==t}function k(t){return null===t?void 0:t}const C="-apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif";function T(t,i,s){return void 0===i&&(i=C),`${s=void 0!==s?`${s} `:""}${t}px ${i}`}var P;!function(t){t[t.BorderSize=1]="BorderSize",t[t.TickLength=5]="TickLength"}(P||(P={}));class R{constructor(t){this._rendererOptions={borderSize:1,tickLength:5,fontSize:NaN,font:"",fontFamily:"",color:"",paneBackgroundColor:"",paddingBottom:0,paddingInner:0,paddingOuter:0,paddingTop:0,baselineOffset:0},this._chartModel=t}options(){const t=this._rendererOptions,i=this._fontSize(),s=this._fontFamily();return t.fontSize===i&&t.fontFamily===s||(t.fontSize=i,t.fontFamily=s,t.font=T(i,s),t.paddingTop=2.5/12*i,t.paddingBottom=t.paddingTop,t.paddingInner=i/12*t.tickLength,t.paddingOuter=i/12*t.tickLength,t.baselineOffset=0),t.color=this._textColor(),t.paneBackgroundColor=this._paneBackgroundColor(),this._rendererOptions}_textColor(){return this._chartModel.options().layout.textColor}_paneBackgroundColor(){return this._chartModel.backgroundTopColor()}_fontSize(){return this._chartModel.options().layout.fontSize}_fontFamily(){return this._chartModel.options().layout.fontFamily}}class D{constructor(){this._renderers=[]}setRenderers(t){this._renderers=t}draw(t,i,s){this._renderers.forEach((e=>{e.draw(t,i,s)}))}}class A{draw(t,i,s){t.useMediaCoordinateSpace((t=>this._drawImpl(t,i,s)))}drawBackground(t,i,s){t.useMediaCoordinateSpace((t=>this._drawBackgroundImpl(t,i,s)))}_drawBackgroundImpl(t,i,s){}}class E extends A{constructor(){super(...arguments),this._data=null}setData(t){this._data=t}_drawImpl({context:t}){if(null===this._data||null===this._data.visibleRange)return;const i=this._data.visibleRange,s=this._data,e=e=>{t.beginPath();for(let h=i.to-1;h>=i.from;--h){const i=s.items[h];t.moveTo(i.x,i.y),t.arc(i.x,i.y,e,0,2*Math.PI)}t.fill()};s.lineWidth>0&&(t.fillStyle=s.backColor,e(s.radius+s.lineWidth)),t.fillStyle=s.lineColor,e(s.radius)}}function O(){return{items:[{x:0,y:0,time:0,price:0}],lineColor:"",backColor:"",radius:0,lineWidth:0,visibleRange:null}}const B={from:0,to:1};class L{constructor(t,i){this._compositeRenderer=new D,this._markersRenderers=[],this._markersData=[],this._invalidated=!0,this._chartModel=t,this._crosshair=i,this._compositeRenderer.setRenderers(this._markersRenderers)}update(t){const i=this._chartModel.serieses();i.length!==this._markersRenderers.length&&(this._markersData=i.map(O),this._markersRenderers=this._markersData.map((t=>{const i=new E;return i.setData(t),i})),this._compositeRenderer.setRenderers(this._markersRenderers)),this._invalidated=!0}renderer(){return this._invalidated&&(this._updateImpl(),this._invalidated=!1),this._compositeRenderer}_updateImpl(){const t=this._chartModel.serieses(),i=this._crosshair.appliedIndex(),s=this._chartModel.timeScale();t.forEach(((t,e)=>{var h;const n=this._markersData[e],o=t.markerDataAtIndex(i);if(null===o||!t.visible())return void(n.visibleRange=null);const l=r(t.firstValue());n.lineColor=o.backgroundColor,n.radius=o.radius,n.lineWidth=o.borderWidth,n.items[0].price=o.price,n.items[0].y=t.priceScale().priceToCoordinate(o.price,l.value),n.backColor=null!==(h=o.borderColor)&&void 0!==h?h:this._chartModel.backgroundColorAtYPercentFromTop(n.items[0].y/t.priceScale().height()),n.items[0].time=i,n.items[0].x=s.indexToCoordinate(i),n.visibleRange=B}))}}class z{draw(t,i,s){t.useBitmapCoordinateSpace((t=>this._drawImpl(t,i,s)))}}class I extends z{constructor(t){super(),this._data=t}_drawImpl({context:t,bitmapSize:i,horizontalPixelRatio:h,verticalPixelRatio:n}){if(null===this._data)return;const r=this._data.vertLine.visible,o=this._data.horzLine.visible;if(!r&&!o)return;const l=Math.round(this._data.x*h),a=Math.round(this._data.y*n);t.lineCap="butt",r&&l>=0&&(t.lineWidth=Math.floor(this._data.vertLine.lineWidth*h),t.strokeStyle=this._data.vertLine.color,t.fillStyle=this._data.vertLine.color,s(t,this._data.vertLine.lineStyle),function(t,i,s,e){t.beginPath();const h=t.lineWidth%2?.5:0;t.moveTo(i+h,s),t.lineTo(i+h,e),t.stroke()}(t,l,0,i.height)),o&&a>=0&&(t.lineWidth=Math.floor(this._data.horzLine.lineWidth*n),t.strokeStyle=this._data.horzLine.color,t.fillStyle=this._data.horzLine.color,s(t,this._data.horzLine.lineStyle),e(t,a,0,i.width))}}class N{constructor(t){this._invalidated=!0,this._rendererData={vertLine:{lineWidth:1,lineStyle:0,color:"",visible:!1},horzLine:{lineWidth:1,lineStyle:0,color:"",visible:!1},x:0,y:0},this._renderer=new I(this._rendererData),this._source=t}update(){this._invalidated=!0}renderer(){return this._invalidated&&(this._updateImpl(),this._invalidated=!1),this._renderer}_updateImpl(){const t=this._source.visible(),i=r(this._source.pane()),s=i.model().options().crosshair,e=this._rendererData;e.horzLine.visible=t&&this._source.horzLineVisible(i),e.vertLine.visible=t&&this._source.vertLineVisible(),e.horzLine.lineWidth=s.horzLine.width,e.horzLine.lineStyle=s.horzLine.style,e.horzLine.color=s.horzLine.color,e.vertLine.lineWidth=s.vertLine.width,e.vertLine.lineStyle=s.vertLine.style,e.vertLine.color=s.vertLine.color,e.x=this._source.appliedX(),e.y=this._source.appliedY()}}function V(t,i,s,e,h,n){t.fillRect(i+n,s,e-2*n,n),t.fillRect(i+n,s+h-n,e-2*n,n),t.fillRect(i,s,n,h),t.fillRect(i+e-n,s,n,h)}function F(t,i,s,e,h,n){t.save(),t.globalCompositeOperation="copy",t.fillStyle=n,t.fillRect(i,s,e,h),t.restore()}function W(t,i){return Array.isArray(t)?t.map((t=>0===t?t:t+i)):t+i}function j(t,i,s,e,h,n){let r,o,l,a;if(Array.isArray(n))if(2===n.length){const t=Math.max(0,n[0]),i=Math.max(0,n[1]);r=t,o=t,l=i,a=i}else{if(4!==n.length)throw new Error("Wrong border radius - it should be like css border radius");r=Math.max(0,n[0]),o=Math.max(0,n[1]),l=Math.max(0,n[2]),a=Math.max(0,n[3])}else{const t=Math.max(0,n);r=t,o=t,l=t,a=t}t.beginPath(),t.moveTo(i+r,s),t.lineTo(i+e-o,s),0!==o&&t.arcTo(i+e,s,i+e,s+o,o),t.lineTo(i+e,s+h-l),0!==l&&t.arcTo(i+e,s+h,i+e-l,s+h,l),t.lineTo(i+a,s+h),0!==a&&t.arcTo(i,s+h,i,s+h-a,a),t.lineTo(i,s+r),0!==r&&t.arcTo(i,s,i+r,s,r)}function H(t,i,s,e,h,n,r=0,o=0,l=""){if(t.save(),!r||!l||l===n)return j(t,i,s,e,h,o),t.fillStyle=n,t.fill(),void t.restore();const a=r/2;if("transparent"!==n){j(t,i+r,s+r,e-2*r,h-2*r,W(o,-r)),t.fillStyle=n,t.fill()}if("transparent"!==l){j(t,i+a,s+a,e-r,h-r,W(o,-a)),t.lineWidth=r,t.strokeStyle=l,t.closePath(),t.stroke()}t.restore()}function $(t,i,s,e,h,n,r){t.save(),t.globalCompositeOperation="copy";const o=t.createLinearGradient(0,0,0,h);o.addColorStop(0,n),o.addColorStop(1,r),t.fillStyle=o,t.fillRect(i,s,e,h),t.restore()}class U{constructor(t,i){this.setData(t,i)}setData(t,i){this._data=t,this._commonData=i}height(t,i){return this._data.visible?t.fontSize+t.paddingTop+t.paddingBottom:0}draw(t,i,s,e){if(!this._data.visible||0===this._data.text.length)return;const h=this._data.color,n=this._commonData.background,r=t.useBitmapCoordinateSpace((t=>{const r=t.context;r.font=i.font;const o=this._calculateGeometry(t,i,s,e),l=o.bitmap,a=(t,i)=>{o.alignRight?H(r,l.xOutside,l.yTop,l.totalWidth,l.totalHeight,t,l.horzBorder,[l.radius,0,0,l.radius],i):H(r,l.xInside,l.yTop,l.totalWidth,l.totalHeight,t,l.horzBorder,[0,l.radius,l.radius,0],i)};return a(n,"transparent"),this._data.tickVisible&&(r.fillStyle=h,r.fillRect(l.xInside,l.yMid,l.xTick-l.xInside,l.tickHeight)),a("transparent",n),this._data.borderVisible&&(r.fillStyle=i.paneBackgroundColor,r.fillRect(o.alignRight?l.right-l.horzBorder:0,l.yTop,l.horzBorder,l.yBottom-l.yTop)),o}));t.useMediaCoordinateSpace((({context:t})=>{const s=r.media;t.font=i.font,t.textAlign=r.alignRight?"right":"left",t.textBaseline="middle",t.fillStyle=h,t.fillText(this._data.text,s.xText,(s.yTop+s.yBottom)/2+s.textMidCorrection)}))}_calculateGeometry(t,i,s,e){var h;const{context:n,bitmapSize:r,mediaSize:o,horizontalPixelRatio:l,verticalPixelRatio:a}=t,u=this._data.tickVisible||!this._data.moveTextToInvisibleTick?i.tickLength:0,c=this._data.separatorVisible?i.borderSize:0,d=i.paddingTop+this._commonData.additionalPaddingTop,f=i.paddingBottom+this._commonData.additionalPaddingBottom,p=i.paddingInner,m=i.paddingOuter,v=this._data.text,b=i.fontSize,g=s.yMidCorrection(n,v),w=Math.ceil(s.measureText(n,v)),M=b+d+f,S=i.borderSize+p+m+w+u,x=Math.max(1,Math.floor(a));let _=Math.round(M*a);_%2!=x%2&&(_+=1);const y=c>0?Math.max(1,Math.floor(c*l)):0,k=Math.round(S*l),C=Math.round(u*l),T=null!==(h=this._commonData.fixedCoordinate)&&void 0!==h?h:this._commonData.coordinate,P=Math.round(T*a)-Math.floor(.5*a),R=Math.floor(P+x/2-_/2),D=R+_,A="right"===e,E=A?o.width-c:c,O=A?r.width-y:y;let B,L,z;return A?(B=O-k,L=O-C,z=E-u-p-c):(B=O+k,L=O+C,z=E+u+p),{alignRight:A,bitmap:{yTop:R,yMid:P,yBottom:D,totalWidth:k,totalHeight:_,radius:2*l,horzBorder:y,xOutside:B,xInside:O,xTick:L,tickHeight:x,right:r.width},media:{yTop:R/a,yBottom:D/a,xText:z,textMidCorrection:g}}}}class q{constructor(t){this._commonRendererData={coordinate:0,background:"#000",additionalPaddingBottom:0,additionalPaddingTop:0},this._axisRendererData={text:"",visible:!1,tickVisible:!0,moveTextToInvisibleTick:!1,borderColor:"",color:"#FFF",borderVisible:!1,separatorVisible:!1},this._paneRendererData={text:"",visible:!1,tickVisible:!1,moveTextToInvisibleTick:!0,borderColor:"",color:"#FFF",borderVisible:!0,separatorVisible:!0},this._invalidated=!0,this._axisRenderer=new(t||U)(this._axisRendererData,this._commonRendererData),this._paneRenderer=new(t||U)(this._paneRendererData,this._commonRendererData)}text(){return this._updateRendererDataIfNeeded(),this._axisRendererData.text}coordinate(){return this._updateRendererDataIfNeeded(),this._commonRendererData.coordinate}update(){this._invalidated=!0}height(t,i=!1){return Math.max(this._axisRenderer.height(t,i),this._paneRenderer.height(t,i))}getFixedCoordinate(){return this._commonRendererData.fixedCoordinate||0}setFixedCoordinate(t){this._commonRendererData.fixedCoordinate=t}isVisible(){return this._updateRendererDataIfNeeded(),this._axisRendererData.visible||this._paneRendererData.visible}isAxisLabelVisible(){return this._updateRendererDataIfNeeded(),this._axisRendererData.visible}renderer(t){return this._updateRendererDataIfNeeded(),this._axisRendererData.tickVisible=this._axisRendererData.tickVisible&&t.options().ticksVisible,this._paneRendererData.tickVisible=this._paneRendererData.tickVisible&&t.options().ticksVisible,this._axisRenderer.setData(this._axisRendererData,this._commonRendererData),this._paneRenderer.setData(this._paneRendererData,this._commonRendererData),this._axisRenderer}paneRenderer(){return this._updateRendererDataIfNeeded(),this._axisRenderer.setData(this._axisRendererData,this._commonRendererData),this._paneRenderer.setData(this._paneRendererData,this._commonRendererData),this._paneRenderer}_updateRendererDataIfNeeded(){this._invalidated&&(this._axisRendererData.tickVisible=!0,this._paneRendererData.tickVisible=!1,this._updateRendererData(this._axisRendererData,this._paneRendererData,this._commonRendererData))}}class Y extends q{constructor(t,i,s){super(),this._source=t,this._priceScale=i,this._valueProvider=s}_updateRendererData(t,i,s){t.visible=!1;const e=this._source.options().horzLine;if(!e.labelVisible)return;const h=this._priceScale.firstValue();if(!this._source.visible()||this._priceScale.isEmpty()||null===h)return;const n=v(e.labelBackgroundColor);s.background=n.background,t.color=n.foreground;const r=2/12*this._priceScale.fontSize();s.additionalPaddingTop=r,s.additionalPaddingBottom=r;const o=this._valueProvider(this._priceScale);s.coordinate=o.coordinate,t.text=this._priceScale.formatPrice(o.price,h),t.visible=!0}}const X=/[1-9]/g;class Z{constructor(){this._data=null}setData(t){this._data=t}draw(t,i){if(null===this._data||!1===this._data.visible||0===this._data.text.length)return;const s=t.useMediaCoordinateSpace((({context:t})=>(t.font=i.font,Math.round(i.widthCache.measureText(t,r(this._data).text,X)))));if(s<=0)return;const e=i.paddingHorizontal,h=s+2*e,n=h/2,o=this._data.width;let l=this._data.coordinate,a=Math.floor(l-n)+.5;a<0?(l+=Math.abs(0-a),a=Math.floor(l-n)+.5):a+h>o&&(l-=Math.abs(o-(a+h)),a=Math.floor(l-n)+.5);const u=a+h,c=Math.ceil(0+i.borderSize+i.tickLength+i.paddingTop+i.fontSize+i.paddingBottom);t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:s,verticalPixelRatio:e})=>{const h=r(this._data);t.fillStyle=h.background;const n=Math.round(a*s),o=Math.round(0*e),l=Math.round(u*s),d=Math.round(c*e),f=Math.round(2*s);if(t.beginPath(),t.moveTo(n,o),t.lineTo(n,d-f),t.arcTo(n,d,n+f,d,f),t.lineTo(l-f,d),t.arcTo(l,d,l,d-f,f),t.lineTo(l,o),t.fill(),h.tickVisible){const n=Math.round(h.coordinate*s),r=o,l=Math.round((r+i.tickLength)*e);t.fillStyle=h.color;const a=Math.max(1,Math.floor(s)),u=Math.floor(.5*s);t.fillRect(n-u,r,a,l-r)}})),t.useMediaCoordinateSpace((({context:t})=>{const s=r(this._data),h=0+i.borderSize+i.tickLength+i.paddingTop+i.fontSize/2;t.font=i.font,t.textAlign="left",t.textBaseline="middle",t.fillStyle=s.color;const n=i.widthCache.yMidCorrection(t,"Apr0");t.translate(a+e,h+n),t.fillText(s.text,0,0)}))}}class K{constructor(t,i,s){this._invalidated=!0,this._renderer=new Z,this._rendererData={visible:!1,background:"#4c525e",color:"white",text:"",width:0,coordinate:NaN,tickVisible:!0},this._crosshair=t,this._model=i,this._valueProvider=s}update(){this._invalidated=!0}renderer(){return this._invalidated&&(this._updateImpl(),this._invalidated=!1),this._renderer.setData(this._rendererData),this._renderer}_updateImpl(){const t=this._rendererData;t.visible=!1;const i=this._crosshair.options().vertLine;if(!i.labelVisible)return;const s=this._model.timeScale();if(s.isEmpty())return;t.width=s.width();const e=this._valueProvider();if(null===e)return;t.coordinate=e.coordinate;const h=s.indexToTimeScalePoint(this._crosshair.appliedIndex());t.text=s.formatDateTime(r(h)),t.visible=!0;const n=v(i.labelBackgroundColor);t.background=n.background,t.color=n.foreground,t.tickVisible=s.options().ticksVisible}}class G{constructor(){this._priceScale=null,this._zorder=0}zorder(){return this._zorder}setZorder(t){this._zorder=t}priceScale(){return this._priceScale}setPriceScale(t){this._priceScale=t}labelPaneViews(t){return[]}timeAxisViews(){return[]}visible(){return!0}}var J,Q,tt,it;!function(t){t[t.Normal=0]="Normal",t[t.Magnet=1]="Magnet"}(J||(J={}));class st extends G{constructor(t,i){super(),this._pane=null,this._price=NaN,this._index=0,this._visible=!0,this._priceAxisViews=new Map,this._subscribed=!1,this._x=NaN,this._y=NaN,this._originX=NaN,this._originY=NaN,this._model=t,this._options=i,this._markersPaneView=new L(t,this);this._currentPosPriceProvider=((t,i)=>s=>{const e=i(),h=t();if(s===r(this._pane).defaultPriceScale())return{price:h,coordinate:e};{const t=r(s.firstValue());return{price:s.coordinateToPrice(e,t),coordinate:e}}})((()=>this._price),(()=>this._y));const s=((t,i)=>()=>{const s=this._model.timeScale().indexToTime(t()),e=i();return s&&Number.isFinite(e)?{time:s,coordinate:e}:null})((()=>this._index),(()=>this.appliedX()));this._timeAxisView=new K(this,t,s),this._paneView=new N(this)}options(){return this._options}saveOriginCoord(t,i){this._originX=t,this._originY=i}clearOriginCoord(){this._originX=NaN,this._originY=NaN}originCoordX(){return this._originX}originCoordY(){return this._originY}setPosition(t,i,s){this._subscribed||(this._subscribed=!0),this._visible=!0,this._tryToUpdateViews(t,i,s)}appliedIndex(){return this._index}appliedX(){return this._x}appliedY(){return this._y}visible(){return this._visible}clearPosition(){this._visible=!1,this._setIndexToLastSeriesBarIndex(),this._price=NaN,this._x=NaN,this._y=NaN,this._pane=null,this.clearOriginCoord()}paneViews(t){return null!==this._pane?[this._paneView,this._markersPaneView]:[]}horzLineVisible(t){return t===this._pane&&this._options.horzLine.visible}vertLineVisible(){return this._options.vertLine.visible}priceAxisViews(t,i){this._visible&&this._pane===t||this._priceAxisViews.clear();const s=[];return this._pane===t&&s.push(this._createPriceAxisViewOnDemand(this._priceAxisViews,i,this._currentPosPriceProvider)),s}timeAxisViews(){return this._visible?[this._timeAxisView]:[]}pane(){return this._pane}updateAllViews(){this._paneView.update(),this._priceAxisViews.forEach((t=>t.update())),this._timeAxisView.update(),this._markersPaneView.update()}_priceScaleByPane(t){return t&&!t.defaultPriceScale().isEmpty()?t.defaultPriceScale():null}_tryToUpdateViews(t,i,s){this._tryToUpdateData(t,i,s)&&this.updateAllViews()}_tryToUpdateData(t,i,s){const e=this._x,h=this._y,n=this._price,r=this._index,o=this._pane,l=this._priceScaleByPane(s);this._index=t,this._x=isNaN(t)?NaN:this._model.timeScale().indexToCoordinate(t),this._pane=s;const a=null!==l?l.firstValue():null;return null!==l&&null!==a?(this._price=i,this._y=l.priceToCoordinate(i,a)):(this._price=NaN,this._y=NaN),e!==this._x||h!==this._y||r!==this._index||n!==this._price||o!==this._pane}_setIndexToLastSeriesBarIndex(){const t=this._model.serieses().map((t=>t.bars().lastIndex())).filter(y),i=0===t.length?null:Math.max(...t);this._index=null!==i?i:NaN}_createPriceAxisViewOnDemand(t,i,s){let e=t.get(i);return void 0===e&&(e=new Y(this,i,s),t.set(i,e)),e}}function et(t){return"left"===t||"right"===t}!function(t){t.Left="left",t.Right="right"}(Q||(Q={})),function(t){t[t.None=0]="None",t[t.Cursor=1]="Cursor",t[t.Light=2]="Light",t[t.Full=3]="Full"}(tt||(tt={})),function(t){t[t.FitContent=0]="FitContent",t[t.ApplyRange=1]="ApplyRange",t[t.ApplyBarSpacing=2]="ApplyBarSpacing",t[t.ApplyRightOffset=3]="ApplyRightOffset",t[t.Reset=4]="Reset",t[t.Animation=5]="Animation",t[t.StopAnimation=6]="StopAnimation"}(it||(it={}));class ht{constructor(t){this._invalidatedPanes=new Map,this._timeScaleInvalidations=[],this._globalLevel=t}invalidatePane(t,i){const s=function(t,i){return void 0===t?i:{level:Math.max(t.level,i.level),autoScale:t.autoScale||i.autoScale}}(this._invalidatedPanes.get(t),i);this._invalidatedPanes.set(t,s)}fullInvalidation(){return this._globalLevel}invalidateForPane(t){const i=this._invalidatedPanes.get(t);return void 0===i?{level:this._globalLevel}:{level:Math.max(this._globalLevel,i.level),autoScale:i.autoScale}}setFitContent(){this.stopTimeScaleAnimation(),this._timeScaleInvalidations=[{type:0}]}applyRange(t){this.stopTimeScaleAnimation(),this._timeScaleInvalidations=[{type:1,value:t}]}setTimeScaleAnimation(t){this._removeTimeScaleAnimation(),this._timeScaleInvalidations.push({type:5,value:t})}stopTimeScaleAnimation(){this._removeTimeScaleAnimation(),this._timeScaleInvalidations.push({type:6})}resetTimeScale(){this.stopTimeScaleAnimation(),this._timeScaleInvalidations=[{type:4}]}setBarSpacing(t){this.stopTimeScaleAnimation(),this._timeScaleInvalidations.push({type:2,value:t})}setRightOffset(t){this.stopTimeScaleAnimation(),this._timeScaleInvalidations.push({type:3,value:t})}timeScaleInvalidations(){return this._timeScaleInvalidations}merge(t){for(const i of t._timeScaleInvalidations)this._applyTimeScaleInvalidation(i);this._globalLevel=Math.max(this._globalLevel,t._globalLevel),t._invalidatedPanes.forEach(((t,i)=>{this.invalidatePane(i,t)}))}static light(){return new ht(2)}static full(){return new ht(3)}_applyTimeScaleInvalidation(t){switch(t.type){case 0:this.setFitContent();break;case 1:this.applyRange(t.value);break;case 2:this.setBarSpacing(t.value);break;case 3:this.setRightOffset(t.value);break;case 4:this.resetTimeScale();break;case 5:this.setTimeScaleAnimation(t.value);break;case 6:this._removeTimeScaleAnimation()}}_removeTimeScaleAnimation(){const t=this._timeScaleInvalidations.findIndex((t=>5===t.type));-1!==t&&this._timeScaleInvalidations.splice(t,1)}}const nt=".";function rt(t,i){if(!w(t))return"n/a";if(!M(i))throw new TypeError("invalid length");if(i<0||i>16)throw new TypeError("invalid length");if(0===i)return t.toString();return("0000000000000000"+t.toString()).slice(-i)}class ot{constructor(t,i){if(i||(i=1),w(t)&&M(t)||(t=100),t<0)throw new TypeError("invalid base");this._priceScale=t,this._minMove=i,this._calculateDecimal()}format(t){const i=t<0?"−":"";return t=Math.abs(t),i+this._formatAsDecimal(t)}_calculateDecimal(){if(this._fractionalLength=0,this._priceScale>0&&this._minMove>0){let t=this._priceScale;for(;t>1;)t/=10,this._fractionalLength++}}_formatAsDecimal(t){const i=this._priceScale/this._minMove;let s=Math.floor(t),e="";const h=void 0!==this._fractionalLength?this._fractionalLength:NaN;if(i>1){let n=+(Math.round(t*i)-s*i).toFixed(this._fractionalLength);n>=i&&(n-=i,s+=1),e=nt+rt(+n.toFixed(this._fractionalLength)*this._minMove,h)}else s=Math.round(s*i)/i,h>0&&(e=nt+rt(0,h));return s.toFixed(0)+e}}class lt extends ot{constructor(t=100){super(t)}format(t){return`${super.format(t)}%`}}class at{constructor(t){this._precision=t}format(t){let i="";return t<0&&(i="-",t=-t),t<995?i+this._formatNumber(t):t<999995?i+this._formatNumber(t/1e3)+"K":t<999999995?(t=1e3*Math.round(t/1e3),i+this._formatNumber(t/1e6)+"M"):(t=1e6*Math.round(t/1e6),i+this._formatNumber(t/1e9)+"B")}_formatNumber(t){let i;const s=Math.pow(10,this._precision);return i=(t=Math.round(t*s)/s)>=1e-15&&t<1?t.toFixed(this._precision).replace(/\.?0+$/,""):String(t),i.replace(/(\.[1-9]*)0+$/,((t,i)=>i))}}function ut(t,i,s,e,h,n,r){if(0===i.length||e.from>=i.length||e.to<=0)return;const o=t.context,l=i[e.from];let a=n(t,l),u=l;if(e.to-e.from<2){const t=h/2;o.beginPath();const i={x:l.x-t,y:l.y},s={x:l.x+t,y:l.y};return o.moveTo(i.x,i.y),o.lineTo(s.x,s.y),void r(o,a,i,s)}const c=(t,i)=>{r(o,a,u,i),o.beginPath(),a=t,u=i};let d=u;o.beginPath(),o.moveTo(l.x,l.y);for(let h=e.from+1;h<e.to;++h){d=i[h];const e=n(t,d);switch(s){case 0:o.lineTo(d.x,d.y);break;case 1:o.lineTo(d.x,i[h-1].y),e!==a&&(c(e,d),o.lineTo(d.x,i[h-1].y)),o.lineTo(d.x,d.y);break;case 2:{const[t,s]=pt(i,h-1,h);o.bezierCurveTo(t.x,t.y,s.x,s.y,d.x,d.y);break}}1!==s&&e!==a&&(c(e,d),o.moveTo(d.x,d.y))}(u!==d||u===d&&1===s)&&r(o,a,u,d)}const ct=6;function dt(t,i){return{x:t.x-i.x,y:t.y-i.y}}function ft(t,i){return{x:t.x/i,y:t.y/i}}function pt(t,i,s){const e=Math.max(0,i-1),h=Math.min(t.length-1,s+1);var n,r;return[(n=t[i],r=ft(dt(t[s],t[e]),ct),{x:n.x+r.x,y:n.y+r.y}),dt(t[s],ft(dt(t[h],t[i]),ct))]}function mt(t,i,s,e,h){i.lineTo(h.x,t),i.lineTo(e.x,t),i.closePath(),i.fillStyle=s,i.fill()}class vt extends A{constructor(){super(...arguments),this._data=null}setData(t){this._data=t}_drawImpl(t){var i;if(null===this._data)return;const{items:e,visibleRange:h,barWidth:n,lineWidth:r,lineStyle:o,lineType:l}=this._data,a=null!==(i=this._data.baseLevelCoordinate)&&void 0!==i?i:this._data.invertFilledArea?0:t.mediaSize.height;if(null===h)return;const u=t.context;u.lineCap="butt",u.lineJoin="round",u.lineWidth=r,s(u,o),u.lineWidth=1,ut(t,e,l,h,n,this._fillStyle.bind(this),mt.bind(null,a))}}class bt extends vt{constructor(){super(...arguments),this._fillCache=null}_fillStyle(t,i){const{context:s,mediaSize:e}=t,{topColor:h,bottomColor:n}=i,r=e.height;if(null!==this._fillCache&&this._fillCache.topColor===h&&this._fillCache.bottomColor===n&&this._fillCache.bottom===r)return this._fillCache.fillStyle;const o=s.createLinearGradient(0,0,0,r);return o.addColorStop(0,h),o.addColorStop(1,n),this._fillCache={topColor:h,bottomColor:n,fillStyle:o,bottom:r},o}}function gt(t,i){t.strokeStyle=i,t.stroke()}class wt extends A{constructor(){super(...arguments),this._data=null}setData(t){this._data=t}_drawImpl(t){if(null===this._data)return;const{items:i,visibleRange:e,barWidth:h,lineType:n,lineWidth:r,lineStyle:o}=this._data;if(null===e)return;const l=t.context;l.lineCap="butt",l.lineWidth=r,s(l,o),l.lineJoin="round",ut(t,i,n,e,h,this._strokeStyle.bind(this),gt)}}class Mt extends wt{_strokeStyle(t,i){return i.lineColor}}function St(t,i,s,e=0,h=t.length){let n=h-e;for(;0<n;){const h=n>>1,r=e+h;s(t[r],i)?(e=r+1,n-=h+1):n=h}return e}function xt(t,i,s,e=0,h=t.length){let n=h-e;for(;0<n;){const h=n>>1,r=e+h;s(i,t[r])?n=h:(e=r+1,n-=h+1)}return e}var _t,yt;function kt(t,i){return t.time<i}function Ct(t,i){return t<i.time}function Tt(t,i,s){const e=i.left(),h=i.right(),n=St(t,e,kt),r=xt(t,h,Ct);if(!s)return{from:n,to:r};let o=n,l=r;return n>0&&n<t.length&&t[n].time>=e&&(o=n-1),r>0&&r<t.length&&t[r-1].time<=h&&(l=r+1),{from:o,to:l}}!function(t){t[t.LessThanSecond=0]="LessThanSecond",t[t.Second=10]="Second",t[t.Minute1=20]="Minute1",t[t.Minute5=21]="Minute5",t[t.Minute30=22]="Minute30",t[t.Hour1=30]="Hour1",t[t.Hour3=31]="Hour3",t[t.Hour6=32]="Hour6",t[t.Hour12=33]="Hour12",t[t.Day=50]="Day",t[t.Month=60]="Month",t[t.Year=70]="Year"}(_t||(_t={}));class Pt{constructor(t,i,s){this._invalidated=!0,this._dataInvalidated=!0,this._optionsInvalidated=!0,this._items=[],this._itemsVisibleRange=null,this._series=t,this._model=i,this._extendedVisibleRange=s}update(t){this._invalidated=!0,"data"===t&&(this._dataInvalidated=!0),"options"===t&&(this._optionsInvalidated=!0)}renderer(){return this._series.visible()?(this._makeValid(),null===this._itemsVisibleRange?null:this._renderer):null}_updateOptions(){this._items=this._items.map((t=>Object.assign(Object.assign({},t),this._series.barColorer().barStyle(t.time))))}_clearVisibleRange(){this._itemsVisibleRange=null}_makeValid(){this._dataInvalidated&&(this._fillRawPoints(),this._dataInvalidated=!1),this._optionsInvalidated&&(this._updateOptions(),this._optionsInvalidated=!1),this._invalidated&&(this._makeValidImpl(),this._invalidated=!1)}_makeValidImpl(){const t=this._series.priceScale(),i=this._model.timeScale();if(this._clearVisibleRange(),i.isEmpty()||t.isEmpty())return;const s=i.visibleStrictRange();if(null===s)return;if(0===this._series.bars().size())return;const e=this._series.firstValue();null!==e&&(this._itemsVisibleRange=Tt(this._items,s,this._extendedVisibleRange),this._convertToCoordinates(t,i,e.value),this._prepareRendererData())}}class Rt extends Pt{constructor(t,i){super(t,i,!0)}_convertToCoordinates(t,i,s){i.indexesToCoordinates(this._items,k(this._itemsVisibleRange)),t.pointsArrayToCoordinates(this._items,s,k(this._itemsVisibleRange))}_createRawItemBase(t,i){return{time:t,price:i,x:NaN,y:NaN}}_fillRawPoints(){const t=this._series.barColorer();this._items=this._series.bars().rows().map((i=>{const s=i.value[3];return this._createRawItem(i.index,s,t)}))}}class Dt extends Rt{constructor(t,i){super(t,i),this._renderer=new D,this._areaRenderer=new bt,this._lineRenderer=new Mt,this._renderer.setRenderers([this._areaRenderer,this._lineRenderer])}_createRawItem(t,i,s){return Object.assign(Object.assign({},this._createRawItemBase(t,i)),s.barStyle(t))}_prepareRendererData(){const t=this._series.options();this._areaRenderer.setData({lineType:t.lineType,items:this._items,lineStyle:t.lineStyle,lineWidth:t.lineWidth,baseLevelCoordinate:null,invertFilledArea:t.invertFilledArea,visibleRange:this._itemsVisibleRange,barWidth:this._model.timeScale().barSpacing()}),this._lineRenderer.setData({lineType:t.lineType,items:this._items,lineStyle:t.lineStyle,lineWidth:t.lineWidth,visibleRange:this._itemsVisibleRange,barWidth:this._model.timeScale().barSpacing()})}}class At extends z{constructor(){super(...arguments),this._data=null,this._barWidth=0,this._barLineWidth=0}setData(t){this._data=t}_drawImpl({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this._data||0===this._data.bars.length||null===this._data.visibleRange)return;if(this._barWidth=this._calcBarWidth(i),this._barWidth>=2){Math.max(1,Math.floor(i))%2!=this._barWidth%2&&this._barWidth--}this._barLineWidth=this._data.thinBars?Math.min(this._barWidth,Math.floor(i)):this._barWidth;let e=null;const h=this._barLineWidth<=this._barWidth&&this._data.barSpacing>=Math.floor(1.5*i);for(let n=this._data.visibleRange.from;n<this._data.visibleRange.to;++n){const r=this._data.bars[n];e!==r.barColor&&(t.fillStyle=r.barColor,e=r.barColor);const o=Math.floor(.5*this._barLineWidth),l=Math.round(r.x*i),a=l-o,u=this._barLineWidth,c=a+u-1,d=Math.min(r.highY,r.lowY),f=Math.max(r.highY,r.lowY),p=Math.round(d*s)-o,m=Math.round(f*s)+o,v=Math.max(m-p,this._barLineWidth);t.fillRect(a,p,u,v);const b=Math.ceil(1.5*this._barWidth);if(h){if(this._data.openVisible){const i=l-b;let e=Math.max(p,Math.round(r.openY*s)-o),h=e+u-1;h>p+v-1&&(h=p+v-1,e=h-u+1),t.fillRect(i,e,a-i,h-e+1)}const i=l+b;let e=Math.max(p,Math.round(r.closeY*s)-o),h=e+u-1;h>p+v-1&&(h=p+v-1,e=h-u+1),t.fillRect(c+1,e,i-c,h-e+1)}}}_calcBarWidth(t){const i=Math.floor(t);return Math.max(i,Math.floor(function(t,i){return Math.floor(.3*t*i)}(r(this._data).barSpacing,t)))}}class Et extends Pt{constructor(t,i){super(t,i,!1)}_convertToCoordinates(t,i,s){i.indexesToCoordinates(this._items,k(this._itemsVisibleRange)),t.barPricesToCoordinates(this._items,s,k(this._itemsVisibleRange))}_createDefaultItem(t,i,s){return{time:t,open:i.value[0],high:i.value[1],low:i.value[2],close:i.value[3],x:NaN,openY:NaN,highY:NaN,lowY:NaN,closeY:NaN}}_fillRawPoints(){const t=this._series.barColorer();this._items=this._series.bars().rows().map((i=>this._createRawItem(i.index,i,t)))}}class Ot extends Et{constructor(){super(...arguments),this._renderer=new At}_createRawItem(t,i,s){return Object.assign(Object.assign({},this._createDefaultItem(t,i,s)),s.barStyle(t))}_prepareRendererData(){const t=this._series.options();this._renderer.setData({bars:this._items,barSpacing:this._model.timeScale().barSpacing(),openVisible:t.openVisible,thinBars:t.thinBars,visibleRange:this._itemsVisibleRange})}}function Bt(t,i,s){return Math.min(Math.max(t,i),s)}function Lt(t,i,s){return i-t<=s}function zt(t){return t<=0?NaN:Math.log(t)/Math.log(10)}function It(t){const i=Math.ceil(t);return i%2==0?i-1:i}class Nt extends vt{constructor(){super(...arguments),this._fillCache=null}_fillStyle(t,i){var s;const{context:e,mediaSize:h}=t,n=this._data,{topFillColor1:r,topFillColor2:o,bottomFillColor1:l,bottomFillColor2:a}=i,u=null!==(s=n.baseLevelCoordinate)&&void 0!==s?s:h.height,c=h.height;if(null!==this._fillCache&&this._fillCache.topFillColor1===r&&this._fillCache.topFillColor2===o&&this._fillCache.bottomFillColor1===l&&this._fillCache.bottomFillColor2===a&&this._fillCache.baseLevelCoordinate===u&&this._fillCache.bottom===c)return this._fillCache.fillStyle;const d=e.createLinearGradient(0,0,0,c),f=Bt(u/c,0,1);return d.addColorStop(0,r),d.addColorStop(f,o),d.addColorStop(f,l),d.addColorStop(1,a),this._fillCache={topFillColor1:r,topFillColor2:o,bottomFillColor1:l,bottomFillColor2:a,fillStyle:d,baseLevelCoordinate:u,bottom:c},d}}class Vt extends wt{constructor(){super(...arguments),this._strokeCache=null}_strokeStyle(t,i){const{context:s,mediaSize:e}=t,h=this._data,{topLineColor:n,bottomLineColor:r}=i,{baseLevelCoordinate:o}=h,l=e.height;if(null!==this._strokeCache&&this._strokeCache.topLineColor===n&&this._strokeCache.bottomLineColor===r&&this._strokeCache.baseLevelCoordinate===o&&this._strokeCache.bottom===l)return this._strokeCache.strokeStyle;const a=s.createLinearGradient(0,0,0,l),u=Bt(o/l,0,1);return a.addColorStop(0,n),a.addColorStop(u,n),a.addColorStop(u,r),a.addColorStop(1,r),this._strokeCache={topLineColor:n,bottomLineColor:r,strokeStyle:a,baseLevelCoordinate:o,bottom:l},a}}class Ft extends Rt{constructor(t,i){super(t,i),this._renderer=new D,this._baselineAreaRenderer=new Nt,this._baselineLineRenderer=new Vt,this._renderer.setRenderers([this._baselineAreaRenderer,this._baselineLineRenderer])}_createRawItem(t,i,s){return Object.assign(Object.assign({},this._createRawItemBase(t,i)),s.barStyle(t))}_prepareRendererData(){const t=this._series.firstValue();if(null===t)return;const i=this._series.options(),s=this._series.priceScale().priceToCoordinate(i.baseValue.price,t.value),e=this._model.timeScale().barSpacing();this._baselineAreaRenderer.setData({items:this._items,lineWidth:i.lineWidth,lineStyle:i.lineStyle,lineType:i.lineType,baseLevelCoordinate:s,invertFilledArea:!1,visibleRange:this._itemsVisibleRange,barWidth:e}),this._baselineLineRenderer.setData({items:this._items,lineWidth:i.lineWidth,lineStyle:i.lineStyle,lineType:i.lineType,baseLevelCoordinate:s,visibleRange:this._itemsVisibleRange,barWidth:e})}}!function(t){t[t.BarBorderWidth=1]="BarBorderWidth"}(yt||(yt={}));class Wt extends z{constructor(){super(...arguments),this._data=null,this._barWidth=0}setData(t){this._data=t}_drawImpl(t){if(null===this._data||0===this._data.bars.length||null===this._data.visibleRange)return;const{horizontalPixelRatio:i}=t;if(this._barWidth=function(t,i){if(t>=2.5&&t<=4)return Math.floor(3*i);const s=1-.2*Math.atan(Math.max(4,t)-4)/(.5*Math.PI),e=Math.floor(t*s*i),h=Math.floor(t*i),n=Math.min(e,h);return Math.max(Math.floor(i),n)}(this._data.barSpacing,i),this._barWidth>=2){Math.floor(i)%2!=this._barWidth%2&&this._barWidth--}const s=this._data.bars;this._data.wickVisible&&this._drawWicks(t,s,this._data.visibleRange),this._data.borderVisible&&this._drawBorder(t,s,this._data.visibleRange);const e=this._calculateBorderWidth(i);(!this._data.borderVisible||this._barWidth>2*e)&&this._drawCandles(t,s,this._data.visibleRange)}_drawWicks(t,i,s){if(null===this._data)return;const{context:e,horizontalPixelRatio:h,verticalPixelRatio:n}=t;let r="",o=Math.min(Math.floor(h),Math.floor(this._data.barSpacing*h));o=Math.max(Math.floor(h),Math.min(o,this._barWidth));const l=Math.floor(.5*o);let a=null;for(let t=s.from;t<s.to;t++){const s=i[t];s.barWickColor!==r&&(e.fillStyle=s.barWickColor,r=s.barWickColor);const u=Math.round(Math.min(s.openY,s.closeY)*n),c=Math.round(Math.max(s.openY,s.closeY)*n),d=Math.round(s.highY*n),f=Math.round(s.lowY*n);let p=Math.round(h*s.x)-l;const m=p+o-1;null!==a&&(p=Math.max(a+1,p),p=Math.min(p,m));const v=m-p+1;e.fillRect(p,d,v,u-d),e.fillRect(p,c+1,v,f-c),a=m}}_calculateBorderWidth(t){let i=Math.floor(1*t);this._barWidth<=2*i&&(i=Math.floor(.5*(this._barWidth-1)));const s=Math.max(Math.floor(t),i);return this._barWidth<=2*s?Math.max(Math.floor(t),Math.floor(1*t)):s}_drawBorder(t,i,s){if(null===this._data)return;const{context:e,horizontalPixelRatio:h,verticalPixelRatio:n}=t;let r="";const o=this._calculateBorderWidth(h);let l=null;for(let t=s.from;t<s.to;t++){const s=i[t];s.barBorderColor!==r&&(e.fillStyle=s.barBorderColor,r=s.barBorderColor);let a=Math.round(s.x*h)-Math.floor(.5*this._barWidth);const u=a+this._barWidth-1,c=Math.round(Math.min(s.openY,s.closeY)*n),d=Math.round(Math.max(s.openY,s.closeY)*n);if(null!==l&&(a=Math.max(l+1,a),a=Math.min(a,u)),this._data.barSpacing*h>2*o)V(e,a,c,u-a+1,d-c+1,o);else{const t=u-a+1;e.fillRect(a,c,t,d-c+1)}l=u}}_drawCandles(t,i,s){if(null===this._data)return;const{context:e,horizontalPixelRatio:h,verticalPixelRatio:n}=t;let r="";const o=this._calculateBorderWidth(h);for(let t=s.from;t<s.to;t++){const s=i[t];let l=Math.round(Math.min(s.openY,s.closeY)*n),a=Math.round(Math.max(s.openY,s.closeY)*n),u=Math.round(s.x*h)-Math.floor(.5*this._barWidth),c=u+this._barWidth-1;if(s.barColor!==r){const t=s.barColor;e.fillStyle=t,r=t}this._data.borderVisible&&(u+=o,l+=o,c-=o,a-=o),l>a||e.fillRect(u,l,c-u+1,a-l+1)}}}class jt extends Et{constructor(){super(...arguments),this._renderer=new Wt}_createRawItem(t,i,s){return Object.assign(Object.assign({},this._createDefaultItem(t,i,s)),s.barStyle(t))}_prepareRendererData(){const t=this._series.options();this._renderer.setData({bars:this._items,barSpacing:this._model.timeScale().barSpacing(),wickVisible:t.wickVisible,borderVisible:t.borderVisible,visibleRange:this._itemsVisibleRange})}}class Ht extends z{constructor(){super(...arguments),this._data=null,this._precalculatedCache=[]}setData(t){this._data=t,this._precalculatedCache=[]}_drawImpl({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(null===this._data||0===this._data.items.length||null===this._data.visibleRange)return;this._precalculatedCache.length||this._fillPrecalculatedCache(i);const e=Math.max(1,Math.floor(s)),h=Math.round(this._data.histogramBase*s)-Math.floor(e/2),n=h+e;for(let i=this._data.visibleRange.from;i<this._data.visibleRange.to;i++){const r=this._data.items[i],o=this._precalculatedCache[i-this._data.visibleRange.from],l=Math.round(r.y*s);let a,u;t.fillStyle=r.barColor,l<=h?(a=l,u=n):(a=h,u=l-Math.floor(e/2)+e),t.fillRect(o.left,a,o.right-o.left+1,u-a)}}_fillPrecalculatedCache(t){if(null===this._data||0===this._data.items.length||null===this._data.visibleRange)return void(this._precalculatedCache=[]);const i=Math.ceil(this._data.barSpacing*t)<=1?0:Math.max(1,Math.floor(t)),s=Math.round(this._data.barSpacing*t)-i;this._precalculatedCache=new Array(this._data.visibleRange.to-this._data.visibleRange.from);for(let i=this._data.visibleRange.from;i<this._data.visibleRange.to;i++){const e=this._data.items[i],h=Math.round(e.x*t);let n,r;if(s%2){const t=(s-1)/2;n=h-t,r=h+t}else{const t=s/2;n=h-t,r=h+t-1}this._precalculatedCache[i-this._data.visibleRange.from]={left:n,right:r,roundedCenter:h,center:e.x*t,time:e.time}}for(let t=this._data.visibleRange.from+1;t<this._data.visibleRange.to;t++){const s=this._precalculatedCache[t-this._data.visibleRange.from],e=this._precalculatedCache[t-this._data.visibleRange.from-1];s.time===e.time+1&&(s.left-e.right!==i+1&&(e.roundedCenter>e.center?e.right=s.left-i-1:s.left=e.right+i+1))}let e=Math.ceil(this._data.barSpacing*t);for(let t=this._data.visibleRange.from;t<this._data.visibleRange.to;t++){const i=this._precalculatedCache[t-this._data.visibleRange.from];i.right<i.left&&(i.right=i.left);const s=i.right-i.left+1;e=Math.min(s,e)}if(i>0&&e<4)for(let t=this._data.visibleRange.from;t<this._data.visibleRange.to;t++){const i=this._precalculatedCache[t-this._data.visibleRange.from];i.right-i.left+1>e&&(i.roundedCenter>i.center?i.right-=1:i.left+=1)}}}class $t extends Rt{constructor(){super(...arguments),this._renderer=new Ht}_createRawItem(t,i,s){return Object.assign(Object.assign({},this._createRawItemBase(t,i)),s.barStyle(t))}_prepareRendererData(){const t={items:this._items,barSpacing:this._model.timeScale().barSpacing(),visibleRange:this._itemsVisibleRange,histogramBase:this._series.priceScale().priceToCoordinate(this._series.options().base,r(this._series.firstValue()).value)};this._renderer.setData(t)}}class Ut extends Rt{constructor(){super(...arguments),this._renderer=new Mt}_createRawItem(t,i,s){return Object.assign(Object.assign({},this._createRawItemBase(t,i)),s.barStyle(t))}_prepareRendererData(){const t=this._series.options(),i={items:this._items,lineStyle:t.lineStyle,lineType:t.lineType,lineWidth:t.lineWidth,visibleRange:this._itemsVisibleRange,barWidth:this._model.timeScale().barSpacing()};this._renderer.setData(i)}}const qt=/[2-9]/g;class Yt{constructor(t=50){this._actualSize=0,this._usageTick=1,this._oldestTick=1,this._tick2Labels={},this._cache=new Map,this._maxSize=t}reset(){this._actualSize=0,this._cache.clear(),this._usageTick=1,this._oldestTick=1,this._tick2Labels={}}measureText(t,i,s){return this._getMetrics(t,i,s).width}yMidCorrection(t,i,s){const e=this._getMetrics(t,i,s);return((e.actualBoundingBoxAscent||0)-(e.actualBoundingBoxDescent||0))/2}_getMetrics(t,i,s){const e=s||qt,h=String(i).replace(e,"0");if(this._cache.has(h))return n(this._cache.get(h)).metrics;if(this._actualSize===this._maxSize){const t=this._tick2Labels[this._oldestTick];delete this._tick2Labels[this._oldestTick],this._cache.delete(t),this._oldestTick++,this._actualSize--}t.save(),t.textBaseline="middle";const r=t.measureText(h);return t.restore(),0===r.width&&i.length||(this._cache.set(h,{metrics:r,tick:this._usageTick}),this._tick2Labels[this._usageTick]=h,this._actualSize++,this._usageTick++),r}}class Xt{constructor(t){this._priceAxisViewRenderer=null,this._rendererOptions=null,this._align="right",this._textWidthCache=t}setParams(t,i,s){this._priceAxisViewRenderer=t,this._rendererOptions=i,this._align=s}draw(t){null!==this._rendererOptions&&null!==this._priceAxisViewRenderer&&this._priceAxisViewRenderer.draw(t,this._rendererOptions,this._textWidthCache,this._align)}}class Zt{constructor(t,i,s){this._priceAxisView=t,this._textWidthCache=new Yt(50),this._dataSource=i,this._chartModel=s,this._fontSize=-1,this._renderer=new Xt(this._textWidthCache)}renderer(){const t=this._chartModel.paneForSource(this._dataSource);if(null===t)return null;const i=t.isOverlay(this._dataSource)?t.defaultVisiblePriceScale():this._dataSource.priceScale();if(null===i)return null;const s=t.priceScalePosition(i);if("overlay"===s)return null;const e=this._chartModel.priceAxisRendererOptions();return e.fontSize!==this._fontSize&&(this._fontSize=e.fontSize,this._textWidthCache.reset()),this._renderer.setParams(this._priceAxisView.paneRenderer(),e,s),this._renderer}}var Kt,Gt;!function(t){t[t.HitTestThreshold=7]="HitTestThreshold"}(Kt||(Kt={}));class Jt extends z{constructor(){super(...arguments),this._data=null}setData(t){this._data=t}hitTest(t,i){var s;if(!(null===(s=this._data)||void 0===s?void 0:s.visible))return null;const{y:e,lineWidth:h,externalId:n}=this._data;return i>=e-h-7&&i<=e+h+7?{hitTestData:this._data,externalId:n}:null}_drawImpl({context:t,bitmapSize:i,horizontalPixelRatio:h,verticalPixelRatio:n}){if(null===this._data)return;if(!1===this._data.visible)return;const r=Math.round(this._data.y*n);r<0||r>i.height||(t.lineCap="butt",t.strokeStyle=this._data.color,t.lineWidth=Math.floor(this._data.lineWidth*h),s(t,this._data.lineStyle),e(t,r,0,i.width))}}class Qt{constructor(t){this._lineRendererData={y:0,color:"rgba(0, 0, 0, 0)",lineWidth:1,lineStyle:0,visible:!1},this._lineRenderer=new Jt,this._invalidated=!0,this._series=t,this._model=t.model(),this._lineRenderer.setData(this._lineRendererData)}update(){this._invalidated=!0}renderer(){return this._series.visible()?(this._invalidated&&(this._updateImpl(),this._invalidated=!1),this._lineRenderer):null}}class ti extends Qt{constructor(t){super(t)}_updateImpl(){this._lineRendererData.visible=!1;const t=this._series.priceScale(),i=t.mode().mode;if(2!==i&&3!==i)return;const s=this._series.options();if(!s.baseLineVisible||!this._series.visible())return;const e=this._series.firstValue();null!==e&&(this._lineRendererData.visible=!0,this._lineRendererData.y=t.priceToCoordinate(e.value,e.value),this._lineRendererData.color=s.baseLineColor,this._lineRendererData.lineWidth=s.baseLineWidth,this._lineRendererData.lineStyle=s.baseLineStyle)}}class ii extends z{constructor(){super(...arguments),this._data=null}setData(t){this._data=t}data(){return this._data}_drawImpl({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){const e=this._data;if(null===e)return;const h=Math.max(1,Math.floor(i)),n=h%2/2,r=Math.round(e.center.x*i)+n,o=e.center.y*s;t.fillStyle=e.seriesLineColor,t.beginPath();const l=Math.max(2,1.5*e.seriesLineWidth)*i;t.arc(r,o,l,0,2*Math.PI,!1),t.fill(),t.fillStyle=e.fillColor,t.beginPath(),t.arc(r,o,e.radius*i,0,2*Math.PI,!1),t.fill(),t.lineWidth=h,t.strokeStyle=e.strokeColor,t.beginPath(),t.arc(r,o,e.radius*i+h/2,0,2*Math.PI,!1),t.stroke()}}!function(t){t[t.AnimationPeriod=2600]="AnimationPeriod",t[t.Stage1Period=.25]="Stage1Period",t[t.Stage2Period=.275]="Stage2Period",t[t.Stage3Period=.475]="Stage3Period",t[t.Stage1StartCircleRadius=4]="Stage1StartCircleRadius",t[t.Stage1EndCircleRadius=10]="Stage1EndCircleRadius",t[t.Stage1StartFillAlpha=.25]="Stage1StartFillAlpha",t[t.Stage1EndFillAlpha=0]="Stage1EndFillAlpha",t[t.Stage1StartStrokeAlpha=.4]="Stage1StartStrokeAlpha",t[t.Stage1EndStrokeAlpha=.8]="Stage1EndStrokeAlpha",t[t.Stage2StartCircleRadius=10]="Stage2StartCircleRadius",t[t.Stage2EndCircleRadius=14]="Stage2EndCircleRadius",t[t.Stage2StartFillAlpha=0]="Stage2StartFillAlpha",t[t.Stage2EndFillAlpha=0]="Stage2EndFillAlpha",t[t.Stage2StartStrokeAlpha=.8]="Stage2StartStrokeAlpha",t[t.Stage2EndStrokeAlpha=0]="Stage2EndStrokeAlpha",t[t.Stage3StartCircleRadius=14]="Stage3StartCircleRadius",t[t.Stage3EndCircleRadius=14]="Stage3EndCircleRadius",t[t.Stage3StartFillAlpha=0]="Stage3StartFillAlpha",t[t.Stage3EndFillAlpha=0]="Stage3EndFillAlpha",t[t.Stage3StartStrokeAlpha=0]="Stage3StartStrokeAlpha",t[t.Stage3EndStrokeAlpha=0]="Stage3EndStrokeAlpha"}(Gt||(Gt={}));const si=[{start:0,end:.25,startRadius:4,endRadius:10,startFillAlpha:.25,endFillAlpha:0,startStrokeAlpha:.4,endStrokeAlpha:.8},{start:.25,end:.525,startRadius:10,endRadius:14,startFillAlpha:0,endFillAlpha:0,startStrokeAlpha:.8,endStrokeAlpha:0},{start:.525,end:1,startRadius:14,endRadius:14,startFillAlpha:0,endFillAlpha:0,startStrokeAlpha:0,endStrokeAlpha:0}];function ei(t,i,s,e){return function(t,i){if("transparent"===t)return t;const s=m(t),e=s[3];return`rgba(${s[0]}, ${s[1]}, ${s[2]}, ${i*e})`}(t,s+(e-s)*i)}function hi(t,i){const s=t%2600/2600;let e;for(const t of si)if(s>=t.start&&s<=t.end){e=t;break}h(void 0!==e,"Last price animation internal logic error");const n=(s-e.start)/(e.end-e.start);return{fillColor:ei(i,n,e.startFillAlpha,e.endFillAlpha),strokeColor:ei(i,n,e.startStrokeAlpha,e.endStrokeAlpha),radius:(r=n,o=e.startRadius,l=e.endRadius,o+(l-o)*r)};var r,o,l}class ni{constructor(t){this._renderer=new ii,this._invalidated=!0,this._stageInvalidated=!0,this._startTime=performance.now(),this._endTime=this._startTime-1,this._series=t}onDataCleared(){this._endTime=this._startTime-1,this.update()}onNewRealtimeDataReceived(){if(this.update(),2===this._series.options().lastPriceAnimation){const t=performance.now(),i=this._endTime-t;if(i>0)return void(i<650&&(this._endTime+=2600));this._startTime=t,this._endTime=t+2600}}update(){this._invalidated=!0}invalidateStage(){this._stageInvalidated=!0}visible(){return 0!==this._series.options().lastPriceAnimation}animationActive(){switch(this._series.options().lastPriceAnimation){case 0:return!1;case 1:return!0;case 2:return performance.now()<=this._endTime}}renderer(){return this._invalidated?(this._updateImpl(),this._invalidated=!1,this._stageInvalidated=!1):this._stageInvalidated&&(this._updateRendererDataStage(),this._stageInvalidated=!1),this._renderer}_updateImpl(){this._renderer.setData(null);const t=this._series.model().timeScale(),i=t.visibleStrictRange(),s=this._series.firstValue();if(null===i||null===s)return;const e=this._series.lastValueData(!0);if(e.noData||!i.contains(e.index))return;const h={x:t.indexToCoordinate(e.index),y:this._series.priceScale().priceToCoordinate(e.price,s.value)},n=e.color,r=this._series.options().lineWidth,o=hi(this._duration(),n);this._renderer.setData({seriesLineColor:n,seriesLineWidth:r,fillColor:o.fillColor,strokeColor:o.strokeColor,radius:o.radius,center:h})}_updateRendererDataStage(){const t=this._renderer.data();if(null!==t){const i=hi(this._duration(),t.seriesLineColor);t.fillColor=i.fillColor,t.strokeColor=i.strokeColor,t.radius=i.radius}}_duration(){return this.animationActive()?performance.now()-this._startTime:2599}}var ri,oi;function li(t,i){return It(Math.min(Math.max(t,12),30)*i)}function ai(t,i){switch(t){case"arrowDown":case"arrowUp":return li(i,1);case"circle":return li(i,.8);case"square":return li(i,.7)}}function ui(t){return function(t){const i=Math.ceil(t);return i%2!=0?i-1:i}(li(t,1))}function ci(t){return Math.max(li(t,.1),3)}function di(t,i,s,e,h){const n=ai("square",s),r=(n-1)/2,o=t-r,l=i-r;return e>=o&&e<=o+n&&h>=l&&h<=l+n}function fi(t,i,s,e,h){const n=(ai("arrowUp",h)-1)/2,r=(It(h/2)-1)/2;i.beginPath(),t?(i.moveTo(s-n,e),i.lineTo(s,e-n),i.lineTo(s+n,e),i.lineTo(s+r,e),i.lineTo(s+r,e+n),i.lineTo(s-r,e+n),i.lineTo(s-r,e)):(i.moveTo(s-n,e),i.lineTo(s,e+n),i.lineTo(s+n,e),i.lineTo(s+r,e),i.lineTo(s+r,e-n),i.lineTo(s-r,e-n),i.lineTo(s-r,e)),i.fill()}function pi(t,i,s,e,h,n){return di(i,s,e,h,n)}!function(t){t[t.MinShapeSize=12]="MinShapeSize",t[t.MaxShapeSize=30]="MaxShapeSize",t[t.MinShapeMargin=3]="MinShapeMargin"}(ri||(ri={}));class mi extends A{constructor(){super(...arguments),this._data=null,this._textWidthCache=new Yt,this._fontSize=-1,this._fontFamily="",this._font=""}setData(t){this._data=t}setParams(t,i){this._fontSize===t&&this._fontFamily===i||(this._fontSize=t,this._fontFamily=i,this._font=T(t,i),this._textWidthCache.reset())}hitTest(t,i){if(null===this._data||null===this._data.visibleRange)return null;for(let s=this._data.visibleRange.from;s<this._data.visibleRange.to;s++){const e=this._data.items[s];if(bi(e,t,i))return{hitTestData:e.internalId,externalId:e.externalId}}return null}_drawImpl({context:t},i,s){if(null!==this._data&&null!==this._data.visibleRange){t.textBaseline="middle",t.font=this._font;for(let i=this._data.visibleRange.from;i<this._data.visibleRange.to;i++){const s=this._data.items[i];void 0!==s.text&&(s.text.width=this._textWidthCache.measureText(t,s.text.content),s.text.height=this._fontSize,s.text.x=s.x-s.text.width/2),vi(s,t)}}}}function vi(t,i){i.fillStyle=t.color,void 0!==t.text&&function(t,i,s,e){t.fillText(i,s,e)}(i,t.text.content,t.text.x,t.text.y),function(t,i){if(0===t.size)return;switch(t.shape){case"arrowDown":return void fi(!1,i,t.x,t.y,t.size);case"arrowUp":return void fi(!0,i,t.x,t.y,t.size);case"circle":return void function(t,i,s,e){const h=(ai("circle",e)-1)/2;t.beginPath(),t.arc(i,s,h,0,2*Math.PI,!1),t.fill()}(i,t.x,t.y,t.size);case"square":return void function(t,i,s,e){const h=ai("square",e),n=(h-1)/2,r=i-n,o=s-n;t.fillRect(r,o,h,h)}(i,t.x,t.y,t.size)}t.shape}(t,i)}function bi(t,i,s){return!(void 0===t.text||!function(t,i,s,e,h,n){const r=e/2;return h>=t&&h<=t+s&&n>=i-r&&n<=i+r}(t.text.x,t.text.y,t.text.width,t.text.height,i,s))||function(t,i,s){if(0===t.size)return!1;switch(t.shape){case"arrowDown":case"arrowUp":return pi(0,t.x,t.y,t.size,i,s);case"circle":return function(t,i,s,e,h){const n=2+ai("circle",s)/2,r=t-e,o=i-h;return Math.sqrt(r*r+o*o)<=n}(t.x,t.y,t.size,i,s);case"square":return di(t.x,t.y,t.size,i,s)}}(t,i,s)}function gi(t,i,s,e,h,n,r,o,l){const a=w(s)?s:s.close,u=w(s)?s:s.high,c=w(s)?s:s.low,d=w(i.size)?Math.max(i.size,0):1,f=ui(o.barSpacing())*d,p=f/2;switch(t.size=f,i.position){case"inBar":return t.y=r.priceToCoordinate(a,l),void(void 0!==t.text&&(t.text.y=t.y+p+n+.6*h));case"aboveBar":return t.y=r.priceToCoordinate(u,l)-p-e.aboveBar,void 0!==t.text&&(t.text.y=t.y-p-.6*h,e.aboveBar+=1.2*h),void(e.aboveBar+=f+n);case"belowBar":return t.y=r.priceToCoordinate(c,l)+p+e.belowBar,void 0!==t.text&&(t.text.y=t.y+p+n+.6*h,e.belowBar+=1.2*h),void(e.belowBar+=f+n)}i.position}!function(t){t[t.TextMargin=.1]="TextMargin"}(oi||(oi={}));class wi{constructor(t,i){this._invalidated=!0,this._dataInvalidated=!0,this._autoScaleMarginsInvalidated=!0,this._autoScaleMargins=null,this._renderer=new mi,this._series=t,this._model=i,this._data={items:[],visibleRange:null}}update(t){this._invalidated=!0,this._autoScaleMarginsInvalidated=!0,"data"===t&&(this._dataInvalidated=!0)}renderer(t){if(!this._series.visible())return null;this._invalidated&&this._makeValid();const i=this._model.options().layout;return this._renderer.setParams(i.fontSize,i.fontFamily),this._renderer.setData(this._data),this._renderer}autoScaleMargins(){if(this._autoScaleMarginsInvalidated){if(this._series.indexedMarkers().length>0){const t=this._model.timeScale().barSpacing(),i=ci(t),s=1.5*ui(t)+2*i;this._autoScaleMargins={above:s,below:s}}else this._autoScaleMargins=null;this._autoScaleMarginsInvalidated=!1}return this._autoScaleMargins}_makeValid(){const t=this._series.priceScale(),i=this._model.timeScale(),s=this._series.indexedMarkers();this._dataInvalidated&&(this._data.items=s.map((t=>({time:t.time,x:0,y:0,size:0,shape:t.shape,color:t.color,internalId:t.internalId,externalId:t.id,text:void 0}))),this._dataInvalidated=!1);const e=this._model.options().layout;this._data.visibleRange=null;const h=i.visibleStrictRange();if(null===h)return;const n=this._series.firstValue();if(null===n)return;if(0===this._data.items.length)return;let r=NaN;const o=ci(i.barSpacing()),l={aboveBar:o,belowBar:o};this._data.visibleRange=Tt(this._data.items,h,!0);for(let h=this._data.visibleRange.from;h<this._data.visibleRange.to;h++){const a=s[h];a.time!==r&&(l.aboveBar=o,l.belowBar=o,r=a.time);const u=this._data.items[h];u.x=i.indexToCoordinate(a.time),void 0!==a.text&&a.text.length>0&&(u.text={content:a.text,x:0,y:0,width:0,height:0});const c=this._series.dataAt(a.time);null!==c&&gi(u,a,c,l,e.fontSize,o,t,i,n.value)}this._invalidated=!1}}class Mi extends Qt{constructor(t){super(t)}_updateImpl(){const t=this._lineRendererData;t.visible=!1;const i=this._series.options();if(!i.priceLineVisible||!this._series.visible())return;const s=this._series.lastValueData(0===i.priceLineSource);s.noData||(t.visible=!0,t.y=s.coordinate,t.color=this._series.priceLineColor(s.color),t.lineWidth=i.priceLineWidth,t.lineStyle=i.priceLineStyle)}}class Si extends q{constructor(t){super(),this._source=t}_updateRendererData(t,i,s){t.visible=!1,i.visible=!1;const e=this._source;if(!e.visible())return;const h=e.options(),n=h.lastValueVisible,r=""!==e.title(),o=0===h.seriesLastValueMode,l=e.lastValueData(!1);if(l.noData)return;n&&(t.text=this._axisText(l,n,o),t.visible=0!==t.text.length),(r||o)&&(i.text=this._paneText(l,n,r,o),i.visible=i.text.length>0);const a=e.priceLineColor(l.color),u=v(a);s.background=u.background,s.coordinate=l.coordinate,i.borderColor=e.model().backgroundColorAtYPercentFromTop(l.coordinate/e.priceScale().height()),t.borderColor=a,t.color=u.foreground,i.color=u.foreground}_paneText(t,i,s,e){let h="";const n=this._source.title();return s&&0!==n.length&&(h+=`${n} `),i&&e&&(h+=this._source.priceScale().isPercentage()?t.formattedPriceAbsolute:t.formattedPricePercentage),h.trim()}_axisText(t,i,s){return i?s?this._source.priceScale().isPercentage()?t.formattedPricePercentage:t.formattedPriceAbsolute:t.text:""}}class xi{constructor(t,i){this._minValue=t,this._maxValue=i}equals(t){return null!==t&&(this._minValue===t._minValue&&this._maxValue===t._maxValue)}clone(){return new xi(this._minValue,this._maxValue)}minValue(){return this._minValue}maxValue(){return this._maxValue}length(){return this._maxValue-this._minValue}isEmpty(){return this._maxValue===this._minValue||Number.isNaN(this._maxValue)||Number.isNaN(this._minValue)}merge(t){return null===t?this:new xi(Math.min(this.minValue(),t.minValue()),Math.max(this.maxValue(),t.maxValue()))}scaleAroundCenter(t){if(!w(t))return;if(0===this._maxValue-this._minValue)return;const i=.5*(this._maxValue+this._minValue);let s=this._maxValue-i,e=this._minValue-i;s*=t,e*=t,this._maxValue=i+s,this._minValue=i+e}shift(t){w(t)&&(this._maxValue+=t,this._minValue+=t)}toRaw(){return{minValue:this._minValue,maxValue:this._maxValue}}static fromRaw(t){return null===t?null:new xi(t.minValue,t.maxValue)}}class _i{constructor(t,i){this._priceRange=t,this._margins=i||null}priceRange(){return this._priceRange}margins(){return this._margins}toRaw(){return null===this._priceRange?null:{priceRange:this._priceRange.toRaw(),margins:this._margins||void 0}}static fromRaw(t){return null===t?null:new _i(xi.fromRaw(t.priceRange),t.margins)}}class yi extends Qt{constructor(t,i){super(t),this._priceLine=i}_updateImpl(){const t=this._lineRendererData;t.visible=!1;const i=this._priceLine.options();if(!this._series.visible()||!i.lineVisible)return;const s=this._priceLine.yCoord();null!==s&&(t.visible=!0,t.y=s,t.color=i.color,t.lineWidth=i.lineWidth,t.lineStyle=i.lineStyle,t.externalId=this._priceLine.options().id)}}class ki extends q{constructor(t,i){super(),this._series=t,this._priceLine=i}_updateRendererData(t,i,s){t.visible=!1,i.visible=!1;const e=this._priceLine.options(),h=e.axisLabelVisible,n=""!==e.title,r=this._series;if(!h||!r.visible())return;const o=this._priceLine.yCoord();if(null===o)return;n&&(i.text=e.title,i.visible=!0),i.borderColor=r.model().backgroundColorAtYPercentFromTop(o/r.priceScale().height()),t.text=this._formatPrice(e.price),t.visible=!0;const l=v(e.axisLabelColor||e.color);s.background=l.background;const a=e.axisLabelTextColor||l.foreground;t.color=a,i.color=a,s.coordinate=o}_formatPrice(t){const i=this._series.firstValue();return null===i?"":this._series.priceScale().formatPrice(t,i.value)}}class Ci{constructor(t,i){this._series=t,this._options=i,this._priceLineView=new yi(t,this),this._priceAxisView=new ki(t,this),this._panePriceAxisView=new Zt(this._priceAxisView,t,t.model())}applyOptions(t){g(this._options,t),this.update(),this._series.model().lightUpdate()}options(){return this._options}paneView(){return this._priceLineView}labelPaneView(){return this._panePriceAxisView}priceAxisView(){return this._priceAxisView}update(){this._priceLineView.update(),this._priceAxisView.update()}yCoord(){const t=this._series,i=t.priceScale();if(t.model().timeScale().isEmpty()||i.isEmpty())return null;const s=t.firstValue();return null===s?null:i.priceToCoordinate(this._options.price,s.value)}}class Ti extends G{constructor(t){super(),this._model=t}model(){return this._model}}const Pi={Bar:(t,i,s,e)=>{var h;const n=i.upColor,l=i.downColor,a=r(t(s,e)),u=o(a.value[0])<=o(a.value[3]);return{barColor:null!==(h=a.color)&&void 0!==h?h:u?n:l}},Candlestick:(t,i,s,e)=>{var h,n,l;const a=i.upColor,u=i.downColor,c=i.borderUpColor,d=i.borderDownColor,f=i.wickUpColor,p=i.wickDownColor,m=r(t(s,e)),v=o(m.value[0])<=o(m.value[3]);return{barColor:null!==(h=m.color)&&void 0!==h?h:v?a:u,barBorderColor:null!==(n=m.borderColor)&&void 0!==n?n:v?c:d,barWickColor:null!==(l=m.wickColor)&&void 0!==l?l:v?f:p}},Area:(t,i,s,e)=>{var h,n,o,l;const a=r(t(s,e));return{barColor:null!==(h=a.lineColor)&&void 0!==h?h:i.lineColor,lineColor:null!==(n=a.lineColor)&&void 0!==n?n:i.lineColor,topColor:null!==(o=a.topColor)&&void 0!==o?o:i.topColor,bottomColor:null!==(l=a.bottomColor)&&void 0!==l?l:i.bottomColor}},Baseline:(t,i,s,e)=>{var h,n,o,l,a,u;const c=r(t(s,e));return{barColor:c.value[3]>=i.baseValue.price?i.topLineColor:i.bottomLineColor,topLineColor:null!==(h=c.topLineColor)&&void 0!==h?h:i.topLineColor,bottomLineColor:null!==(n=c.bottomLineColor)&&void 0!==n?n:i.bottomLineColor,topFillColor1:null!==(o=c.topFillColor1)&&void 0!==o?o:i.topFillColor1,topFillColor2:null!==(l=c.topFillColor2)&&void 0!==l?l:i.topFillColor2,bottomFillColor1:null!==(a=c.bottomFillColor1)&&void 0!==a?a:i.bottomFillColor1,bottomFillColor2:null!==(u=c.bottomFillColor2)&&void 0!==u?u:i.bottomFillColor2}},Line:(t,i,s,e)=>{var h,n;const o=r(t(s,e));return{barColor:null!==(h=o.color)&&void 0!==h?h:i.color,lineColor:null!==(n=o.color)&&void 0!==n?n:i.color}},Histogram:(t,i,s,e)=>{var h;return{barColor:null!==(h=r(t(s,e)).color)&&void 0!==h?h:i.color}}};class Ri{constructor(t){this._findBar=(t,i)=>void 0!==i?i.value:this._series.bars().valueAt(t),this._series=t,this._styleGetter=Pi[t.seriesType()]}barStyle(t,i){return this._styleGetter(this._findBar,this._series.options(),t,i)}}var Di;!function(t){t[t.NearestLeft=-1]="NearestLeft",t[t.None=0]="None",t[t.NearestRight=1]="NearestRight"}(Di||(Di={}));const Ai=30;class Ei{constructor(){this._items=[],this._minMaxCache=new Map,this._rowSearchCache=new Map}last(){return this.size()>0?this._items[this._items.length-1]:null}firstIndex(){return this.size()>0?this._indexAt(0):null}lastIndex(){return this.size()>0?this._indexAt(this._items.length-1):null}size(){return this._items.length}isEmpty(){return 0===this.size()}contains(t){return null!==this._search(t,0)}valueAt(t){return this.search(t)}search(t,i=0){const s=this._search(t,i);return null===s?null:Object.assign(Object.assign({},this._valueAt(s)),{index:this._indexAt(s)})}rows(){return this._items}minMaxOnRangeCached(t,i,s){if(this.isEmpty())return null;let e=null;for(const h of s){e=Oi(e,this._minMaxOnRangeCachedImpl(t,i,h))}return e}setData(t){this._rowSearchCache.clear(),this._minMaxCache.clear(),this._items=t}_indexAt(t){return this._items[t].index}_valueAt(t){return this._items[t]}_search(t,i){const s=this._bsearch(t);if(null===s&&0!==i)switch(i){case-1:return this._searchNearestLeft(t);case 1:return this._searchNearestRight(t);default:throw new TypeError("Unknown search mode")}return s}_searchNearestLeft(t){let i=this._lowerbound(t);return i>0&&(i-=1),i!==this._items.length&&this._indexAt(i)<t?i:null}_searchNearestRight(t){const i=this._upperbound(t);return i!==this._items.length&&t<this._indexAt(i)?i:null}_bsearch(t){const i=this._lowerbound(t);return i===this._items.length||t<this._items[i].index?null:i}_lowerbound(t){return St(this._items,t,((t,i)=>t.index<i))}_upperbound(t){return xt(this._items,t,((t,i)=>i.index>t))}_plotMinMax(t,i,s){let e=null;for(let h=t;h<i;h++){const t=this._items[h].value[s];Number.isNaN(t)||(null===e?e={min:t,max:t}:(t<e.min&&(e.min=t),t>e.max&&(e.max=t)))}return e}_minMaxOnRangeCachedImpl(t,i,s){if(this.isEmpty())return null;let e=null;const h=r(this.firstIndex()),n=r(this.lastIndex()),o=Math.max(t,h),l=Math.min(i,n),a=Math.ceil(o/Ai)*Ai,u=Math.max(a,Math.floor(l/Ai)*Ai);{const t=this._lowerbound(o),h=this._upperbound(Math.min(l,a,i));e=Oi(e,this._plotMinMax(t,h,s))}let c=this._minMaxCache.get(s);void 0===c&&(c=new Map,this._minMaxCache.set(s,c));for(let t=Math.max(a+1,o);t<u;t+=Ai){const i=Math.floor(t/Ai);let h=c.get(i);if(void 0===h){const t=this._lowerbound(i*Ai),e=this._upperbound((i+1)*Ai-1);h=this._plotMinMax(t,e,s),c.set(i,h)}e=Oi(e,h)}{const t=this._lowerbound(u),i=this._upperbound(l);e=Oi(e,this._plotMinMax(t,i,s))}return e}}function Oi(t,i){if(null===t)return i;if(null===i)return t;return{min:Math.min(t.min,i.min),max:Math.max(t.max,i.max)}}class Bi extends Ti{constructor(t,i,s){super(t),this._data=new Ei,this._priceLineView=new Mi(this),this._customPriceLines=[],this._baseHorizontalLineView=new ti(this),this._lastPriceAnimationPaneView=null,this._barColorerCache=null,this._markers=[],this._indexedMarkers=[],this._animationTimeoutId=null,this._options=i,this._seriesType=s;const e=new Si(this);this._priceAxisViews=[e],this._panePriceAxisView=new Zt(e,this,t),"Area"!==s&&"Line"!==s&&"Baseline"!==s||(this._lastPriceAnimationPaneView=new ni(this)),this._recreateFormatter(),this._recreatePaneViews()}destroy(){null!==this._animationTimeoutId&&clearTimeout(this._animationTimeoutId)}priceLineColor(t){return this._options.priceLineColor||t}lastValueData(t){const i={noData:!0},s=this.priceScale();if(this.model().timeScale().isEmpty()||s.isEmpty()||this._data.isEmpty())return i;const e=this.model().timeScale().visibleStrictRange(),h=this.firstValue();if(null===e||null===h)return i;let n,r;if(t){const t=this._data.last();if(null===t)return i;n=t,r=t.index}else{const t=this._data.search(e.right(),-1);if(null===t)return i;if(n=this._data.valueAt(t.index),null===n)return i;r=t.index}const o=n.value[3],l=this.barColorer().barStyle(r,{value:n}),a=s.priceToCoordinate(o,h.value);return{noData:!1,price:o,text:s.formatPrice(o,h.value),formattedPriceAbsolute:s.formatPriceAbsolute(o),formattedPricePercentage:s.formatPricePercentage(o,h.value),color:l.barColor,coordinate:a,index:r}}barColorer(){return null!==this._barColorerCache||(this._barColorerCache=new Ri(this)),this._barColorerCache}options(){return this._options}applyOptions(t){const i=t.priceScaleId;void 0!==i&&i!==this._options.priceScaleId&&this.model().moveSeriesToScale(this,i),g(this._options,t),void 0!==t.priceFormat&&(this._recreateFormatter(),this.model().fullUpdate()),this.model().updateSource(this),this.model().updateCrosshair(),this._paneView.update("options")}setData(t,i){this._data.setData(t),this._recalculateMarkers(),this._paneView.update("data"),this._markersPaneView.update("data"),null!==this._lastPriceAnimationPaneView&&(i&&i.lastBarUpdatedOrNewBarsAddedToTheRight?this._lastPriceAnimationPaneView.onNewRealtimeDataReceived():0===t.length&&this._lastPriceAnimationPaneView.onDataCleared());const s=this.model().paneForSource(this);this.model().recalculatePane(s),this.model().updateSource(this),this.model().updateCrosshair(),this.model().lightUpdate()}setMarkers(t){this._markers=t,this._recalculateMarkers();const i=this.model().paneForSource(this);this._markersPaneView.update("data"),this.model().recalculatePane(i),this.model().updateSource(this),this.model().updateCrosshair(),this.model().lightUpdate()}markers(){return this._markers}indexedMarkers(){return this._indexedMarkers}createPriceLine(t){const i=new Ci(this,t);return this._customPriceLines.push(i),this.model().updateSource(this),i}removePriceLine(t){const i=this._customPriceLines.indexOf(t);-1!==i&&this._customPriceLines.splice(i,1),this.model().updateSource(this)}seriesType(){return this._seriesType}firstValue(){const t=this.firstBar();return null===t?null:{value:t.value[3],timePoint:t.time}}firstBar(){const t=this.model().timeScale().visibleStrictRange();if(null===t)return null;const i=t.left();return this._data.search(i,1)}bars(){return this._data}dataAt(t){const i=this._data.valueAt(t);return null===i?null:"Bar"===this._seriesType||"Candlestick"===this._seriesType?{open:i.value[0],high:i.value[1],low:i.value[2],close:i.value[3]}:i.value[3]}topPaneViews(t){const i=this._lastPriceAnimationPaneView;return null!==i&&i.visible()?(null===this._animationTimeoutId&&i.animationActive()&&(this._animationTimeoutId=setTimeout((()=>{this._animationTimeoutId=null,this.model().cursorUpdate()}),0)),i.invalidateStage(),[i]):[]}paneViews(){const t=[];this._isOverlay()||t.push(this._baseHorizontalLineView),t.push(this._paneView,this._priceLineView,this._markersPaneView);const i=this._customPriceLines.map((t=>t.paneView()));return t.push(...i),t}labelPaneViews(t){return[this._panePriceAxisView,...this._customPriceLines.map((t=>t.labelPaneView()))]}priceAxisViews(t,i){if(i!==this._priceScale&&!this._isOverlay())return[];const s=[...this._priceAxisViews];for(const t of this._customPriceLines)s.push(t.priceAxisView());return s}autoscaleInfo(t,i){if(void 0!==this._options.autoscaleInfoProvider){const s=this._options.autoscaleInfoProvider((()=>{const s=this._autoscaleInfoImpl(t,i);return null===s?null:s.toRaw()}));return _i.fromRaw(s)}return this._autoscaleInfoImpl(t,i)}minMove(){return this._options.priceFormat.minMove}formatter(){return this._formatter}updateAllViews(){var t;this._paneView.update(),this._markersPaneView.update();for(const t of this._priceAxisViews)t.update();for(const t of this._customPriceLines)t.update();this._priceLineView.update(),this._baseHorizontalLineView.update(),null===(t=this._lastPriceAnimationPaneView)||void 0===t||t.update()}priceScale(){return r(super.priceScale())}markerDataAtIndex(t){if(!(("Line"===this._seriesType||"Area"===this._seriesType||"Baseline"===this._seriesType)&&this._options.crosshairMarkerVisible))return null;const i=this._data.valueAt(t);if(null===i)return null;return{price:i.value[3],radius:this._markerRadius(),borderColor:this._markerBorderColor(),borderWidth:this._markerBorderWidth(),backgroundColor:this._markerBackgroundColor(t)}}title(){return this._options.title}visible(){return this._options.visible}_isOverlay(){return!et(this.priceScale().id())}_autoscaleInfoImpl(t,i){if(!M(t)||!M(i)||this._data.isEmpty())return null;const s="Line"===this._seriesType||"Area"===this._seriesType||"Baseline"===this._seriesType||"Histogram"===this._seriesType?[3]:[2,1],e=this._data.minMaxOnRangeCached(t,i,s);let h=null!==e?new xi(e.min,e.max):null;if("Histogram"===this.seriesType()){const t=this._options.base,i=new xi(t,t);h=null!==h?h.merge(i):i}return new _i(h,this._markersPaneView.autoScaleMargins())}_markerRadius(){switch(this._seriesType){case"Line":case"Area":case"Baseline":return this._options.crosshairMarkerRadius}return 0}_markerBorderColor(){switch(this._seriesType){case"Line":case"Area":case"Baseline":{const t=this._options.crosshairMarkerBorderColor;if(0!==t.length)return t}}return null}_markerBorderWidth(){switch(this._seriesType){case"Line":case"Area":case"Baseline":return this._options.crosshairMarkerBorderWidth}return 0}_markerBackgroundColor(t){switch(this._seriesType){case"Line":case"Area":case"Baseline":{const t=this._options.crosshairMarkerBackgroundColor;if(0!==t.length)return t}}return this.barColorer().barStyle(t).barColor}_recreateFormatter(){switch(this._options.priceFormat.type){case"custom":this._formatter={format:this._options.priceFormat.formatter};break;case"volume":this._formatter=new at(this._options.priceFormat.precision);break;case"percent":this._formatter=new lt(this._options.priceFormat.precision);break;default:{const t=Math.pow(10,this._options.priceFormat.precision);this._formatter=new ot(t,this._options.priceFormat.minMove*t)}}null!==this._priceScale&&this._priceScale.updateFormatter()}_recalculateMarkers(){const t=this.model().timeScale();if(!t.hasPoints()||this._data.isEmpty())return void(this._indexedMarkers=[]);const i=r(this._data.firstIndex());this._indexedMarkers=this._markers.map(((s,e)=>{const h=r(t.timeToIndex(s.time,!0)),n=h<i?1:-1;return{time:r(this._data.search(h,n)).index,position:s.position,shape:s.shape,color:s.color,id:s.id,internalId:e,text:s.text,size:s.size}}))}_recreatePaneViews(){switch(this._markersPaneView=new wi(this,this.model()),this._seriesType){case"Bar":this._paneView=new Ot(this,this.model());break;case"Candlestick":this._paneView=new jt(this,this.model());break;case"Line":this._paneView=new Ut(this,this.model());break;case"Area":this._paneView=new Dt(this,this.model());break;case"Baseline":this._paneView=new Ft(this,this.model());break;case"Histogram":this._paneView=new $t(this,this.model());break;default:throw Error("Unknown chart style assigned: "+this._seriesType)}}}class Li{constructor(t){this._options=t}align(t,i,s){let e=t;if(0===this._options.mode)return e;const h=s.defaultPriceScale(),n=h.firstValue();if(null===n)return e;const r=h.priceToCoordinate(t,n),l=s.dataSources().filter((t=>t instanceof Bi)).reduce(((t,e)=>{if(s.isOverlay(e)||!e.visible())return t;const h=e.priceScale(),n=e.bars();if(h.isEmpty()||!n.contains(i))return t;const r=n.valueAt(i);if(null===r)return t;const l=o(e.firstValue());return t.concat([h.priceToCoordinate(r.value[3],l.value)])}),[]);if(0===l.length)return e;l.sort(((t,i)=>Math.abs(t-r)-Math.abs(i-r)));const a=l[0];return e=h.coordinateToPrice(a,n),e}}class zi extends z{constructor(){super(...arguments),this._data=null}setData(t){this._data=t}_drawImpl({context:t,bitmapSize:i,horizontalPixelRatio:e,verticalPixelRatio:h}){if(null===this._data)return;const n=Math.max(1,Math.floor(e));t.lineWidth=n,function(t,i){t.save(),t.lineWidth%2&&t.translate(.5,.5),i(),t.restore()}(t,(()=>{const o=r(this._data);if(o.vertLinesVisible){t.strokeStyle=o.vertLinesColor,s(t,o.vertLineStyle),t.beginPath();for(const s of o.timeMarks){const h=Math.round(s.coord*e);t.moveTo(h,-n),t.lineTo(h,i.height+n)}t.stroke()}if(o.horzLinesVisible){t.strokeStyle=o.horzLinesColor,s(t,o.horzLineStyle),t.beginPath();for(const s of o.priceMarks){const e=Math.round(s.coord*h);t.moveTo(-n,e),t.lineTo(i.width+n,e)}t.stroke()}}))}}class Ii{constructor(t){this._renderer=new zi,this._invalidated=!0,this._pane=t}update(){this._invalidated=!0}renderer(){if(this._invalidated){const t=this._pane.model().options().grid,i={horzLinesVisible:t.horzLines.visible,vertLinesVisible:t.vertLines.visible,horzLinesColor:t.horzLines.color,vertLinesColor:t.vertLines.color,horzLineStyle:t.horzLines.style,vertLineStyle:t.vertLines.style,priceMarks:this._pane.defaultPriceScale().marks(),timeMarks:this._pane.model().timeScale().marks()||[]};this._renderer.setData(i),this._invalidated=!1}return this._renderer}}class Ni{constructor(t){this._paneView=new Ii(t)}paneView(){return this._paneView}}const Vi={logicalOffset:4,coordOffset:1e-4};function Fi(t,i){const s=100*(t-i)/i;return i<0?-s:s}function Wi(t,i){const s=Fi(t.minValue(),i),e=Fi(t.maxValue(),i);return new xi(s,e)}function ji(t,i){const s=100*(t-i)/i+100;return i<0?-s:s}function Hi(t,i){const s=ji(t.minValue(),i),e=ji(t.maxValue(),i);return new xi(s,e)}function $i(t,i){const s=Math.abs(t);if(s<1e-15)return 0;const e=zt(s+i.coordOffset)+i.logicalOffset;return t<0?-e:e}function Ui(t,i){const s=Math.abs(t);if(s<1e-15)return 0;const e=Math.pow(10,s-i.logicalOffset)-i.coordOffset;return t<0?-e:e}function qi(t,i){if(null===t)return null;const s=$i(t.minValue(),i),e=$i(t.maxValue(),i);return new xi(s,e)}function Yi(t,i){if(null===t)return null;const s=Ui(t.minValue(),i),e=Ui(t.maxValue(),i);return new xi(s,e)}function Xi(t){if(null===t)return Vi;const i=Math.abs(t.maxValue()-t.minValue());if(i>=1||i<1e-15)return Vi;const s=Math.ceil(Math.abs(Math.log10(i))),e=Vi.logicalOffset+s;return{logicalOffset:e,coordOffset:1/Math.pow(10,e)}}var Zi;!function(t){t[t.TickSpanEpsilon=1e-14]="TickSpanEpsilon"}(Zi||(Zi={}));class Ki{constructor(t,i){if(this._base=t,this._integralDividers=i,function(t){if(t<0)return!1;for(let i=t;i>1;i/=10)if(i%10!=0)return!1;return!0}(this._base))this._fractionalDividers=[2,2.5,2];else{this._fractionalDividers=[];for(let t=this._base;1!==t;){if(t%2==0)this._fractionalDividers.push(2),t/=2;else{if(t%5!=0)throw new Error("unexpected base");this._fractionalDividers.push(2,2.5),t/=5}if(this._fractionalDividers.length>100)throw new Error("something wrong with base")}}}tickSpan(t,i,s){const e=0===this._base?0:1/this._base;let h=Math.pow(10,Math.max(0,Math.ceil(zt(t-i)))),n=0,r=this._integralDividers[0];for(;;){const t=Lt(h,e,1e-14)&&h>e+1e-14,i=Lt(h,s*r,1e-14),o=Lt(h,1,1e-14);if(!(t&&i&&o))break;h/=r,r=this._integralDividers[++n%this._integralDividers.length]}if(h<=e+1e-14&&(h=e),h=Math.max(1,h),this._fractionalDividers.length>0&&(o=h,l=1,a=1e-14,Math.abs(o-l)<a))for(n=0,r=this._fractionalDividers[0];Lt(h,s*r,1e-14)&&h>e+1e-14;)h/=r,r=this._fractionalDividers[++n%this._fractionalDividers.length];var o,l,a;return h}}class Gi{constructor(t,i,s,e){this._marks=[],this._priceScale=t,this._base=i,this._coordinateToLogicalFunc=s,this._logicalToCoordinateFunc=e}tickSpan(t,i){if(t<i)throw new Error("high < low");const s=this._priceScale.height(),e=(t-i)*this._tickMarkHeight()/s,h=new Ki(this._base,[2,2.5,2]),n=new Ki(this._base,[2,2,2.5]),r=new Ki(this._base,[2.5,2,2]),o=[];return o.push(h.tickSpan(t,i,e),n.tickSpan(t,i,e),r.tickSpan(t,i,e)),function(t){if(t.length<1)throw Error("array is empty");let i=t[0];for(let s=1;s<t.length;++s)t[s]<i&&(i=t[s]);return i}(o)}rebuildTickMarks(){const t=this._priceScale,i=t.firstValue();if(null===i)return void(this._marks=[]);const s=t.height(),e=this._coordinateToLogicalFunc(s-1,i),h=this._coordinateToLogicalFunc(0,i),n=this._priceScale.options().entireTextOnly?this._fontHeight()/2:0,r=n,o=s-1-n,l=Math.max(e,h),a=Math.min(e,h);if(l===a)return void(this._marks=[]);let u=this.tickSpan(l,a),c=l%u;c+=c<0?u:0;const d=l>=a?1:-1;let f=null,p=0;for(let s=l-c;s>a;s-=u){const e=this._logicalToCoordinateFunc(s,i,!0);null!==f&&Math.abs(e-f)<this._tickMarkHeight()||(e<r||e>o||(p<this._marks.length?(this._marks[p].coord=e,this._marks[p].label=t.formatLogical(s)):this._marks.push({coord:e,label:t.formatLogical(s)}),p++,f=e,t.isLog()&&(u=this.tickSpan(s*d,a))))}this._marks.length=p}marks(){return this._marks}_fontHeight(){return this._priceScale.fontSize()}_tickMarkHeight(){return Math.ceil(2.5*this._fontHeight())}}function Ji(t){return t.slice().sort(((t,i)=>r(t.zorder())-r(i.zorder())))}var Qi;!function(t){t[t.Normal=0]="Normal",t[t.Logarithmic=1]="Logarithmic",t[t.Percentage=2]="Percentage",t[t.IndexedTo100=3]="IndexedTo100"}(Qi||(Qi={}));const ts=new lt,is=new ot(100,1);class ss{constructor(t,i,s,e){this._height=0,this._internalHeightCache=null,this._priceRange=null,this._priceRangeSnapshot=null,this._invalidatedForRange={isValid:!1,visibleBars:null},this._marginAbove=0,this._marginBelow=0,this._onMarksChanged=new b,this._modeChanged=new b,this._dataSources=[],this._cachedOrderedSources=null,this._marksCache=null,this._scaleStartPoint=null,this._scrollStartPoint=null,this._formatter=is,this._logFormula=Xi(null),this._id=t,this._options=i,this._layoutOptions=s,this._localizationOptions=e,this._markBuilder=new Gi(this,100,this._coordinateToLogical.bind(this),this._logicalToCoordinate.bind(this))}id(){return this._id}options(){return this._options}applyOptions(t){if(g(this._options,t),this.updateFormatter(),void 0!==t.mode&&this.setMode({mode:t.mode}),void 0!==t.scaleMargins){const i=n(t.scaleMargins.top),s=n(t.scaleMargins.bottom);if(i<0||i>1)throw new Error(`Invalid top margin - expect value between 0 and 1, given=${i}`);if(s<0||s>1||i+s>1)throw new Error(`Invalid bottom margin - expect value between 0 and 1, given=${s}`);if(i+s>1)throw new Error(`Invalid margins - sum of margins must be less than 1, given=${i+s}`);this._invalidateInternalHeightCache(),this._marksCache=null}}isAutoScale(){return this._options.autoScale}isLog(){return 1===this._options.mode}isPercentage(){return 2===this._options.mode}isIndexedTo100(){return 3===this._options.mode}mode(){return{autoScale:this._options.autoScale,isInverted:this._options.invertScale,mode:this._options.mode}}setMode(t){const i=this.mode();let s=null;void 0!==t.autoScale&&(this._options.autoScale=t.autoScale),void 0!==t.mode&&(this._options.mode=t.mode,2!==t.mode&&3!==t.mode||(this._options.autoScale=!0),this._invalidatedForRange.isValid=!1),1===i.mode&&t.mode!==i.mode&&(!function(t,i){if(null===t)return!1;const s=Ui(t.minValue(),i),e=Ui(t.maxValue(),i);return isFinite(s)&&isFinite(e)}(this._priceRange,this._logFormula)?this._options.autoScale=!0:(s=Yi(this._priceRange,this._logFormula),null!==s&&this.setPriceRange(s))),1===t.mode&&t.mode!==i.mode&&(s=qi(this._priceRange,this._logFormula),null!==s&&this.setPriceRange(s));const e=i.mode!==this._options.mode;e&&(2===i.mode||this.isPercentage())&&this.updateFormatter(),e&&(3===i.mode||this.isIndexedTo100())&&this.updateFormatter(),void 0!==t.isInverted&&i.isInverted!==t.isInverted&&(this._options.invertScale=t.isInverted,this._onIsInvertedChanged()),this._modeChanged.fire(i,this.mode())}modeChanged(){return this._modeChanged}fontSize(){return this._layoutOptions.fontSize}height(){return this._height}setHeight(t){this._height!==t&&(this._height=t,this._invalidateInternalHeightCache(),this._marksCache=null)}internalHeight(){if(this._internalHeightCache)return this._internalHeightCache;const t=this.height()-this._topMarginPx()-this._bottomMarginPx();return this._internalHeightCache=t,t}priceRange(){return this._makeSureItIsValid(),this._priceRange}setPriceRange(t,i){const s=this._priceRange;(i||null===s&&null!==t||null!==s&&!s.equals(t))&&(this._marksCache=null,this._priceRange=t)}isEmpty(){return this._makeSureItIsValid(),0===this._height||!this._priceRange||this._priceRange.isEmpty()}invertedCoordinate(t){return this.isInverted()?t:this.height()-1-t}priceToCoordinate(t,i){return this.isPercentage()?t=Fi(t,i):this.isIndexedTo100()&&(t=ji(t,i)),this._logicalToCoordinate(t,i)}pointsArrayToCoordinates(t,i,s){this._makeSureItIsValid();const e=this._bottomMarginPx(),h=r(this.priceRange()),n=h.minValue(),o=h.maxValue(),l=this.internalHeight()-1,a=this.isInverted(),u=l/(o-n),c=void 0===s?0:s.from,d=void 0===s?t.length:s.to,f=this._getCoordinateTransformer();for(let s=c;s<d;s++){const h=t[s],r=h.price;if(isNaN(r))continue;let o=r;null!==f&&(o=f(h.price,i));const l=e+u*(o-n),c=a?l:this._height-1-l;h.y=c}}barPricesToCoordinates(t,i,s){this._makeSureItIsValid();const e=this._bottomMarginPx(),h=r(this.priceRange()),n=h.minValue(),o=h.maxValue(),l=this.internalHeight()-1,a=this.isInverted(),u=l/(o-n),c=void 0===s?0:s.from,d=void 0===s?t.length:s.to,f=this._getCoordinateTransformer();for(let s=c;s<d;s++){const h=t[s];let r=h.open,o=h.high,l=h.low,c=h.close;null!==f&&(r=f(h.open,i),o=f(h.high,i),l=f(h.low,i),c=f(h.close,i));let d=e+u*(r-n),p=a?d:this._height-1-d;h.openY=p,d=e+u*(o-n),p=a?d:this._height-1-d,h.highY=p,d=e+u*(l-n),p=a?d:this._height-1-d,h.lowY=p,d=e+u*(c-n),p=a?d:this._height-1-d,h.closeY=p}}coordinateToPrice(t,i){const s=this._coordinateToLogical(t,i);return this.logicalToPrice(s,i)}logicalToPrice(t,i){let s=t;return this.isPercentage()?s=function(t,i){return i<0&&(t=-t),t/100*i+i}(s,i):this.isIndexedTo100()&&(s=function(t,i){return t-=100,i<0&&(t=-t),t/100*i+i}(s,i)),s}dataSources(){return this._dataSources}orderedSources(){if(this._cachedOrderedSources)return this._cachedOrderedSources;let t=[];for(let i=0;i<this._dataSources.length;i++){const s=this._dataSources[i];null===s.zorder()&&s.setZorder(i+1),t.push(s)}return t=Ji(t),this._cachedOrderedSources=t,this._cachedOrderedSources}addDataSource(t){-1===this._dataSources.indexOf(t)&&(this._dataSources.push(t),this.updateFormatter(),this.invalidateSourcesCache())}removeDataSource(t){const i=this._dataSources.indexOf(t);if(-1===i)throw new Error("source is not attached to scale");this._dataSources.splice(i,1),0===this._dataSources.length&&(this.setMode({autoScale:!0}),this.setPriceRange(null)),this.updateFormatter(),this.invalidateSourcesCache()}firstValue(){let t=null;for(const i of this._dataSources){const s=i.firstValue();null!==s&&((null===t||s.timePoint<t.timePoint)&&(t=s))}return null===t?null:t.value}isInverted(){return this._options.invertScale}marks(){const t=null===this.firstValue();if(null!==this._marksCache&&(t||this._marksCache.firstValueIsNull===t))return this._marksCache.marks;this._markBuilder.rebuildTickMarks();const i=this._markBuilder.marks();return this._marksCache={marks:i,firstValueIsNull:t},this._onMarksChanged.fire(),i}onMarksChanged(){return this._onMarksChanged}startScale(t){this.isPercentage()||this.isIndexedTo100()||null===this._scaleStartPoint&&null===this._priceRangeSnapshot&&(this.isEmpty()||(this._scaleStartPoint=this._height-t,this._priceRangeSnapshot=r(this.priceRange()).clone()))}scaleTo(t){if(this.isPercentage()||this.isIndexedTo100())return;if(null===this._scaleStartPoint)return;this.setMode({autoScale:!1}),(t=this._height-t)<0&&(t=0);let i=(this._scaleStartPoint+.2*(this._height-1))/(t+.2*(this._height-1));const s=r(this._priceRangeSnapshot).clone();i=Math.max(i,.1),s.scaleAroundCenter(i),this.setPriceRange(s)}endScale(){this.isPercentage()||this.isIndexedTo100()||(this._scaleStartPoint=null,this._priceRangeSnapshot=null)}startScroll(t){this.isAutoScale()||null===this._scrollStartPoint&&null===this._priceRangeSnapshot&&(this.isEmpty()||(this._scrollStartPoint=t,this._priceRangeSnapshot=r(this.priceRange()).clone()))}scrollTo(t){if(this.isAutoScale())return;if(null===this._scrollStartPoint)return;const i=r(this.priceRange()).length()/(this.internalHeight()-1);let s=t-this._scrollStartPoint;this.isInverted()&&(s*=-1);const e=s*i,h=r(this._priceRangeSnapshot).clone();h.shift(e),this.setPriceRange(h,!0),this._marksCache=null}endScroll(){this.isAutoScale()||null!==this._scrollStartPoint&&(this._scrollStartPoint=null,this._priceRangeSnapshot=null)}formatter(){return this._formatter||this.updateFormatter(),this._formatter}formatPrice(t,i){switch(this._options.mode){case 2:return this._formatPercentage(Fi(t,i));case 3:return this.formatter().format(ji(t,i));default:return this._formatPrice(t)}}formatLogical(t){switch(this._options.mode){case 2:return this._formatPercentage(t);case 3:return this.formatter().format(t);default:return this._formatPrice(t)}}formatPriceAbsolute(t){return this._formatPrice(t,r(this._formatterSource()).formatter())}formatPricePercentage(t,i){return t=Fi(t,i),this._formatPercentage(t,ts)}sourcesForAutoScale(){return this._dataSources}recalculatePriceRange(t){this._invalidatedForRange={visibleBars:t,isValid:!1}}updateAllViews(){this._dataSources.forEach((t=>t.updateAllViews()))}updateFormatter(){this._marksCache=null;const t=this._formatterSource();let i=100;null!==t&&(i=Math.round(1/t.minMove())),this._formatter=is,this.isPercentage()?(this._formatter=ts,i=100):this.isIndexedTo100()?(this._formatter=new ot(100,1),i=100):null!==t&&(this._formatter=t.formatter()),this._markBuilder=new Gi(this,i,this._coordinateToLogical.bind(this),this._logicalToCoordinate.bind(this)),this._markBuilder.rebuildTickMarks()}invalidateSourcesCache(){this._cachedOrderedSources=null}_formatterSource(){return this._dataSources[0]||null}_topMarginPx(){return this.isInverted()?this._options.scaleMargins.bottom*this.height()+this._marginBelow:this._options.scaleMargins.top*this.height()+this._marginAbove}_bottomMarginPx(){return this.isInverted()?this._options.scaleMargins.top*this.height()+this._marginAbove:this._options.scaleMargins.bottom*this.height()+this._marginBelow}_makeSureItIsValid(){this._invalidatedForRange.isValid||(this._invalidatedForRange.isValid=!0,this._recalculatePriceRangeImpl())}_invalidateInternalHeightCache(){this._internalHeightCache=null}_logicalToCoordinate(t,i){if(this._makeSureItIsValid(),this.isEmpty())return 0;t=this.isLog()&&t?$i(t,this._logFormula):t;const s=r(this.priceRange()),e=this._bottomMarginPx()+(this.internalHeight()-1)*(t-s.minValue())/s.length();return this.invertedCoordinate(e)}_coordinateToLogical(t,i){if(this._makeSureItIsValid(),this.isEmpty())return 0;const s=this.invertedCoordinate(t),e=r(this.priceRange()),h=e.minValue()+e.length()*((s-this._bottomMarginPx())/(this.internalHeight()-1));return this.isLog()?Ui(h,this._logFormula):h}_onIsInvertedChanged(){this._marksCache=null,this._markBuilder.rebuildTickMarks()}_recalculatePriceRangeImpl(){const t=this._invalidatedForRange.visibleBars;if(null===t)return;let i=null;const s=this.sourcesForAutoScale();let e=0,h=0;for(const n of s){if(!n.visible())continue;const s=n.firstValue();if(null===s)continue;const o=n.autoscaleInfo(t.left(),t.right());let l=o&&o.priceRange();if(null!==l){switch(this._options.mode){case 1:l=qi(l,this._logFormula);break;case 2:l=Wi(l,s.value);break;case 3:l=Hi(l,s.value)}if(i=null===i?l:i.merge(r(l)),null!==o){const t=o.margins();null!==t&&(e=Math.max(e,t.above),h=Math.max(e,t.below))}}}if(e===this._marginAbove&&h===this._marginBelow||(this._marginAbove=e,this._marginBelow=h,this._marksCache=null,this._invalidateInternalHeightCache()),null!==i){if(i.minValue()===i.maxValue()){const t=this._formatterSource(),s=5*(null===t||this.isPercentage()||this.isIndexedTo100()?1:t.minMove());this.isLog()&&(i=Yi(i,this._logFormula)),i=new xi(i.minValue()-s,i.maxValue()+s),this.isLog()&&(i=qi(i,this._logFormula))}if(this.isLog()){const t=Yi(i,this._logFormula),s=Xi(t);if(n=s,o=this._logFormula,n.logicalOffset!==o.logicalOffset||n.coordOffset!==o.coordOffset){const e=null!==this._priceRangeSnapshot?Yi(this._priceRangeSnapshot,this._logFormula):null;this._logFormula=s,i=qi(t,s),null!==e&&(this._priceRangeSnapshot=qi(e,s))}}this.setPriceRange(i)}else null===this._priceRange&&(this.setPriceRange(new xi(-.5,.5)),this._logFormula=Xi(null));var n,o;this._invalidatedForRange.isValid=!0}_getCoordinateTransformer(){return this.isPercentage()?Fi:this.isIndexedTo100()?ji:this.isLog()?t=>$i(t,this._logFormula):null}_formatValue(t,i,s){return void 0===i?(void 0===s&&(s=this.formatter()),s.format(t)):i(t)}_formatPrice(t,i){return this._formatValue(t,this._localizationOptions.priceFormatter,i)}_formatPercentage(t,i){return this._formatValue(t,this._localizationOptions.percentageFormatter,i)}}class es{constructor(t,i){this._dataSources=[],this._overlaySourcesByScaleId=new Map,this._height=0,this._width=0,this._stretchFactor=1e3,this._cachedOrderedSources=null,this._destroyed=new b,this._timeScale=t,this._model=i,this._grid=new Ni(this);const s=i.options();this._leftPriceScale=this._createPriceScale("left",s.leftPriceScale),this._rightPriceScale=this._createPriceScale("right",s.rightPriceScale),this._leftPriceScale.modeChanged().subscribe(this._onPriceScaleModeChanged.bind(this,this._leftPriceScale),this),this._rightPriceScale.modeChanged().subscribe(this._onPriceScaleModeChanged.bind(this,this._rightPriceScale),this),this.applyScaleOptions(s)}applyScaleOptions(t){if(t.leftPriceScale&&this._leftPriceScale.applyOptions(t.leftPriceScale),t.rightPriceScale&&this._rightPriceScale.applyOptions(t.rightPriceScale),t.localization&&(this._leftPriceScale.updateFormatter(),this._rightPriceScale.updateFormatter()),t.overlayPriceScales){const i=Array.from(this._overlaySourcesByScaleId.values());for(const s of i){const i=r(s[0].priceScale());i.applyOptions(t.overlayPriceScales),t.localization&&i.updateFormatter()}}}priceScaleById(t){switch(t){case"left":return this._leftPriceScale;case"right":return this._rightPriceScale}return this._overlaySourcesByScaleId.has(t)?n(this._overlaySourcesByScaleId.get(t))[0].priceScale():null}destroy(){this.model().priceScalesOptionsChanged().unsubscribeAll(this),this._leftPriceScale.modeChanged().unsubscribeAll(this),this._rightPriceScale.modeChanged().unsubscribeAll(this),this._dataSources.forEach((t=>{t.destroy&&t.destroy()})),this._destroyed.fire()}stretchFactor(){return this._stretchFactor}setStretchFactor(t){this._stretchFactor=t}model(){return this._model}width(){return this._width}height(){return this._height}setWidth(t){this._width=t,this.updateAllSources()}setHeight(t){this._height=t,this._leftPriceScale.setHeight(t),this._rightPriceScale.setHeight(t),this._dataSources.forEach((i=>{if(this.isOverlay(i)){const s=i.priceScale();null!==s&&s.setHeight(t)}})),this.updateAllSources()}dataSources(){return this._dataSources}isOverlay(t){const i=t.priceScale();return null===i||this._leftPriceScale!==i&&this._rightPriceScale!==i}addDataSource(t,i,s){const e=void 0!==s?s:this._getZOrderMinMax().maxZOrder+1;this._insertDataSource(t,i,e)}removeDataSource(t){const i=this._dataSources.indexOf(t);h(-1!==i,"removeDataSource: invalid data source"),this._dataSources.splice(i,1);const s=r(t.priceScale()).id();if(this._overlaySourcesByScaleId.has(s)){const i=n(this._overlaySourcesByScaleId.get(s)),e=i.indexOf(t);-1!==e&&(i.splice(e,1),0===i.length&&this._overlaySourcesByScaleId.delete(s))}const e=t.priceScale();e&&e.dataSources().indexOf(t)>=0&&e.removeDataSource(t),null!==e&&(e.invalidateSourcesCache(),this.recalculatePriceScale(e)),this._cachedOrderedSources=null}priceScalePosition(t){return t===this._leftPriceScale?"left":t===this._rightPriceScale?"right":"overlay"}leftPriceScale(){return this._leftPriceScale}rightPriceScale(){return this._rightPriceScale}startScalePrice(t,i){t.startScale(i)}scalePriceTo(t,i){t.scaleTo(i),this.updateAllSources()}endScalePrice(t){t.endScale()}startScrollPrice(t,i){t.startScroll(i)}scrollPriceTo(t,i){t.scrollTo(i),this.updateAllSources()}endScrollPrice(t){t.endScroll()}updateAllSources(){this._dataSources.forEach((t=>{t.updateAllViews()}))}defaultPriceScale(){let t=null;return this._model.options().rightPriceScale.visible&&0!==this._rightPriceScale.dataSources().length?t=this._rightPriceScale:this._model.options().leftPriceScale.visible&&0!==this._leftPriceScale.dataSources().length?t=this._leftPriceScale:0!==this._dataSources.length&&(t=this._dataSources[0].priceScale()),null===t&&(t=this._rightPriceScale),t}defaultVisiblePriceScale(){let t=null;return this._model.options().rightPriceScale.visible?t=this._rightPriceScale:this._model.options().leftPriceScale.visible&&(t=this._leftPriceScale),t}recalculatePriceScale(t){null!==t&&t.isAutoScale()&&this._recalculatePriceScaleImpl(t)}resetPriceScale(t){const i=this._timeScale.visibleStrictRange();t.setMode({autoScale:!0}),null!==i&&t.recalculatePriceRange(i),this.updateAllSources()}momentaryAutoScale(){this._recalculatePriceScaleImpl(this._leftPriceScale),this._recalculatePriceScaleImpl(this._rightPriceScale)}recalculate(){this.recalculatePriceScale(this._leftPriceScale),this.recalculatePriceScale(this._rightPriceScale),this._dataSources.forEach((t=>{this.isOverlay(t)&&this.recalculatePriceScale(t.priceScale())})),this.updateAllSources(),this._model.lightUpdate()}orderedSources(){return null===this._cachedOrderedSources&&(this._cachedOrderedSources=Ji(this._dataSources)),this._cachedOrderedSources}onDestroyed(){return this._destroyed}grid(){return this._grid}_recalculatePriceScaleImpl(t){const i=t.sourcesForAutoScale();if(i&&i.length>0&&!this._timeScale.isEmpty()){const i=this._timeScale.visibleStrictRange();null!==i&&t.recalculatePriceRange(i)}t.updateAllViews()}_getZOrderMinMax(){const t=this.orderedSources();if(0===t.length)return{minZOrder:0,maxZOrder:0};let i=0,s=0;for(let e=0;e<t.length;e++){const h=t[e].zorder();null!==h&&(h<i&&(i=h),h>s&&(s=h))}return{minZOrder:i,maxZOrder:s}}_insertDataSource(t,i,s){let e=this.priceScaleById(i);if(null===e&&(e=this._createPriceScale(i,this._model.options().overlayPriceScales)),this._dataSources.push(t),!et(i)){const s=this._overlaySourcesByScaleId.get(i)||[];s.push(t),this._overlaySourcesByScaleId.set(i,s)}e.addDataSource(t),t.setPriceScale(e),t.setZorder(s),this.recalculatePriceScale(e),this._cachedOrderedSources=null}_onPriceScaleModeChanged(t,i,s){i.mode!==s.mode&&this._recalculatePriceScaleImpl(t)}_createPriceScale(t,i){const s=Object.assign({visible:!0,autoScale:!0},_(i)),e=new ss(t,s,this._model.options().layout,this._model.options().localization);return e.setHeight(this.height()),e}}const hs=t=>t.getUTCFullYear();function ns(t,i,s){return i.replace(/yyyy/g,(t=>rt(hs(t),4))(t)).replace(/yy/g,(t=>rt(hs(t)%100,2))(t)).replace(/MMMM/g,((t,i)=>new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:"long"}))(t,s)).replace(/MMM/g,((t,i)=>new Date(t.getUTCFullYear(),t.getUTCMonth(),1).toLocaleString(i,{month:"short"}))(t,s)).replace(/MM/g,(t=>rt((t=>t.getUTCMonth()+1)(t),2))(t)).replace(/dd/g,(t=>rt((t=>t.getUTCDate())(t),2))(t))}class rs{constructor(t="yyyy-MM-dd",i="default"){this._dateFormat=t,this._locale=i}format(t){return ns(t,this._dateFormat,this._locale)}}class os{constructor(t){this._formatStr=t||"%h:%m:%s"}format(t){return this._formatStr.replace("%h",rt(t.getUTCHours(),2)).replace("%m",rt(t.getUTCMinutes(),2)).replace("%s",rt(t.getUTCSeconds(),2))}}const ls={dateFormat:"yyyy-MM-dd",timeFormat:"%h:%m:%s",dateTimeSeparator:" ",locale:"default"};class as{constructor(t={}){const i=Object.assign(Object.assign({},ls),t);this._dateFormatter=new rs(i.dateFormat,i.locale),this._timeFormatter=new os(i.timeFormat),this._separator=i.dateTimeSeparator}format(t){return`${this._dateFormatter.format(t)}${this._separator}${this._timeFormatter.format(t)}`}}class us{constructor(t,i=50){this._actualSize=0,this._usageTick=1,this._oldestTick=1,this._cache=new Map,this._tick2Labels=new Map,this._format=t,this._maxSize=i}format(t){const i=t.time,s=void 0===i.businessDay?new Date(1e3*i.timestamp).getTime():new Date(Date.UTC(i.businessDay.year,i.businessDay.month-1,i.businessDay.day)).getTime(),e=this._cache.get(s);if(void 0!==e)return e.string;if(this._actualSize===this._maxSize){const t=this._tick2Labels.get(this._oldestTick);this._tick2Labels.delete(this._oldestTick),this._cache.delete(n(t)),this._oldestTick++,this._actualSize--}const h=this._format(t);return this._cache.set(s,{string:h,tick:this._usageTick}),this._tick2Labels.set(this._usageTick,s),this._actualSize++,this._usageTick++,h}}class cs{constructor(t,i){h(t<=i,"right should be >= left"),this._left=t,this._right=i}left(){return this._left}right(){return this._right}count(){return this._right-this._left+1}contains(t){return this._left<=t&&t<=this._right}equals(t){return this._left===t.left()&&this._right===t.right()}}function ds(t,i){return null===t||null===i?t===i:t.equals(i)}class fs{constructor(){this._marksByWeight=new Map,this._cache=null}setTimeScalePoints(t,i){this._removeMarksSinceIndex(i),this._cache=null;for(let s=i;s<t.length;++s){const i=t[s];let e=this._marksByWeight.get(i.timeWeight);void 0===e&&(e=[],this._marksByWeight.set(i.timeWeight,e)),e.push({index:s,time:i.time,weight:i.timeWeight,originalTime:i.originalTime})}}build(t,i){const s=Math.ceil(i/t);return null!==this._cache&&this._cache.maxIndexesPerMark===s||(this._cache={marks:this._buildMarksImpl(s),maxIndexesPerMark:s}),this._cache.marks}_removeMarksSinceIndex(t){if(0===t)return void this._marksByWeight.clear();const i=[];this._marksByWeight.forEach(((s,e)=>{t<=s[0].index?i.push(e):s.splice(St(s,t,(i=>i.index<t)),1/0)}));for(const t of i)this._marksByWeight.delete(t)}_buildMarksImpl(t){let i=[];for(const s of Array.from(this._marksByWeight.keys()).sort(((t,i)=>i-t))){if(!this._marksByWeight.get(s))continue;const e=i;i=[];const h=e.length;let r=0;const o=n(this._marksByWeight.get(s)),l=o.length;let a=1/0,u=-1/0;for(let s=0;s<l;s++){const n=o[s],l=n.index;for(;r<h;){const t=e[r],s=t.index;if(!(s<l)){a=s;break}r++,i.push(t),u=s,a=1/0}a-l>=t&&l-u>=t&&(i.push(n),u=l)}for(;r<h;r++)i.push(e[r])}return i}}class ps{constructor(t){this._logicalRange=t}strictRange(){return null===this._logicalRange?null:new cs(Math.floor(this._logicalRange.left()),Math.ceil(this._logicalRange.right()))}logicalRange(){return this._logicalRange}static invalid(){return new ps(null)}}var ms,vs,bs,gs,ws,Ms,Ss,xs;!function(t){t[t.DefaultAnimationDuration=400]="DefaultAnimationDuration",t[t.MinVisibleBarsCount=2]="MinVisibleBarsCount"}(ms||(ms={})),function(t){t[t.Year=0]="Year",t[t.Month=1]="Month",t[t.DayOfMonth=2]="DayOfMonth",t[t.Time=3]="Time",t[t.TimeWithSeconds=4]="TimeWithSeconds"}(vs||(vs={}));class _s{constructor(t,i,s){this._width=0,this._baseIndexOrNull=null,this._points=[],this._scrollStartPoint=null,this._scaleStartPoint=null,this._tickMarks=new fs,this._formattedByWeight=new Map,this._visibleRange=ps.invalid(),this._visibleRangeInvalidated=!0,this._visibleBarsChanged=new b,this._logicalRangeChanged=new b,this._optionsApplied=new b,this._commonTransitionStartState=null,this._timeMarksCache=null,this._labels=[],this._options=i,this._localizationOptions=s,this._rightOffset=i.rightOffset,this._barSpacing=i.barSpacing,this._model=t,this._updateDateTimeFormatter()}options(){return this._options}applyLocalizationOptions(t){g(this._localizationOptions,t),this._invalidateTickMarks(),this._updateDateTimeFormatter()}applyOptions(t,i){var s;g(this._options,t),this._options.fixLeftEdge&&this._doFixLeftEdge(),this._options.fixRightEdge&&this._doFixRightEdge(),void 0!==t.barSpacing&&this._model.setBarSpacing(t.barSpacing),void 0!==t.rightOffset&&this._model.setRightOffset(t.rightOffset),void 0!==t.minBarSpacing&&this._model.setBarSpacing(null!==(s=t.barSpacing)&&void 0!==s?s:this._barSpacing),this._invalidateTickMarks(),this._updateDateTimeFormatter(),this._optionsApplied.fire()}indexToTime(t){var i,s;return null!==(s=null===(i=this._points[t])||void 0===i?void 0:i.time)&&void 0!==s?s:null}indexToTimeScalePoint(t){var i;return null!==(i=this._points[t])&&void 0!==i?i:null}timeToIndex(t,i){if(this._points.length<1)return null;if(t.timestamp>this._points[this._points.length-1].time.timestamp)return i?this._points.length-1:null;const s=St(this._points,t.timestamp,((t,i)=>t.time.timestamp<i));return t.timestamp<this._points[s].time.timestamp?i?s:null:s}isEmpty(){return 0===this._width||0===this._points.length||null===this._baseIndexOrNull}hasPoints(){return this._points.length>0}visibleStrictRange(){return this._updateVisibleRange(),this._visibleRange.strictRange()}visibleLogicalRange(){return this._updateVisibleRange(),this._visibleRange.logicalRange()}visibleTimeRange(){const t=this.visibleStrictRange();if(null===t)return null;const i={from:t.left(),to:t.right()};return this.timeRangeForLogicalRange(i)}timeRangeForLogicalRange(t){const i=Math.round(t.from),s=Math.round(t.to),e=r(this._firstIndex()),h=r(this._lastIndex());return{from:r(this.indexToTime(Math.max(e,i))),to:r(this.indexToTime(Math.min(h,s)))}}logicalRangeForTimeRange(t){return{from:r(this.timeToIndex(t.from,!0)),to:r(this.timeToIndex(t.to,!0))}}width(){return this._width}setWidth(t){if(!isFinite(t)||t<=0)return;if(this._width===t)return;const i=this.visibleLogicalRange(),s=this._width;if(this._width=t,this._visibleRangeInvalidated=!0,this._options.lockVisibleTimeRangeOnResize&&0!==s){const i=this._barSpacing*t/s;this._barSpacing=i}if(this._options.fixLeftEdge&&null!==i&&i.left()<=0){const i=s-t;this._rightOffset-=Math.round(i/this._barSpacing)+1,this._visibleRangeInvalidated=!0}this._correctBarSpacing(),this._correctOffset()}indexToCoordinate(t){if(this.isEmpty()||!M(t))return 0;const i=this.baseIndex()+this._rightOffset-t;return this._width-(i+.5)*this._barSpacing-1}indexesToCoordinates(t,i){const s=this.baseIndex(),e=void 0===i?0:i.from,h=void 0===i?t.length:i.to;for(let i=e;i<h;i++){const e=t[i].time,h=s+this._rightOffset-e,n=this._width-(h+.5)*this._barSpacing-1;t[i].x=n}}coordinateToIndex(t){return Math.ceil(this._coordinateToFloatIndex(t))}setRightOffset(t){this._visibleRangeInvalidated=!0,this._rightOffset=t,this._correctOffset(),this._model.recalculateAllPanes(),this._model.lightUpdate()}barSpacing(){return this._barSpacing}setBarSpacing(t){this._setBarSpacing(t),this._correctOffset(),this._model.recalculateAllPanes(),this._model.lightUpdate()}rightOffset(){return this._rightOffset}marks(){if(this.isEmpty())return null;if(null!==this._timeMarksCache)return this._timeMarksCache;const t=this._barSpacing,i=5*(this._model.options().layout.fontSize+4),s=Math.round(i/t),e=r(this.visibleStrictRange()),h=Math.max(e.left(),e.left()-s),n=Math.max(e.right(),e.right()-s),o=this._tickMarks.build(t,i),l=this._firstIndex()+s,a=this._lastIndex()-s,u=this._isAllScalingAndScrollingDisabled(),c=this._options.fixLeftEdge||u,d=this._options.fixRightEdge||u;let f=0;for(const t of o){if(!(h<=t.index&&t.index<=n))continue;let s;f<this._labels.length?(s=this._labels[f],s.coord=this.indexToCoordinate(t.index),s.label=this._formatLabel(t),s.weight=t.weight):(s={needAlignCoordinate:!1,coord:this.indexToCoordinate(t.index),label:this._formatLabel(t),weight:t.weight},this._labels.push(s)),this._barSpacing>i/2&&!u?s.needAlignCoordinate=!1:s.needAlignCoordinate=c&&t.index<=l||d&&t.index>=a,f++}return this._labels.length=f,this._timeMarksCache=this._labels,this._labels}restoreDefault(){this._visibleRangeInvalidated=!0,this.setBarSpacing(this._options.barSpacing),this.setRightOffset(this._options.rightOffset)}setBaseIndex(t){this._visibleRangeInvalidated=!0,this._baseIndexOrNull=t,this._correctOffset(),this._doFixLeftEdge()}zoom(t,i){const s=this._coordinateToFloatIndex(t),e=this.barSpacing(),h=e+i*(e/10);this.setBarSpacing(h),this._options.rightBarStaysOnScroll||this.setRightOffset(this.rightOffset()+(s-this._coordinateToFloatIndex(t)))}startScale(t){this._scrollStartPoint&&this.endScroll(),null===this._scaleStartPoint&&null===this._commonTransitionStartState&&(this.isEmpty()||(this._scaleStartPoint=t,this._saveCommonTransitionsStartState()))}scaleTo(t){if(null===this._commonTransitionStartState)return;const i=Bt(this._width-t,0,this._width),s=Bt(this._width-r(this._scaleStartPoint),0,this._width);0!==i&&0!==s&&this.setBarSpacing(this._commonTransitionStartState.barSpacing*i/s)}endScale(){null!==this._scaleStartPoint&&(this._scaleStartPoint=null,this._clearCommonTransitionsStartState())}startScroll(t){null===this._scrollStartPoint&&null===this._commonTransitionStartState&&(this.isEmpty()||(this._scrollStartPoint=t,this._saveCommonTransitionsStartState()))}scrollTo(t){if(null===this._scrollStartPoint)return;const i=(this._scrollStartPoint-t)/this.barSpacing();this._rightOffset=r(this._commonTransitionStartState).rightOffset+i,this._visibleRangeInvalidated=!0,this._correctOffset()}endScroll(){null!==this._scrollStartPoint&&(this._scrollStartPoint=null,this._clearCommonTransitionsStartState())}scrollToRealTime(){this.scrollToOffsetAnimated(this._options.rightOffset)}scrollToOffsetAnimated(t,i=400){if(!isFinite(t))throw new RangeError("offset is required and must be finite number");if(!isFinite(i)||i<=0)throw new RangeError("animationDuration (optional) must be finite positive number");const s=this._rightOffset,e=performance.now();this._model.setTimeScaleAnimation({finished:t=>(t-e)/i>=1,getPosition:h=>{const n=(h-e)/i;return n>=1?t:s+(t-s)*n}})}update(t,i){this._visibleRangeInvalidated=!0,this._points=t,this._tickMarks.setTimeScalePoints(t,i),this._correctOffset()}visibleBarsChanged(){return this._visibleBarsChanged}logicalRangeChanged(){return this._logicalRangeChanged}optionsApplied(){return this._optionsApplied}baseIndex(){return this._baseIndexOrNull||0}setVisibleRange(t){const i=t.count();this._setBarSpacing(this._width/i),this._rightOffset=t.right()-this.baseIndex(),this._correctOffset(),this._visibleRangeInvalidated=!0,this._model.recalculateAllPanes(),this._model.lightUpdate()}fitContent(){const t=this._firstIndex(),i=this._lastIndex();null!==t&&null!==i&&this.setVisibleRange(new cs(t,i+this._options.rightOffset))}setLogicalRange(t){const i=new cs(t.from,t.to);this.setVisibleRange(i)}formatDateTime(t){return void 0!==this._localizationOptions.timeFormatter?this._localizationOptions.timeFormatter(t.originalTime):this._dateTimeFormatter.format(new Date(1e3*t.time.timestamp))}_isAllScalingAndScrollingDisabled(){const{handleScroll:t,handleScale:i}=this._model.options();return!(t.horzTouchDrag||t.mouseWheel||t.pressedMouseMove||t.vertTouchDrag||i.axisDoubleClickReset.time||i.axisPressedMouseMove.time||i.mouseWheel||i.pinch)}_firstIndex(){return 0===this._points.length?null:0}_lastIndex(){return 0===this._points.length?null:this._points.length-1}_rightOffsetForCoordinate(t){return(this._width-1-t)/this._barSpacing}_coordinateToFloatIndex(t){const i=this._rightOffsetForCoordinate(t),s=this.baseIndex()+this._rightOffset-i;return Math.round(1e6*s)/1e6}_setBarSpacing(t){const i=this._barSpacing;this._barSpacing=t,this._correctBarSpacing(),i!==this._barSpacing&&(this._visibleRangeInvalidated=!0,this._resetTimeMarksCache())}_updateVisibleRange(){if(!this._visibleRangeInvalidated)return;if(this._visibleRangeInvalidated=!1,this.isEmpty())return void this._setVisibleRange(ps.invalid());const t=this.baseIndex(),i=this._width/this._barSpacing,s=this._rightOffset+t,e=new cs(s-i+1,s);this._setVisibleRange(new ps(e))}_correctBarSpacing(){const t=this._minBarSpacing();if(this._barSpacing<t&&(this._barSpacing=t,this._visibleRangeInvalidated=!0),0!==this._width){const t=.5*this._width;this._barSpacing>t&&(this._barSpacing=t,this._visibleRangeInvalidated=!0)}}_minBarSpacing(){return this._options.fixLeftEdge&&this._options.fixRightEdge&&0!==this._points.length?this._width/this._points.length:this._options.minBarSpacing}_correctOffset(){const t=this._maxRightOffset();this._rightOffset>t&&(this._rightOffset=t,this._visibleRangeInvalidated=!0);const i=this._minRightOffset();null!==i&&this._rightOffset<i&&(this._rightOffset=i,this._visibleRangeInvalidated=!0)}_minRightOffset(){const t=this._firstIndex(),i=this._baseIndexOrNull;if(null===t||null===i)return null;return t-i-1+(this._options.fixLeftEdge?this._width/this._barSpacing:Math.min(2,this._points.length))}_maxRightOffset(){return this._options.fixRightEdge?0:this._width/this._barSpacing-Math.min(2,this._points.length)}_saveCommonTransitionsStartState(){this._commonTransitionStartState={barSpacing:this.barSpacing(),rightOffset:this.rightOffset()}}_clearCommonTransitionsStartState(){this._commonTransitionStartState=null}_formatLabel(t){let i=this._formattedByWeight.get(t.weight);return void 0===i&&(i=new us((t=>this._formatLabelImpl(t))),this._formattedByWeight.set(t.weight,i)),i.format(t)}_formatLabelImpl(t){const i=function(t,i,s){switch(t){case 0:case 10:return i?s?4:3:2;case 20:case 21:case 22:case 30:case 31:case 32:case 33:return i?3:2;case 50:return 2;case 60:return 1;case 70:return 0}}(t.weight,this._options.timeVisible,this._options.secondsVisible);if(void 0!==this._options.tickMarkFormatter){const s=this._options.tickMarkFormatter(t.originalTime,i,this._localizationOptions.locale);if(null!==s)return s}return function(t,i,s){const e={};switch(i){case 0:e.year="numeric";break;case 1:e.month="short";break;case 2:e.day="numeric";break;case 3:e.hour12=!1,e.hour="2-digit",e.minute="2-digit";break;case 4:e.hour12=!1,e.hour="2-digit",e.minute="2-digit",e.second="2-digit"}const h=void 0===t.businessDay?new Date(1e3*t.timestamp):new Date(Date.UTC(t.businessDay.year,t.businessDay.month-1,t.businessDay.day));return new Date(h.getUTCFullYear(),h.getUTCMonth(),h.getUTCDate(),h.getUTCHours(),h.getUTCMinutes(),h.getUTCSeconds(),h.getUTCMilliseconds()).toLocaleString(s,e)}(t.time,i,this._localizationOptions.locale)}_setVisibleRange(t){const i=this._visibleRange;this._visibleRange=t,ds(i.strictRange(),this._visibleRange.strictRange())||this._visibleBarsChanged.fire(),ds(i.logicalRange(),this._visibleRange.logicalRange())||this._logicalRangeChanged.fire(),this._resetTimeMarksCache()}_resetTimeMarksCache(){this._timeMarksCache=null}_invalidateTickMarks(){this._resetTimeMarksCache(),this._formattedByWeight.clear()}_updateDateTimeFormatter(){const t=this._localizationOptions.dateFormat;this._options.timeVisible?this._dateTimeFormatter=new as({dateFormat:t,timeFormat:this._options.secondsVisible?"%h:%m:%s":"%h:%m",dateTimeSeparator:"   ",locale:this._localizationOptions.locale}):this._dateTimeFormatter=new rs(t,this._localizationOptions.locale)}_doFixLeftEdge(){if(!this._options.fixLeftEdge)return;const t=this._firstIndex();if(null===t)return;const i=this.visibleStrictRange();if(null===i)return;const s=i.left()-t;if(s<0){const t=this._rightOffset-s-1;this.setRightOffset(t)}this._correctBarSpacing()}_doFixRightEdge(){this._correctOffset(),this._correctBarSpacing()}}class ys extends A{constructor(t){super(),this._metricsCache=new Map,this._data=t}_drawImpl(t){}_drawBackgroundImpl(t){if(!this._data.visible)return;const{context:i,mediaSize:s}=t;let e=0;for(const t of this._data.lines){if(0===t.text.length)continue;i.font=t.font;const h=this._metrics(i,t.text);h>s.width?t.zoom=s.width/h:t.zoom=1,e+=t.lineHeight*t.zoom}let h=0;switch(this._data.vertAlign){case"top":h=0;break;case"center":h=Math.max((s.height-e)/2,0);break;case"bottom":h=Math.max(s.height-e,0)}i.fillStyle=this._data.color;for(const t of this._data.lines){i.save();let e=0;switch(this._data.horzAlign){case"left":i.textAlign="left",e=t.lineHeight/2;break;case"center":i.textAlign="center",e=s.width/2;break;case"right":i.textAlign="right",e=s.width-1-t.lineHeight/2}i.translate(e,h),i.textBaseline="top",i.font=t.font,i.scale(t.zoom,t.zoom),i.fillText(t.text,0,t.vertOffset),i.restore(),h+=t.lineHeight*t.zoom}}_metrics(t,i){const s=this._fontCache(t.font);let e=s.get(i);return void 0===e&&(e=t.measureText(i).width,s.set(i,e)),e}_fontCache(t){let i=this._metricsCache.get(t);return void 0===i&&(i=new Map,this._metricsCache.set(t,i)),i}}class ks{constructor(t){this._invalidated=!0,this._rendererData={visible:!1,color:"",lines:[],vertAlign:"center",horzAlign:"center"},this._renderer=new ys(this._rendererData),this._source=t}update(){this._invalidated=!0}renderer(){return this._invalidated&&(this._updateImpl(),this._invalidated=!1),this._renderer}_updateImpl(){const t=this._source.options(),i=this._rendererData;i.visible=t.visible,i.visible&&(i.color=t.color,i.horzAlign=t.horzAlign,i.vertAlign=t.vertAlign,i.lines=[{text:t.text,font:T(t.fontSize,t.fontFamily,t.fontStyle),lineHeight:1.2*t.fontSize,vertOffset:0,zoom:0}])}}class Cs extends G{constructor(t,i){super(),this._options=i,this._paneView=new ks(this)}priceAxisViews(){return[]}paneViews(){return[this._paneView]}options(){return this._options}updateAllViews(){this._paneView.update()}}!function(t){t[t.Top=0]="Top",t[t.Bottom=1]="Bottom"}(bs||(bs={})),function(t){t[t.OnTouchEnd=0]="OnTouchEnd",t[t.OnNextTap=1]="OnNextTap"}(gs||(gs={}));class Ts{constructor(t,i){this._panes=[],this._serieses=[],this._width=0,this._hoveredSource=null,this._priceScalesOptionsChanged=new b,this._crosshairMoved=new b,this._gradientColorsCache=null,this._invalidateHandler=t,this._options=i,this._rendererOptionsProvider=new R(this),this._timeScale=new _s(this,i.timeScale,this._options.localization),this._crosshair=new st(this,i.crosshair),this._magnet=new Li(i.crosshair),this._watermark=new Cs(this,i.watermark),this.createPane(),this._panes[0].setStretchFactor(2e3),this._backgroundTopColor=this._getBackgroundColor(0),this._backgroundBottomColor=this._getBackgroundColor(1)}fullUpdate(){this._invalidate(ht.full())}lightUpdate(){this._invalidate(ht.light())}cursorUpdate(){this._invalidate(new ht(1))}updateSource(t){const i=this._invalidationMaskForSource(t);this._invalidate(i)}hoveredSource(){return this._hoveredSource}setHoveredSource(t){const i=this._hoveredSource;this._hoveredSource=t,null!==i&&this.updateSource(i.source),null!==t&&this.updateSource(t.source)}options(){return this._options}applyOptions(t){g(this._options,t),this._panes.forEach((i=>i.applyScaleOptions(t))),void 0!==t.timeScale&&this._timeScale.applyOptions(t.timeScale),void 0!==t.localization&&this._timeScale.applyLocalizationOptions(t.localization),(t.leftPriceScale||t.rightPriceScale)&&this._priceScalesOptionsChanged.fire(),this._backgroundTopColor=this._getBackgroundColor(0),this._backgroundBottomColor=this._getBackgroundColor(1),this.fullUpdate()}applyPriceScaleOptions(t,i){if("left"===t)return void this.applyOptions({leftPriceScale:i});if("right"===t)return void this.applyOptions({rightPriceScale:i});const s=this.findPriceScale(t);null!==s&&(s.priceScale.applyOptions(i),this._priceScalesOptionsChanged.fire())}findPriceScale(t){for(const i of this._panes){const s=i.priceScaleById(t);if(null!==s)return{pane:i,priceScale:s}}return null}timeScale(){return this._timeScale}panes(){return this._panes}watermarkSource(){return this._watermark}crosshairSource(){return this._crosshair}crosshairMoved(){return this._crosshairMoved}setPaneHeight(t,i){t.setHeight(i),this.recalculateAllPanes()}setWidth(t){this._width=t,this._timeScale.setWidth(this._width),this._panes.forEach((i=>i.setWidth(t))),this.recalculateAllPanes()}createPane(t){const i=new es(this._timeScale,this);void 0!==t?this._panes.splice(t,0,i):this._panes.push(i);const s=void 0===t?this._panes.length-1:t,e=ht.full();return e.invalidatePane(s,{level:0,autoScale:!0}),this._invalidate(e),i}startScalePrice(t,i,s){t.startScalePrice(i,s)}scalePriceTo(t,i,s){t.scalePriceTo(i,s),this.updateCrosshair(),this._invalidate(this._paneInvalidationMask(t,2))}endScalePrice(t,i){t.endScalePrice(i),this._invalidate(this._paneInvalidationMask(t,2))}startScrollPrice(t,i,s){i.isAutoScale()||t.startScrollPrice(i,s)}scrollPriceTo(t,i,s){i.isAutoScale()||(t.scrollPriceTo(i,s),this.updateCrosshair(),this._invalidate(this._paneInvalidationMask(t,2)))}endScrollPrice(t,i){i.isAutoScale()||(t.endScrollPrice(i),this._invalidate(this._paneInvalidationMask(t,2)))}resetPriceScale(t,i){t.resetPriceScale(i),this._invalidate(this._paneInvalidationMask(t,2))}startScaleTime(t){this._timeScale.startScale(t)}zoomTime(t,i){const s=this.timeScale();if(s.isEmpty()||0===i)return;const e=s.width();t=Math.max(1,Math.min(t,e)),s.zoom(t,i),this.recalculateAllPanes()}scrollChart(t){this.startScrollTime(0),this.scrollTimeTo(t),this.endScrollTime()}scaleTimeTo(t){this._timeScale.scaleTo(t),this.recalculateAllPanes()}endScaleTime(){this._timeScale.endScale(),this.lightUpdate()}startScrollTime(t){this._timeScale.startScroll(t)}scrollTimeTo(t){this._timeScale.scrollTo(t),this.recalculateAllPanes()}endScrollTime(){this._timeScale.endScroll(),this.lightUpdate()}serieses(){return this._serieses}setAndSaveCurrentPosition(t,i,s,e){this._crosshair.saveOriginCoord(t,i);let h=NaN,n=this._timeScale.coordinateToIndex(t);const r=this._timeScale.visibleStrictRange();null!==r&&(n=Math.min(Math.max(r.left(),n),r.right()));const o=e.defaultPriceScale(),l=o.firstValue();null!==l&&(h=o.coordinateToPrice(i,l)),h=this._magnet.align(h,n,e),this._crosshair.setPosition(n,h,e),this.cursorUpdate(),this._crosshairMoved.fire(this._crosshair.appliedIndex(),{x:t,y:i},s)}setAndSaveCurrentPositionFire(t,i,s,e){this._crosshair.saveOriginCoord(t,i);let h=NaN,n=this._timeScale.coordinateToIndex(t);const r=this._timeScale.visibleStrictRange();null!==r&&(n=Math.min(Math.max(r.left(),n),r.right()));const o=e.defaultPriceScale(),l=o.firstValue();null!==l&&(h=o.coordinateToPrice(i,l)),h=this._magnet.align(h,n,e),this._crosshair.setPosition(n,h,e),this.cursorUpdate(),s&&this._crosshairMoved.fire(this._crosshair.appliedIndex(),{x:t,y:i},null)}clearCurrentPosition(){this.crosshairSource().clearPosition(),this.cursorUpdate(),this._crosshairMoved.fire(null,null,null)}updateCrosshair(){const t=this._crosshair.pane();if(null!==t){const i=this._crosshair.originCoordX(),s=this._crosshair.originCoordY();this.setAndSaveCurrentPosition(i,s,null,t)}this._crosshair.updateAllViews()}updateTimeScale(t,i,s){const e=this._timeScale.indexToTime(0);void 0!==i&&void 0!==s&&this._timeScale.update(i,s);const h=this._timeScale.indexToTime(0),n=this._timeScale.baseIndex(),r=this._timeScale.visibleStrictRange();if(null!==r&&null!==e&&null!==h){const i=r.contains(n),s=e.timestamp>h.timestamp,o=null!==t&&t>n&&!s,l=i&&this._timeScale.options().shiftVisibleRangeOnNewBar;if(o&&!l){const i=t-n;this._timeScale.setRightOffset(this._timeScale.rightOffset()-i)}}this._timeScale.setBaseIndex(t)}recalculatePane(t){null!==t&&t.recalculate()}paneForSource(t){const i=this._panes.find((i=>i.orderedSources().includes(t)));return void 0===i?null:i}recalculateAllPanes(){this._watermark.updateAllViews(),this._panes.forEach((t=>t.recalculate())),this.updateCrosshair()}destroy(){this._panes.forEach((t=>t.destroy())),this._panes.length=0,this._options.localization.priceFormatter=void 0,this._options.localization.percentageFormatter=void 0,this._options.localization.timeFormatter=void 0}rendererOptionsProvider(){return this._rendererOptionsProvider}priceAxisRendererOptions(){return this._rendererOptionsProvider.options()}priceScalesOptionsChanged(){return this._priceScalesOptionsChanged}createSeries(t,i){const s=this._panes[0],e=this._createSeries(i,t,s);return this._serieses.push(e),1===this._serieses.length?this.fullUpdate():this.lightUpdate(),e}removeSeries(t){const i=this.paneForSource(t),s=this._serieses.indexOf(t);h(-1!==s,"Series not found"),this._serieses.splice(s,1),r(i).removeDataSource(t),t.destroy&&t.destroy()}moveSeriesToScale(t,i){const s=r(this.paneForSource(t));s.removeDataSource(t);const e=this.findPriceScale(i);if(null===e){const e=t.zorder();s.addDataSource(t,i,e)}else{const h=e.pane===s?t.zorder():void 0;e.pane.addDataSource(t,i,h)}}fitContent(){const t=ht.light();t.setFitContent(),this._invalidate(t)}setTargetLogicalRange(t){const i=ht.light();i.applyRange(t),this._invalidate(i)}resetTimeScale(){const t=ht.light();t.resetTimeScale(),this._invalidate(t)}setBarSpacing(t){const i=ht.light();i.setBarSpacing(t),this._invalidate(i)}setRightOffset(t){const i=ht.light();i.setRightOffset(t),this._invalidate(i)}setTimeScaleAnimation(t){const i=ht.light();i.setTimeScaleAnimation(t),this._invalidate(i)}stopTimeScaleAnimation(){const t=ht.light();t.stopTimeScaleAnimation(),this._invalidate(t)}defaultVisiblePriceScaleId(){return this._options.rightPriceScale.visible?"right":"left"}backgroundBottomColor(){return this._backgroundBottomColor}backgroundTopColor(){return this._backgroundTopColor}backgroundColorAtYPercentFromTop(t){const i=this._backgroundBottomColor,s=this._backgroundTopColor;if(i===s)return i;if(t=Math.max(0,Math.min(100,Math.round(100*t))),null===this._gradientColorsCache||this._gradientColorsCache.topColor!==s||this._gradientColorsCache.bottomColor!==i)this._gradientColorsCache={topColor:s,bottomColor:i,colors:new Map};else{const i=this._gradientColorsCache.colors.get(t);if(void 0!==i)return i}const e=function(t,i,s){const[e,h,n,r]=m(t),[o,l,c,d]=m(i),f=[a(e+s*(o-e)),a(h+s*(l-h)),a(n+s*(c-n)),u(r+s*(d-r))];return`rgba(${f[0]}, ${f[1]}, ${f[2]}, ${f[3]})`}(s,i,t/100);return this._gradientColorsCache.colors.set(t,e),e}_paneInvalidationMask(t,i){const s=new ht(i);if(null!==t){const e=this._panes.indexOf(t);s.invalidatePane(e,{level:i})}return s}_invalidationMaskForSource(t,i){return void 0===i&&(i=2),this._paneInvalidationMask(this.paneForSource(t),i)}_invalidate(t){this._invalidateHandler&&this._invalidateHandler(t),this._panes.forEach((t=>t.grid().paneView().update()))}_createSeries(t,i,s){const e=new Bi(this,t,i),h=void 0!==t.priceScaleId?t.priceScaleId:this.defaultVisiblePriceScaleId();return s.addDataSource(e,h),et(h)||e.applyOptions(t),e}_getBackgroundColor(t){const i=this._options.layout;return"gradient"===i.background.type?0===t?i.background.topColor:i.background.bottomColor:i.background.color}}function Ps(t){return!w(t)&&!S(t)}function Rs(t){return w(t)}function Ds(t){var i=t.width,s=t.height;if(i<0)throw new Error("Negative width is not allowed for Size");if(s<0)throw new Error("Negative height is not allowed for Size");return{width:i,height:s}}function As(t,i){return t.width===i.width&&t.height===i.height}!function(t){t[t.Disabled=0]="Disabled",t[t.Continuous=1]="Continuous",t[t.OnDataUpdate=2]="OnDataUpdate"}(ws||(ws={})),function(t){t[t.LastPriceAndPercentageValue=0]="LastPriceAndPercentageValue",t[t.LastValueAccordingToScale=1]="LastValueAccordingToScale"}(Ms||(Ms={})),function(t){t[t.LastBar=0]="LastBar",t[t.LastVisible=1]="LastVisible"}(Ss||(Ss={})),function(t){t.Solid="solid",t.VerticalGradient="gradient"}(xs||(xs={}));var Es=function(){function t(t){var i=this;this._resolutionListener=function(){return i._onResolutionChanged()},this._resolutionMediaQueryList=null,this._observers=[],this._window=t,this._installResolutionListener()}return t.prototype.dispose=function(){this._uninstallResolutionListener(),this._window=null},Object.defineProperty(t.prototype,"value",{get:function(){return this._window.devicePixelRatio},enumerable:!1,configurable:!0}),t.prototype.subscribe=function(t){var i=this,s={next:t};return this._observers.push(s),{unsubscribe:function(){i._observers=i._observers.filter((function(t){return t!==s}))}}},t.prototype._installResolutionListener=function(){if(null!==this._resolutionMediaQueryList)throw new Error("Resolution listener is already installed");var t=this._window.devicePixelRatio;this._resolutionMediaQueryList=this._window.matchMedia("all and (resolution: ".concat(t,"dppx)")),this._resolutionMediaQueryList.addListener(this._resolutionListener)},t.prototype._uninstallResolutionListener=function(){null!==this._resolutionMediaQueryList&&(this._resolutionMediaQueryList.removeListener(this._resolutionListener),this._resolutionMediaQueryList=null)},t.prototype._reinstallResolutionListener=function(){this._uninstallResolutionListener(),this._installResolutionListener()},t.prototype._onResolutionChanged=function(){var t=this;this._observers.forEach((function(i){return i.next(t._window.devicePixelRatio)})),this._reinstallResolutionListener()},t}();var Os=function(){function t(t,i,s){var e;this._canvasElement=null,this._bitmapSizeChangedListeners=[],this._suggestedBitmapSize=null,this._suggestedBitmapSizeChangedListeners=[],this._devicePixelRatioObservable=null,this._canvasElementResizeObserver=null,this._canvasElement=t,this._canvasElementClientSize=Ds({width:this._canvasElement.clientWidth,height:this._canvasElement.clientHeight}),this._transformBitmapSize=null!=i?i:function(t){return t},this._allowResizeObserver=null===(e=null==s?void 0:s.allowResizeObserver)||void 0===e||e,this._chooseAndInitObserver()}return t.prototype.dispose=function(){var t,i;if(null===this._canvasElement)throw new Error("Object is disposed");null===(t=this._canvasElementResizeObserver)||void 0===t||t.disconnect(),this._canvasElementResizeObserver=null,null===(i=this._devicePixelRatioObservable)||void 0===i||i.dispose(),this._devicePixelRatioObservable=null,this._suggestedBitmapSizeChangedListeners.length=0,this._bitmapSizeChangedListeners.length=0,this._canvasElement=null},Object.defineProperty(t.prototype,"canvasElement",{get:function(){if(null===this._canvasElement)throw new Error("Object is disposed");return this._canvasElement},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"canvasElementClientSize",{get:function(){return this._canvasElementClientSize},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"bitmapSize",{get:function(){return Ds({width:this.canvasElement.width,height:this.canvasElement.height})},enumerable:!1,configurable:!0}),t.prototype.resizeCanvasElement=function(t){this._canvasElementClientSize=Ds(t),this.canvasElement.style.width="".concat(this._canvasElementClientSize.width,"px"),this.canvasElement.style.height="".concat(this._canvasElementClientSize.height,"px"),this._invalidateBitmapSize()},t.prototype.subscribeBitmapSizeChanged=function(t){this._bitmapSizeChangedListeners.push(t)},t.prototype.unsubscribeBitmapSizeChanged=function(t){this._bitmapSizeChangedListeners=this._bitmapSizeChangedListeners.filter((function(i){return i!==t}))},Object.defineProperty(t.prototype,"suggestedBitmapSize",{get:function(){return this._suggestedBitmapSize},enumerable:!1,configurable:!0}),t.prototype.subscribeSuggestedBitmapSizeChanged=function(t){this._suggestedBitmapSizeChangedListeners.push(t)},t.prototype.unsubscribeSuggestedBitmapSizeChanged=function(t){this._suggestedBitmapSizeChangedListeners=this._suggestedBitmapSizeChangedListeners.filter((function(i){return i!==t}))},t.prototype.applySuggestedBitmapSize=function(){if(null!==this._suggestedBitmapSize){var t=this._suggestedBitmapSize;this._suggestedBitmapSize=null,this._resizeBitmap(t),this._emitSuggestedBitmapSizeChanged(t,this._suggestedBitmapSize)}},t.prototype._resizeBitmap=function(t){var i=this.bitmapSize;As(i,t)||(this.canvasElement.width=t.width,this.canvasElement.height=t.height,this._emitBitmapSizeChanged(i,t))},t.prototype._emitBitmapSizeChanged=function(t,i){var s=this;this._bitmapSizeChangedListeners.forEach((function(e){return e.call(s,t,i)}))},t.prototype._suggestNewBitmapSize=function(t){var i=this._suggestedBitmapSize,s=Ds(this._transformBitmapSize(t,this._canvasElementClientSize)),e=As(this.bitmapSize,s)?null:s;null===i&&null===e||null!==i&&null!==e&&As(i,e)||(this._suggestedBitmapSize=e,this._emitSuggestedBitmapSizeChanged(i,e))},t.prototype._emitSuggestedBitmapSizeChanged=function(t,i){var s=this;this._suggestedBitmapSizeChangedListeners.forEach((function(e){return e.call(s,t,i)}))},t.prototype._chooseAndInitObserver=function(){var t=this;this._allowResizeObserver?new Promise((function(t){var i=new ResizeObserver((function(s){t(s.every((function(t){return"devicePixelContentBoxSize"in t}))),i.disconnect()}));i.observe(document.body,{box:"device-pixel-content-box"})})).catch((function(){return!1})).then((function(i){return i?t._initResizeObserver():t._initDevicePixelRatioObservable()})):this._initDevicePixelRatioObservable()},t.prototype._initDevicePixelRatioObservable=function(){var t=this;if(null!==this._canvasElement){var i=Bs(this._canvasElement);if(null===i)throw new Error("No window is associated with the canvas");this._devicePixelRatioObservable=function(t){return new Es(t)}(i),this._devicePixelRatioObservable.subscribe((function(){return t._invalidateBitmapSize()})),this._invalidateBitmapSize()}},t.prototype._invalidateBitmapSize=function(){var t,i;if(null!==this._canvasElement){var s=Bs(this._canvasElement);if(null!==s){var e=null!==(i=null===(t=this._devicePixelRatioObservable)||void 0===t?void 0:t.value)&&void 0!==i?i:s.devicePixelRatio,h=this._canvasElement.getClientRects(),n=void 0!==h[0]?function(t,i){return Ds({width:Math.round(t.left*i+t.width*i)-Math.round(t.left*i),height:Math.round(t.top*i+t.height*i)-Math.round(t.top*i)})}(h[0],e):Ds({width:this._canvasElementClientSize.width*e,height:this._canvasElementClientSize.height*e});this._suggestNewBitmapSize(n)}}},t.prototype._initResizeObserver=function(){var t=this;null!==this._canvasElement&&(this._canvasElementResizeObserver=new ResizeObserver((function(i){var s=i.find((function(i){return i.target===t._canvasElement}));if(s&&s.devicePixelContentBoxSize&&s.devicePixelContentBoxSize[0]){var e=s.devicePixelContentBoxSize[0],h=Ds({width:e.inlineSize,height:e.blockSize});t._suggestNewBitmapSize(h)}})),this._canvasElementResizeObserver.observe(this._canvasElement,{box:"device-pixel-content-box"}))},t}();function Bs(t){return t.ownerDocument.defaultView}var Ls=function(){function t(t,i,s){if(0===i.width||0===i.height)throw new TypeError("Rendering target could only be created on a media with positive width and height");if(this._mediaSize=i,0===s.width||0===s.height)throw new TypeError("Rendering target could only be created using a bitmap with positive integer width and height");this._bitmapSize=s,this._context=t}return t.prototype.useMediaCoordinateSpace=function(t){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),this._context.scale(this._horizontalPixelRatio,this._verticalPixelRatio),t({context:this._context,mediaSize:this._mediaSize})}finally{this._context.restore()}},t.prototype.useBitmapCoordinateSpace=function(t){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),t({context:this._context,mediaSize:this._mediaSize,bitmapSize:this._bitmapSize,horizontalPixelRatio:this._horizontalPixelRatio,verticalPixelRatio:this._verticalPixelRatio})}finally{this._context.restore()}},Object.defineProperty(t.prototype,"_horizontalPixelRatio",{get:function(){return this._bitmapSize.width/this._mediaSize.width},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_verticalPixelRatio",{get:function(){return this._bitmapSize.height/this._mediaSize.height},enumerable:!1,configurable:!0}),t}();function zs(t,i){var s=t.canvasElementClientSize;if(0===s.width||0===s.height)return null;var e=t.bitmapSize;if(0===e.width||0===e.height)return null;var h=t.canvasElement.getContext("2d",i);return null===h?null:new Ls(h,s,e)}const Is="undefined"!=typeof window;function Ns(){return!!Is&&window.navigator.userAgent.toLowerCase().indexOf("firefox")>-1}function Vs(){return!!Is&&/iPhone|iPad|iPod/.test(window.navigator.platform)}function Fs(t){return t+t%2}var Ws,js,Hs,$s,Us,qs,Ys,Xs;function Zs(t,i){return t.position-i.position}function Ks(t,i,s){const e=(t.position-i.position)/(t.time-i.time);return Math.sign(e)*Math.min(Math.abs(e),s)}!function(t){t[t.MaxStartDelay=50]="MaxStartDelay",t[t.EpsilonDistance=1]="EpsilonDistance"}(Ws||(Ws={}));class Gs{constructor(t,i,s,e){this._position1=null,this._position2=null,this._position3=null,this._position4=null,this._animationStartPosition=null,this._durationMsecs=0,this._speedPxPerMsec=0,this._minSpeed=t,this._maxSpeed=i,this._dumpingCoeff=s,this._minMove=e}addPosition(t,i){if(null!==this._position1){if(this._position1.time===i)return void(this._position1.position=t);if(Math.abs(this._position1.position-t)<this._minMove)return}this._position4=this._position3,this._position3=this._position2,this._position2=this._position1,this._position1={time:i,position:t}}start(t,i){if(null===this._position1||null===this._position2)return;if(i-this._position1.time>50)return;let s=0;const e=Ks(this._position1,this._position2,this._maxSpeed),h=Zs(this._position1,this._position2),n=[e],r=[h];if(s+=h,null!==this._position3){const t=Ks(this._position2,this._position3,this._maxSpeed);if(Math.sign(t)===Math.sign(e)){const i=Zs(this._position2,this._position3);if(n.push(t),r.push(i),s+=i,null!==this._position4){const t=Ks(this._position3,this._position4,this._maxSpeed);if(Math.sign(t)===Math.sign(e)){const i=Zs(this._position3,this._position4);n.push(t),r.push(i),s+=i}}}}let o=0;for(let t=0;t<n.length;++t)o+=r[t]/s*n[t];Math.abs(o)<this._minSpeed||(this._animationStartPosition={position:t,time:i},this._speedPxPerMsec=o,this._durationMsecs=function(t,i){const s=Math.log(i);return Math.log(1*s/-t)/s}(Math.abs(o),this._dumpingCoeff))}getPosition(t){const i=r(this._animationStartPosition),s=t-i.time;return i.position+this._speedPxPerMsec*(Math.pow(this._dumpingCoeff,s)-1)/Math.log(this._dumpingCoeff)}finished(t){return null===this._animationStartPosition||this._progressDuration(t)===this._durationMsecs}_progressDuration(t){const i=t-r(this._animationStartPosition).time;return Math.min(i,this._durationMsecs)}}function Js(t,i){const s=r(t.ownerDocument).createElement("canvas");t.appendChild(s);const e=function(t,i){if("device-pixel-content-box"===i.type)return new Os(t,i.transform,i.options);throw new Error("Unsupported binding target")}(s,{type:"device-pixel-content-box",options:{allowResizeObserver:!1},transform:(t,i)=>({width:Math.max(t.width,i.width),height:Math.max(t.height,i.height)})});return e.resizeCanvasElement(i),e}function Qs(t){Is&&void 0!==window.chrome&&t.addEventListener("mousedown",(t=>{if(1===t.button)return t.preventDefault(),!1}))}!function(t){t[t.ResetClick=500]="ResetClick",t[t.LongTap=240]="LongTap",t[t.PreventFiresTouchEvents=500]="PreventFiresTouchEvents"}(js||(js={})),function(t){t[t.CancelClickManhattanDistance=5]="CancelClickManhattanDistance",t[t.CancelTapManhattanDistance=5]="CancelTapManhattanDistance",t[t.DoubleClickManhattanDistance=5]="DoubleClickManhattanDistance",t[t.DoubleTapManhattanDistance=30]="DoubleTapManhattanDistance"}(Hs||(Hs={}));class te{constructor(t,i,s){this._clickCount=0,this._clickTimeoutId=null,this._clickPosition={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY},this._tapCount=0,this._tapTimeoutId=null,this._tapPosition={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY},this._longTapTimeoutId=null,this._longTapActive=!1,this._mouseMoveStartPosition=null,this._touchMoveStartPosition=null,this._touchMoveExceededManhattanDistance=!1,this._cancelClick=!1,this._cancelTap=!1,this._unsubscribeOutsideMouseEvents=null,this._unsubscribeOutsideTouchEvents=null,this._unsubscribeMobileSafariEvents=null,this._unsubscribeMousemove=null,this._unsubscribeRootMouseEvents=null,this._unsubscribeRootTouchEvents=null,this._startPinchMiddlePoint=null,this._startPinchDistance=0,this._pinchPrevented=!1,this._preventTouchDragProcess=!1,this._mousePressed=!1,this._lastTouchEventTimeStamp=0,this._activeTouchId=null,this._acceptMouseLeave=!Vs(),this._onFirefoxOutsideMouseUp=t=>{this._mouseUpHandler(t)},this._onMobileSafariDoubleClick=t=>{if(this._firesTouchEvents(t)){const i=this._makeCompatEvent(t);if(++this._tapCount,this._tapTimeoutId&&this._tapCount>1){const{manhattanDistance:s}=this._touchMouseMoveWithDownInfo(ee(t),this._tapPosition);s<30&&!this._cancelTap&&this._processTouchEvent(i,this._handler.doubleTapEvent),this._resetTapTimeout()}}else{const i=this._makeCompatEvent(t);if(++this._clickCount,this._clickTimeoutId&&this._clickCount>1){const{manhattanDistance:s}=this._touchMouseMoveWithDownInfo(ee(t),this._clickPosition);s<5&&!this._cancelClick&&this._processMouseEvent(i,this._handler.mouseDoubleClickEvent),this._resetClickTimeout()}}},this._target=t,this._handler=i,this._options=s,this._init()}destroy(){null!==this._unsubscribeOutsideMouseEvents&&(this._unsubscribeOutsideMouseEvents(),this._unsubscribeOutsideMouseEvents=null),null!==this._unsubscribeOutsideTouchEvents&&(this._unsubscribeOutsideTouchEvents(),this._unsubscribeOutsideTouchEvents=null),null!==this._unsubscribeMousemove&&(this._unsubscribeMousemove(),this._unsubscribeMousemove=null),null!==this._unsubscribeRootMouseEvents&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null),null!==this._unsubscribeRootTouchEvents&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null),null!==this._unsubscribeMobileSafariEvents&&(this._unsubscribeMobileSafariEvents(),this._unsubscribeMobileSafariEvents=null),this._clearLongTapTimeout(),this._resetClickTimeout()}_mouseEnterHandler(t){this._unsubscribeMousemove&&this._unsubscribeMousemove();const i=this._mouseMoveHandler.bind(this);if(this._unsubscribeMousemove=()=>{this._target.removeEventListener("mousemove",i)},this._target.addEventListener("mousemove",i),this._firesTouchEvents(t))return;const s=this._makeCompatEvent(t);this._processMouseEvent(s,this._handler.mouseEnterEvent),this._acceptMouseLeave=!0}_resetClickTimeout(){null!==this._clickTimeoutId&&clearTimeout(this._clickTimeoutId),this._clickCount=0,this._clickTimeoutId=null,this._clickPosition={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY}}_resetTapTimeout(){null!==this._tapTimeoutId&&clearTimeout(this._tapTimeoutId),this._tapCount=0,this._tapTimeoutId=null,this._tapPosition={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY}}_mouseMoveHandler(t){if(this._mousePressed||null!==this._touchMoveStartPosition)return;if(this._firesTouchEvents(t))return;const i=this._makeCompatEvent(t);this._processMouseEvent(i,this._handler.mouseMoveEvent),this._acceptMouseLeave=!0}_touchMoveHandler(t){const i=ne(t.changedTouches,r(this._activeTouchId));if(null===i)return;if(this._lastTouchEventTimeStamp=he(t),null!==this._startPinchMiddlePoint)return;if(this._preventTouchDragProcess)return;this._pinchPrevented=!0;const s=this._touchMouseMoveWithDownInfo(ee(i),r(this._touchMoveStartPosition)),{xOffset:e,yOffset:h,manhattanDistance:n}=s;if(this._touchMoveExceededManhattanDistance||!(n<5)){if(!this._touchMoveExceededManhattanDistance){const t=.5*e,i=h>=t&&!this._options.treatVertTouchDragAsPageScroll(),s=t>h&&!this._options.treatHorzTouchDragAsPageScroll();i||s||(this._preventTouchDragProcess=!0),this._touchMoveExceededManhattanDistance=!0,this._cancelTap=!0,this._clearLongTapTimeout(),this._resetTapTimeout()}if(!this._preventTouchDragProcess){const s=this._makeCompatEvent(t,i);this._processTouchEvent(s,this._handler.touchMoveEvent),se(t)}}}_mouseMoveWithDownHandler(t){if(0!==t.button)return;const i=this._touchMouseMoveWithDownInfo(ee(t),r(this._mouseMoveStartPosition)),{manhattanDistance:s}=i;if(s>=5&&(this._cancelClick=!0,this._resetClickTimeout()),this._cancelClick){const i=this._makeCompatEvent(t);this._processMouseEvent(i,this._handler.pressedMouseMoveEvent)}}_touchMouseMoveWithDownInfo(t,i){const s=Math.abs(i.x-t.x),e=Math.abs(i.y-t.y);return{xOffset:s,yOffset:e,manhattanDistance:s+e}}_touchEndHandler(t){let i=ne(t.changedTouches,r(this._activeTouchId));if(null===i&&0===t.touches.length&&(i=t.changedTouches[0]),null===i)return;this._activeTouchId=null,this._lastTouchEventTimeStamp=he(t),this._clearLongTapTimeout(),this._touchMoveStartPosition=null,this._unsubscribeRootTouchEvents&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null);const s=this._makeCompatEvent(t,i);if(this._processTouchEvent(s,this._handler.touchEndEvent),++this._tapCount,this._tapTimeoutId&&this._tapCount>1){const{manhattanDistance:t}=this._touchMouseMoveWithDownInfo(ee(i),this._tapPosition);t<30&&!this._cancelTap&&this._processTouchEvent(s,this._handler.doubleTapEvent),this._resetTapTimeout()}else this._cancelTap||(this._processTouchEvent(s,this._handler.tapEvent),this._handler.tapEvent&&se(t));0===this._tapCount&&se(t),0===t.touches.length&&this._longTapActive&&(this._longTapActive=!1,se(t))}_mouseUpHandler(t){if(0!==t.button)return;const i=this._makeCompatEvent(t);if(this._mouseMoveStartPosition=null,this._mousePressed=!1,this._unsubscribeRootMouseEvents&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null),Ns()){this._target.ownerDocument.documentElement.removeEventListener("mouseleave",this._onFirefoxOutsideMouseUp)}if(!this._firesTouchEvents(t))if(this._processMouseEvent(i,this._handler.mouseUpEvent),++this._clickCount,this._clickTimeoutId&&this._clickCount>1){const{manhattanDistance:s}=this._touchMouseMoveWithDownInfo(ee(t),this._clickPosition);s<5&&!this._cancelClick&&this._processMouseEvent(i,this._handler.mouseDoubleClickEvent),this._resetClickTimeout()}else this._cancelClick||this._processMouseEvent(i,this._handler.mouseClickEvent)}_clearLongTapTimeout(){null!==this._longTapTimeoutId&&(clearTimeout(this._longTapTimeoutId),this._longTapTimeoutId=null)}_touchStartHandler(t){if(null!==this._activeTouchId)return;const i=t.changedTouches[0];this._activeTouchId=i.identifier,this._lastTouchEventTimeStamp=he(t);const s=this._target.ownerDocument.documentElement;this._cancelTap=!1,this._touchMoveExceededManhattanDistance=!1,this._preventTouchDragProcess=!1,this._touchMoveStartPosition=ee(i),this._unsubscribeRootTouchEvents&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null);{const i=this._touchMoveHandler.bind(this),e=this._touchEndHandler.bind(this);this._unsubscribeRootTouchEvents=()=>{s.removeEventListener("touchmove",i),s.removeEventListener("touchend",e)},s.addEventListener("touchmove",i,{passive:!1}),s.addEventListener("touchend",e,{passive:!1}),this._clearLongTapTimeout(),this._longTapTimeoutId=setTimeout(this._longTapHandler.bind(this,t),240)}const e=this._makeCompatEvent(t,i);this._processTouchEvent(e,this._handler.touchStartEvent),this._tapTimeoutId||(this._tapCount=0,this._tapTimeoutId=setTimeout(this._resetTapTimeout.bind(this),500),this._tapPosition=ee(i))}_mouseDownHandler(t){if(0!==t.button)return;const i=this._target.ownerDocument.documentElement;Ns()&&i.addEventListener("mouseleave",this._onFirefoxOutsideMouseUp),this._cancelClick=!1,this._mouseMoveStartPosition=ee(t),this._unsubscribeRootMouseEvents&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null);{const t=this._mouseMoveWithDownHandler.bind(this),s=this._mouseUpHandler.bind(this);this._unsubscribeRootMouseEvents=()=>{i.removeEventListener("mousemove",t),i.removeEventListener("mouseup",s)},i.addEventListener("mousemove",t),i.addEventListener("mouseup",s)}if(this._mousePressed=!0,this._firesTouchEvents(t))return;const s=this._makeCompatEvent(t);this._processMouseEvent(s,this._handler.mouseDownEvent),this._clickTimeoutId||(this._clickCount=0,this._clickTimeoutId=setTimeout(this._resetClickTimeout.bind(this),500),this._clickPosition=ee(t))}_init(){this._target.addEventListener("mouseenter",this._mouseEnterHandler.bind(this)),this._target.addEventListener("touchcancel",this._clearLongTapTimeout.bind(this));{const t=this._target.ownerDocument,i=t=>{this._handler.mouseDownOutsideEvent&&(t.composed&&this._target.contains(t.composedPath()[0])||t.target&&this._target.contains(t.target)||this._handler.mouseDownOutsideEvent())};this._unsubscribeOutsideTouchEvents=()=>{t.removeEventListener("touchstart",i)},this._unsubscribeOutsideMouseEvents=()=>{t.removeEventListener("mousedown",i)},t.addEventListener("mousedown",i),t.addEventListener("touchstart",i,{passive:!0})}Vs()&&(this._unsubscribeMobileSafariEvents=()=>{this._target.removeEventListener("dblclick",this._onMobileSafariDoubleClick)},this._target.addEventListener("dblclick",this._onMobileSafariDoubleClick)),this._target.addEventListener("mouseleave",this._mouseLeaveHandler.bind(this)),this._target.addEventListener("touchstart",this._touchStartHandler.bind(this),{passive:!0}),Qs(this._target),this._target.addEventListener("mousedown",this._mouseDownHandler.bind(this)),this._initPinch(),this._target.addEventListener("touchmove",(()=>{}),{passive:!1})}_initPinch(){void 0===this._handler.pinchStartEvent&&void 0===this._handler.pinchEvent&&void 0===this._handler.pinchEndEvent||(this._target.addEventListener("touchstart",(t=>this._checkPinchState(t.touches)),{passive:!0}),this._target.addEventListener("touchmove",(t=>{if(2===t.touches.length&&null!==this._startPinchMiddlePoint&&void 0!==this._handler.pinchEvent){const i=ie(t.touches[0],t.touches[1])/this._startPinchDistance;this._handler.pinchEvent(this._startPinchMiddlePoint,i),se(t)}}),{passive:!1}),this._target.addEventListener("touchend",(t=>{this._checkPinchState(t.touches)})))}_checkPinchState(t){1===t.length&&(this._pinchPrevented=!1),2!==t.length||this._pinchPrevented||this._longTapActive?this._stopPinch():this._startPinch(t)}_startPinch(t){const i=this._target.getBoundingClientRect()||{left:0,top:0};this._startPinchMiddlePoint={x:(t[0].clientX-i.left+(t[1].clientX-i.left))/2,y:(t[0].clientY-i.top+(t[1].clientY-i.top))/2},this._startPinchDistance=ie(t[0],t[1]),void 0!==this._handler.pinchStartEvent&&this._handler.pinchStartEvent(),this._clearLongTapTimeout()}_stopPinch(){null!==this._startPinchMiddlePoint&&(this._startPinchMiddlePoint=null,void 0!==this._handler.pinchEndEvent&&this._handler.pinchEndEvent())}_mouseLeaveHandler(t){if(this._unsubscribeMousemove&&this._unsubscribeMousemove(),this._firesTouchEvents(t))return;if(!this._acceptMouseLeave)return;const i=this._makeCompatEvent(t);this._processMouseEvent(i,this._handler.mouseLeaveEvent),this._acceptMouseLeave=!Vs()}_longTapHandler(t){const i=ne(t.touches,r(this._activeTouchId));if(null===i)return;const s=this._makeCompatEvent(t,i);this._processTouchEvent(s,this._handler.longTapEvent),this._cancelTap=!0,this._longTapActive=!0}_firesTouchEvents(t){return t.sourceCapabilities&&void 0!==t.sourceCapabilities.firesTouchEvents?t.sourceCapabilities.firesTouchEvents:he(t)<this._lastTouchEventTimeStamp+500}_processTouchEvent(t,i){i&&i.call(this._handler,t)}_processMouseEvent(t,i){i&&i.call(this._handler,t)}_makeCompatEvent(t,i){const s=i||t,e=this._target.getBoundingClientRect()||{left:0,top:0};return{clientX:s.clientX,clientY:s.clientY,pageX:s.pageX,pageY:s.pageY,screenX:s.screenX,screenY:s.screenY,localX:s.clientX-e.left,localY:s.clientY-e.top,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,metaKey:t.metaKey,isTouch:!t.type.startsWith("mouse")&&"contextmenu"!==t.type&&"click"!==t.type,srcType:t.type,target:s.target,view:t.view,preventDefault:()=>{"touchstart"!==t.type&&se(t)}}}}function ie(t,i){const s=t.clientX-i.clientX,e=t.clientY-i.clientY;return Math.sqrt(s*s+e*e)}function se(t){t.cancelable&&t.preventDefault()}function ee(t){return{x:t.pageX,y:t.pageY}}function he(t){return t.timeStamp||performance.now()}function ne(t,i){for(let s=0;s<t.length;++s)if(t[s].identifier===i)return t[s];return null}!function(t){t[t.Default=0]="Default",t[t.NsResize=1]="NsResize"}($s||($s={})),function(t){t[t.DefaultOptimalWidth=34]="DefaultOptimalWidth"}(Us||(Us={})),function(t){t[t.LabelOffset=5]="LabelOffset"}(Us||(Us={}));class re{constructor(t,i,s,e){this._priceScale=null,this._size=null,this._mousedown=!1,this._widthCache=new Yt(200),this._font=null,this._prevOptimalWidth=0,this._isSettingSize=!1,this._canvasSuggestedBitmapSizeChangedHandler=()=>{this._isSettingSize||this._pane.chart().model().lightUpdate()},this._topCanvasSuggestedBitmapSizeChangedHandler=()=>{this._isSettingSize||this._pane.chart().model().lightUpdate()},this._pane=t,this._options=i,this._layoutOptions=i.layout,this._rendererOptionsProvider=s,this._isLeft="left"===e,this._cell=document.createElement("div"),this._cell.style.height="100%",this._cell.style.overflow="hidden",this._cell.style.width="25px",this._cell.style.left="0",this._cell.style.position="relative",this._canvasBinding=Js(this._cell,Ds({width:16,height:16})),this._canvasBinding.subscribeSuggestedBitmapSizeChanged(this._canvasSuggestedBitmapSizeChangedHandler);const h=this._canvasBinding.canvasElement;h.style.position="absolute",h.style.zIndex="1",h.style.left="0",h.style.top="0",this._topCanvasBinding=Js(this._cell,Ds({width:16,height:16})),this._topCanvasBinding.subscribeSuggestedBitmapSizeChanged(this._topCanvasSuggestedBitmapSizeChangedHandler);const n=this._topCanvasBinding.canvasElement;n.style.position="absolute",n.style.zIndex="2",n.style.left="0",n.style.top="0";const r={mouseDownEvent:this._mouseDownEvent.bind(this),touchStartEvent:this._mouseDownEvent.bind(this),pressedMouseMoveEvent:this._pressedMouseMoveEvent.bind(this),touchMoveEvent:this._pressedMouseMoveEvent.bind(this),mouseDownOutsideEvent:this._mouseDownOutsideEvent.bind(this),mouseUpEvent:this._mouseUpEvent.bind(this),touchEndEvent:this._mouseUpEvent.bind(this),mouseDoubleClickEvent:this._mouseDoubleClickEvent.bind(this),doubleTapEvent:this._mouseDoubleClickEvent.bind(this),mouseEnterEvent:this._mouseEnterEvent.bind(this),mouseLeaveEvent:this._mouseLeaveEvent.bind(this)};this._mouseEventHandler=new te(this._topCanvasBinding.canvasElement,r,{treatVertTouchDragAsPageScroll:()=>!1,treatHorzTouchDragAsPageScroll:()=>!0})}destroy(){this._mouseEventHandler.destroy(),this._topCanvasBinding.unsubscribeSuggestedBitmapSizeChanged(this._topCanvasSuggestedBitmapSizeChangedHandler),this._topCanvasBinding.dispose(),this._canvasBinding.unsubscribeSuggestedBitmapSizeChanged(this._canvasSuggestedBitmapSizeChangedHandler),this._canvasBinding.dispose(),null!==this._priceScale&&this._priceScale.onMarksChanged().unsubscribeAll(this),this._priceScale=null}getElement(){return this._cell}fontSize(){return this._layoutOptions.fontSize}rendererOptions(){const t=this._rendererOptionsProvider.options();return this._font!==t.font&&(this._widthCache.reset(),this._font=t.font),t}optimalWidth(){if(null===this._priceScale)return 0;let t=0;const i=this.rendererOptions(),s=r(this._canvasBinding.canvasElement.getContext("2d"));s.save();const e=this._priceScale.marks();s.font=this._baseFont(),e.length>0&&(t=Math.max(this._widthCache.measureText(s,e[0].label),this._widthCache.measureText(s,e[e.length-1].label)));const h=this._backLabels();for(let i=h.length;i--;){const e=this._widthCache.measureText(s,h[i].text());e>t&&(t=e)}const n=this._priceScale.firstValue();if(null!==n&&null!==this._size){const i=this._priceScale.coordinateToPrice(1,n),e=this._priceScale.coordinateToPrice(this._size.height-2,n);t=Math.max(t,this._widthCache.measureText(s,this._priceScale.formatPrice(Math.floor(Math.min(i,e))+.11111111111111,n)),this._widthCache.measureText(s,this._priceScale.formatPrice(Math.ceil(Math.max(i,e))-.11111111111111,n)))}s.restore();const o=t||34;return Fs(Math.ceil(i.borderSize+i.tickLength+i.paddingInner+i.paddingOuter+5+o))}setSize(t){null!==this._size&&As(this._size,t)||(this._size=t,this._isSettingSize=!0,this._canvasBinding.resizeCanvasElement(t),this._topCanvasBinding.resizeCanvasElement(t),this._isSettingSize=!1,this._cell.style.width=`${t.width}px`,this._cell.style.height=`${t.height}px`)}getWidth(){return r(this._size).width}setPriceScale(t){this._priceScale!==t&&(null!==this._priceScale&&this._priceScale.onMarksChanged().unsubscribeAll(this),this._priceScale=t,t.onMarksChanged().subscribe(this._onMarksChanged.bind(this),this))}priceScale(){return this._priceScale}reset(){const t=this._pane.state();this._pane.chart().model().resetPriceScale(t,r(this.priceScale()))}paint(t){if(null===this._size)return;if(1!==t){this._alignLabels(),this._canvasBinding.applySuggestedBitmapSize();const t=zs(this._canvasBinding);null!==t&&(t.useBitmapCoordinateSpace((t=>{this._drawBackground(t),this._drawBorder(t)})),this._drawTickMarks(t),this._drawBackLabels(t))}this._topCanvasBinding.applySuggestedBitmapSize();const i=zs(this._topCanvasBinding);null!==i&&(i.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this._drawCrosshairLabel(i))}getBitmapSize(){return this._canvasBinding.bitmapSize}drawBitmap(t,i,s){const e=this.getBitmapSize();e.width>0&&e.height>0&&t.drawImage(this._canvasBinding.canvasElement,i,s)}update(){var t;null===(t=this._priceScale)||void 0===t||t.marks()}_mouseDownEvent(t){if(null===this._priceScale||this._priceScale.isEmpty()||!this._options.handleScale.axisPressedMouseMove.price)return;const i=this._pane.chart().model(),s=this._pane.state();this._mousedown=!0,i.startScalePrice(s,this._priceScale,t.localY)}_pressedMouseMoveEvent(t){if(null===this._priceScale||!this._options.handleScale.axisPressedMouseMove.price)return;const i=this._pane.chart().model(),s=this._pane.state(),e=this._priceScale;i.scalePriceTo(s,e,t.localY)}_mouseDownOutsideEvent(){if(null===this._priceScale||!this._options.handleScale.axisPressedMouseMove.price)return;const t=this._pane.chart().model(),i=this._pane.state(),s=this._priceScale;this._mousedown&&(this._mousedown=!1,t.endScalePrice(i,s))}_mouseUpEvent(t){if(null===this._priceScale||!this._options.handleScale.axisPressedMouseMove.price)return;const i=this._pane.chart().model(),s=this._pane.state();this._mousedown=!1,i.endScalePrice(s,this._priceScale)}_mouseDoubleClickEvent(t){this._options.handleScale.axisDoubleClickReset.price&&this.reset()}_mouseEnterEvent(t){if(null===this._priceScale)return;!this._pane.chart().model().options().handleScale.axisPressedMouseMove.price||this._priceScale.isPercentage()||this._priceScale.isIndexedTo100()||this._setCursor(1)}_mouseLeaveEvent(t){this._setCursor(0)}_backLabels(){const t=[],i=null===this._priceScale?void 0:this._priceScale;return(s=>{for(let e=0;e<s.length;++e){const h=s[e].priceAxisViews(this._pane.state(),i);for(let i=0;i<h.length;i++)t.push(h[i])}})(this._pane.state().orderedSources()),t}_drawBackground({context:t,bitmapSize:i}){const{width:s,height:e}=i,h=this._pane.state().model(),n=h.backgroundTopColor(),r=h.backgroundBottomColor();n===r?F(t,0,0,s,e,n):$(t,0,0,s,e,n,r)}_drawBorder({context:t,bitmapSize:i,horizontalPixelRatio:s}){if(null===this._size||null===this._priceScale||!this._priceScale.options().borderVisible)return;t.fillStyle=this._priceScale.options().borderColor;const e=Math.max(1,Math.floor(this.rendererOptions().borderSize*s));let h;h=this._isLeft?i.width-e:0,t.fillRect(h,0,e,i.height)}_drawTickMarks(t){if(null===this._size||null===this._priceScale)return;const i=this._priceScale.marks(),s=this._priceScale.options(),e=this.rendererOptions(),h=this._isLeft?this._size.width-e.tickLength:0;s.borderVisible&&s.ticksVisible&&t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:n,verticalPixelRatio:r})=>{t.fillStyle=s.borderColor;const o=Math.max(1,Math.floor(r)),l=Math.floor(.5*r),a=Math.round(e.tickLength*n);t.beginPath();for(const s of i)t.rect(Math.floor(h*n),Math.round(s.coord*r)-l,a,o);t.fill()})),t.useMediaCoordinateSpace((({context:t})=>{var n;t.font=this._baseFont(),t.fillStyle=null!==(n=s.textColor)&&void 0!==n?n:this._layoutOptions.textColor,t.textAlign=this._isLeft?"right":"left",t.textBaseline="middle";const r=this._isLeft?Math.round(h-e.paddingInner):Math.round(h+e.tickLength+e.paddingInner),o=i.map((i=>this._widthCache.yMidCorrection(t,i.label)));for(let s=i.length;s--;){const e=i[s];t.fillText(e.label,r,e.coord+o[s])}}))}_alignLabels(){if(null===this._size||null===this._priceScale)return;let t=this._size.height/2;const i=[],s=this._priceScale.orderedSources().slice(),e=this._pane.state(),h=this.rendererOptions();this._priceScale===e.defaultVisiblePriceScale()&&this._pane.state().orderedSources().forEach((t=>{e.isOverlay(t)&&s.push(t)}));const n=this._priceScale.dataSources()[0],r=this._priceScale;s.forEach((s=>{const h=s.priceAxisViews(e,r);h.forEach((t=>{t.setFixedCoordinate(null),t.isVisible()&&i.push(t)})),n===s&&h.length>0&&(t=h[0].coordinate())})),i.forEach((t=>t.setFixedCoordinate(t.coordinate())));this._priceScale.options().alignLabels&&this._fixLabelOverlap(i,h,t)}_fixLabelOverlap(t,i,s){if(null===this._size)return;const e=t.filter((t=>t.coordinate()<=s)),h=t.filter((t=>t.coordinate()>s));e.sort(((t,i)=>i.coordinate()-t.coordinate())),e.length&&h.length&&h.push(e[0]),h.sort(((t,i)=>t.coordinate()-i.coordinate()));for(const s of t){const t=Math.floor(s.height(i)/2),e=s.coordinate();e>-t&&e<t&&s.setFixedCoordinate(t),e>this._size.height-t&&e<this._size.height+t&&s.setFixedCoordinate(this._size.height-t)}for(let t=1;t<e.length;t++){const s=e[t],h=e[t-1],n=h.height(i,!1),r=s.coordinate(),o=h.getFixedCoordinate();r>o-n&&s.setFixedCoordinate(o-n)}for(let t=1;t<h.length;t++){const s=h[t],e=h[t-1],n=e.height(i,!0),r=s.coordinate(),o=e.getFixedCoordinate();r<o+n&&s.setFixedCoordinate(o+n)}}_drawBackLabels(t){if(null===this._size)return;const i=this._backLabels(),s=this.rendererOptions(),e=this._isLeft?"right":"left";i.forEach((i=>{if(i.isAxisLabelVisible()){i.renderer(r(this._priceScale)).draw(t,s,this._widthCache,e)}}))}_drawCrosshairLabel(t){if(null===this._size||null===this._priceScale)return;const i=this._pane.chart().model(),s=[],e=this._pane.state(),h=i.crosshairSource().priceAxisViews(e,this._priceScale);h.length&&s.push(h);const n=this.rendererOptions(),o=this._isLeft?"right":"left";s.forEach((i=>{i.forEach((i=>{i.renderer(r(this._priceScale)).draw(t,n,this._widthCache,o)}))}))}_setCursor(t){this._cell.style.cursor=1===t?"ns-resize":"default"}_onMarksChanged(){const t=this.optimalWidth();this._prevOptimalWidth<t&&this._pane.chart().model().fullUpdate(),this._prevOptimalWidth=t}_baseFont(){return T(this._layoutOptions.fontSize,this._layoutOptions.fontFamily)}}function oe(t,i,s,e){t.drawBackground&&t.drawBackground(i,s,e)}function le(t,i,s,e){t.draw(i,s,e)}function ae(t,i){return t.paneViews(i)}function ue(t,i){return t.labelPaneViews(i)}function ce(t,i){return void 0!==t.topPaneViews?t.topPaneViews(i):[]}!function(t){t[t.MinScrollSpeed=.2]="MinScrollSpeed",t[t.MaxScrollSpeed=7]="MaxScrollSpeed",t[t.DumpingCoeff=.997]="DumpingCoeff",t[t.ScrollMinMove=15]="ScrollMinMove"}(qs||(qs={}));class de{constructor(t,i){this._size=Ds({width:0,height:0}),this._leftPriceAxisWidget=null,this._rightPriceAxisWidget=null,this._startScrollingPos=null,this._isScrolling=!1,this._clicked=new b,this._prevPinchScale=0,this._longTap=!1,this._startTrackPoint=null,this._exitTrackingModeOnNextTry=!1,this._initCrosshairPosition=null,this._scrollXAnimation=null,this._isSettingSize=!1,this._canvasSuggestedBitmapSizeChangedHandler=()=>{this._isSettingSize||null===this._state||this._model().lightUpdate()},this._topCanvasSuggestedBitmapSizeChangedHandler=()=>{this._isSettingSize||null===this._state||this._model().lightUpdate()},this._chart=t,this._state=i,this._state.onDestroyed().subscribe(this._onStateDestroyed.bind(this),this,!0),this._paneCell=document.createElement("td"),this._paneCell.style.padding="0",this._paneCell.style.position="relative";const s=document.createElement("div");s.style.width="100%",s.style.height="100%",s.style.position="relative",s.style.overflow="hidden",this._leftAxisCell=document.createElement("td"),this._leftAxisCell.style.padding="0",this._rightAxisCell=document.createElement("td"),this._rightAxisCell.style.padding="0",this._paneCell.appendChild(s),this._canvasBinding=Js(s,Ds({width:16,height:16})),this._canvasBinding.subscribeSuggestedBitmapSizeChanged(this._canvasSuggestedBitmapSizeChangedHandler);const e=this._canvasBinding.canvasElement;e.style.position="absolute",e.style.zIndex="1",e.style.left="0",e.style.top="0",this._topCanvasBinding=Js(s,Ds({width:16,height:16})),this._topCanvasBinding.subscribeSuggestedBitmapSizeChanged(this._topCanvasSuggestedBitmapSizeChangedHandler);const h=this._topCanvasBinding.canvasElement;h.style.position="absolute",h.style.zIndex="2",h.style.left="0",h.style.top="0",this._rowElement=document.createElement("tr"),this._rowElement.appendChild(this._leftAxisCell),this._rowElement.appendChild(this._paneCell),this._rowElement.appendChild(this._rightAxisCell),this.updatePriceAxisWidgetsStates(),this._mouseEventHandler=new te(this._topCanvasBinding.canvasElement,this,{treatVertTouchDragAsPageScroll:()=>null===this._startTrackPoint&&!this._chart.options().handleScroll.vertTouchDrag,treatHorzTouchDragAsPageScroll:()=>null===this._startTrackPoint&&!this._chart.options().handleScroll.horzTouchDrag})}destroy(){null!==this._leftPriceAxisWidget&&this._leftPriceAxisWidget.destroy(),null!==this._rightPriceAxisWidget&&this._rightPriceAxisWidget.destroy(),this._topCanvasBinding.unsubscribeSuggestedBitmapSizeChanged(this._topCanvasSuggestedBitmapSizeChangedHandler),this._topCanvasBinding.dispose(),this._canvasBinding.unsubscribeSuggestedBitmapSizeChanged(this._canvasSuggestedBitmapSizeChangedHandler),this._canvasBinding.dispose(),null!==this._state&&this._state.onDestroyed().unsubscribeAll(this),this._mouseEventHandler.destroy()}state(){return r(this._state)}setState(t){null!==this._state&&this._state.onDestroyed().unsubscribeAll(this),this._state=t,null!==this._state&&this._state.onDestroyed().subscribe(de.prototype._onStateDestroyed.bind(this),this,!0),this.updatePriceAxisWidgetsStates()}chart(){return this._chart}getElement(){return this._rowElement}updatePriceAxisWidgetsStates(){if(null!==this._state&&(this._recreatePriceAxisWidgets(),0!==this._model().serieses().length)){if(null!==this._leftPriceAxisWidget){const t=this._state.leftPriceScale();this._leftPriceAxisWidget.setPriceScale(r(t))}if(null!==this._rightPriceAxisWidget){const t=this._state.rightPriceScale();this._rightPriceAxisWidget.setPriceScale(r(t))}}}updatePriceAxisWidgets(){null!==this._leftPriceAxisWidget&&this._leftPriceAxisWidget.update(),null!==this._rightPriceAxisWidget&&this._rightPriceAxisWidget.update()}stretchFactor(){return null!==this._state?this._state.stretchFactor():0}setStretchFactor(t){this._state&&this._state.setStretchFactor(t)}mouseEnterEvent(t){if(!this._state)return;this._onMouseEvent();const i=t.localX,s=t.localY;this._setCrosshairPosition(i,s,t)}mouseDownEvent(t){this._onMouseEvent(),this._mouseTouchDownEvent(),this._setCrosshairPosition(t.localX,t.localY,t)}mouseMoveEvent(t){if(!this._state)return;this._onMouseEvent();const i=t.localX,s=t.localY;this._setCrosshairPosition(i,s,t);const e=this.hitTest(i,s);this._model().setHoveredSource(e&&{source:e.source,object:e.object})}mouseClickEvent(t){null!==this._state&&(this._onMouseEvent(),this._fireClickedDelegate(t))}pressedMouseMoveEvent(t){this._onMouseEvent(),this._pressedMouseTouchMoveEvent(t),this._setCrosshairPosition(t.localX,t.localY,t)}mouseUpEvent(t){null!==this._state&&(this._onMouseEvent(),this._longTap=!1,this._endScroll(t))}tapEvent(t){null!==this._state&&this._fireClickedDelegate(t)}longTapEvent(t){if(this._longTap=!0,null===this._startTrackPoint){const i={x:t.localX,y:t.localY};this._startTrackingMode(i,i,t)}}mouseLeaveEvent(t){null!==this._state&&(this._onMouseEvent(),this._state.model().setHoveredSource(null),this._clearCrosshairPosition())}clicked(){return this._clicked}pinchStartEvent(){this._prevPinchScale=1,this._model().stopTimeScaleAnimation()}pinchEvent(t,i){if(!this._chart.options().handleScale.pinch)return;const s=5*(i-this._prevPinchScale);this._prevPinchScale=i,this._model().zoomTime(t.x,s)}touchStartEvent(t){if(this._longTap=!1,this._exitTrackingModeOnNextTry=null!==this._startTrackPoint,this._mouseTouchDownEvent(),null!==this._startTrackPoint){const i=this._model().crosshairSource();this._initCrosshairPosition={x:i.appliedX(),y:i.appliedY()},this._startTrackPoint={x:t.localX,y:t.localY}}}touchMoveEvent(t){if(null===this._state)return;const i=t.localX,s=t.localY;if(null===this._startTrackPoint)this._pressedMouseTouchMoveEvent(t);else{this._exitTrackingModeOnNextTry=!1;const e=r(this._initCrosshairPosition),h=e.x+(i-this._startTrackPoint.x),n=e.y+(s-this._startTrackPoint.y);this._setCrosshairPosition(h,n,t)}}touchEndEvent(t){0===this.chart().options().trackingMode.exitMode&&(this._exitTrackingModeOnNextTry=!0),this._tryExitTrackingMode(),this._endScroll(t)}hitTest(t,i){const s=this._state;if(null===s)return null;const e=s.orderedSources();for(const h of e){const e=this._hitTestPaneView(h.paneViews(s),t,i);if(null!==e)return{source:h,view:e.view,object:e.object}}return null}setPriceAxisSize(t,i){r("left"===i?this._leftPriceAxisWidget:this._rightPriceAxisWidget).setSize(Ds({width:t,height:this._size.height}))}getSize(){return this._size}setSize(t){As(this._size,t)||(this._size=t,this._isSettingSize=!0,this._canvasBinding.resizeCanvasElement(t),this._topCanvasBinding.resizeCanvasElement(t),this._isSettingSize=!1,this._paneCell.style.width=t.width+"px",this._paneCell.style.height=t.height+"px")}recalculatePriceScales(){const t=r(this._state);t.recalculatePriceScale(t.leftPriceScale()),t.recalculatePriceScale(t.rightPriceScale());for(const i of t.dataSources())if(t.isOverlay(i)){const s=i.priceScale();null!==s&&t.recalculatePriceScale(s),i.updateAllViews()}}getBitmapSize(){return this._canvasBinding.bitmapSize}drawBitmap(t,i,s){const e=this.getBitmapSize();e.width>0&&e.height>0&&t.drawImage(this._canvasBinding.canvasElement,i,s)}paint(t){if(0===t)return;if(null===this._state)return;if(t>1&&this.recalculatePriceScales(),null!==this._leftPriceAxisWidget&&this._leftPriceAxisWidget.paint(t),null!==this._rightPriceAxisWidget&&this._rightPriceAxisWidget.paint(t),1!==t){this._canvasBinding.applySuggestedBitmapSize();const t=zs(this._canvasBinding);null!==t&&(t.useBitmapCoordinateSpace((t=>{this._drawBackground(t)})),this._state&&(this._drawGrid(t),this._drawWatermark(t),this._drawSources(t,ae),this._drawSources(t,ue)))}this._topCanvasBinding.applySuggestedBitmapSize();const i=zs(this._topCanvasBinding);null!==i&&(i.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this._drawSources(i,ce),this._drawCrosshair(i))}leftPriceAxisWidget(){return this._leftPriceAxisWidget}rightPriceAxisWidget(){return this._rightPriceAxisWidget}setCrosshair(t,i,s){if(this._state)if(s){const s=t,e=i;this._setCrosshairPositionNoFire(s,e)}else this._state.model().setHoveredSource(null),this._clearCrosshairPosition()}_onStateDestroyed(){null!==this._state&&this._state.onDestroyed().unsubscribeAll(this),this._state=null}_fireClickedDelegate(t){const i=t.localX,s=t.localY;this._clicked.hasListeners()&&this._clicked.fire(this._model().timeScale().coordinateToIndex(i),{x:i,y:s},t)}_drawBackground({context:t,bitmapSize:i}){const{width:s,height:e}=i,h=this._model(),n=h.backgroundTopColor(),r=h.backgroundBottomColor();n===r?F(t,0,0,s,e,r):$(t,0,0,s,e,n,r)}_drawGrid(t){const i=r(this._state).grid().paneView().renderer();null!==i&&i.draw(t,!1)}_drawWatermark(t){const i=this._model().watermarkSource();this._drawSourceImpl(t,ae,oe,i),this._drawSourceImpl(t,ae,le,i)}_drawCrosshair(t){this._drawSourceImpl(t,ae,le,this._model().crosshairSource())}_drawSources(t,i){const s=r(this._state).orderedSources();for(const e of s)this._drawSourceImpl(t,i,oe,e);for(const e of s)this._drawSourceImpl(t,i,le,e)}_drawSourceImpl(t,i,s,e){const h=r(this._state),n=i(e,h),o=h.model().hoveredSource(),l=null!==o&&o.source===e,a=null!==o&&l&&void 0!==o.object?o.object.hitTestData:void 0;for(const i of n){const e=i.renderer();null!==e&&s(e,t,l,a)}}_hitTestPaneView(t,i,s){for(const e of t){const t=e.renderer();if(null!==t&&t.hitTest){const h=t.hitTest(i,s);if(null!==h)return{view:e,object:h}}}return null}_recreatePriceAxisWidgets(){if(null===this._state)return;const t=this._chart,i=this._state.leftPriceScale().options().visible,s=this._state.rightPriceScale().options().visible;i||null===this._leftPriceAxisWidget||(this._leftAxisCell.removeChild(this._leftPriceAxisWidget.getElement()),this._leftPriceAxisWidget.destroy(),this._leftPriceAxisWidget=null),s||null===this._rightPriceAxisWidget||(this._rightAxisCell.removeChild(this._rightPriceAxisWidget.getElement()),this._rightPriceAxisWidget.destroy(),this._rightPriceAxisWidget=null);const e=t.model().rendererOptionsProvider();i&&null===this._leftPriceAxisWidget&&(this._leftPriceAxisWidget=new re(this,t.options(),e,"left"),this._leftAxisCell.appendChild(this._leftPriceAxisWidget.getElement())),s&&null===this._rightPriceAxisWidget&&(this._rightPriceAxisWidget=new re(this,t.options(),e,"right"),this._rightAxisCell.appendChild(this._rightPriceAxisWidget.getElement()))}_preventScroll(t){return t.isTouch&&this._longTap||null!==this._startTrackPoint}_correctXCoord(t){return Math.max(0,Math.min(t,this._size.width-1))}_correctYCoord(t){return Math.max(0,Math.min(t,this._size.height-1))}_setCrosshairPosition(t,i,s){this._model().setAndSaveCurrentPosition(this._correctXCoord(t),this._correctYCoord(i),s,r(this._state))}_setCrosshairPositionNoFire(t,i){this._model().setAndSaveCurrentPositionFire(this._correctXCoord(t),this._correctYCoord(i),!1,r(this._state))}_clearCrosshairPosition(){this._model().clearCurrentPosition()}_tryExitTrackingMode(){this._exitTrackingModeOnNextTry&&(this._startTrackPoint=null,this._clearCrosshairPosition())}_startTrackingMode(t,i,s){this._startTrackPoint=t,this._exitTrackingModeOnNextTry=!1,this._setCrosshairPosition(i.x,i.y,s);const e=this._model().crosshairSource();this._initCrosshairPosition={x:e.appliedX(),y:e.appliedY()}}_model(){return this._chart.model()}_endScroll(t){if(!this._isScrolling)return;const i=this._model(),s=this.state();if(i.endScrollPrice(s,s.defaultPriceScale()),this._startScrollingPos=null,this._isScrolling=!1,i.endScrollTime(),null!==this._scrollXAnimation){const t=performance.now(),s=i.timeScale();this._scrollXAnimation.start(s.rightOffset(),t),this._scrollXAnimation.finished(t)||i.setTimeScaleAnimation(this._scrollXAnimation)}}_onMouseEvent(){this._startTrackPoint=null}_mouseTouchDownEvent(){if(!this._state)return;if(this._model().stopTimeScaleAnimation(),document.activeElement!==document.body&&document.activeElement!==document.documentElement)r(document.activeElement).blur();else{const t=document.getSelection();null!==t&&t.removeAllRanges()}!this._state.defaultPriceScale().isEmpty()&&this._model().timeScale().isEmpty()}_pressedMouseTouchMoveEvent(t){if(null===this._state)return;const i=this._model(),s=i.timeScale();if(s.isEmpty())return;const e=this._chart.options(),h=e.handleScroll,n=e.kineticScroll;if((!h.pressedMouseMove||t.isTouch)&&(!h.horzTouchDrag&&!h.vertTouchDrag||!t.isTouch))return;const r=this._state.defaultPriceScale(),o=performance.now();if(null!==this._startScrollingPos||this._preventScroll(t)||(this._startScrollingPos={x:t.clientX,y:t.clientY,timestamp:o,localX:t.localX,localY:t.localY}),null!==this._startScrollingPos&&!this._isScrolling&&(this._startScrollingPos.x!==t.clientX||this._startScrollingPos.y!==t.clientY)){if(t.isTouch&&n.touch||!t.isTouch&&n.mouse){const t=s.barSpacing();this._scrollXAnimation=new Gs(.2/t,7/t,.997,15/t),this._scrollXAnimation.addPosition(s.rightOffset(),this._startScrollingPos.timestamp)}else this._scrollXAnimation=null;r.isEmpty()||i.startScrollPrice(this._state,r,t.localY),i.startScrollTime(t.localX),this._isScrolling=!0}this._isScrolling&&(r.isEmpty()||i.scrollPriceTo(this._state,r,t.localY),i.scrollTimeTo(t.localX),null!==this._scrollXAnimation&&this._scrollXAnimation.addPosition(s.rightOffset(),o))}}class fe{constructor(t,i,s,e,h){this._invalidated=!0,this._size=Ds({width:0,height:0}),this._canvasSuggestedBitmapSizeChangedHandler=()=>this.paint(3),this._isLeft="left"===t,this._rendererOptionsProvider=s.rendererOptionsProvider,this._options=i,this._borderVisible=e,this._bottomColor=h,this._cell=document.createElement("div"),this._cell.style.width="25px",this._cell.style.height="100%",this._cell.style.overflow="hidden",this._canvasBinding=Js(this._cell,Ds({width:16,height:16})),this._canvasBinding.subscribeSuggestedBitmapSizeChanged(this._canvasSuggestedBitmapSizeChangedHandler)}destroy(){this._canvasBinding.unsubscribeSuggestedBitmapSizeChanged(this._canvasSuggestedBitmapSizeChangedHandler),this._canvasBinding.dispose()}getElement(){return this._cell}getSize(){return this._size}setSize(t){As(this._size,t)||(this._size=t,this._canvasBinding.resizeCanvasElement(t),this._cell.style.width=`${t.width}px`,this._cell.style.height=`${t.height}px`,this._invalidated=!0)}paint(t){if(t<3&&!this._invalidated)return;if(0===this._size.width||0===this._size.height)return;this._invalidated=!1,this._canvasBinding.applySuggestedBitmapSize();const i=zs(this._canvasBinding);null!==i&&i.useBitmapCoordinateSpace((t=>{this._drawBackground(t),this._drawBorder(t)}))}getBitmapSize(){return this._canvasBinding.bitmapSize}drawBitmap(t,i,s){const e=this.getBitmapSize();e.width>0&&e.height>0&&t.drawImage(this._canvasBinding.canvasElement,i,s)}_drawBorder({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(!this._borderVisible())return;t.fillStyle=this._options.timeScale.borderColor;const h=Math.floor(this._rendererOptionsProvider.options().borderSize*s),n=Math.floor(this._rendererOptionsProvider.options().borderSize*e),r=this._isLeft?i.width-h:0;t.fillRect(r,0,h,n)}_drawBackground({context:t,bitmapSize:i}){F(t,0,0,i.width,i.height,this._bottomColor())}}function pe(t,i){return t.weight>i.weight?t:i}!function(t){t[t.BorderSize=1]="BorderSize",t[t.TickLength=5]="TickLength"}(Ys||(Ys={})),function(t){t[t.Default=0]="Default",t[t.EwResize=1]="EwResize"}(Xs||(Xs={}));class me{constructor(t){this._leftStub=null,this._rightStub=null,this._rendererOptions=null,this._mouseDown=!1,this._size=Ds({width:0,height:0}),this._sizeChanged=new b,this._widthCache=new Yt(5),this._isSettingSize=!1,this._canvasSuggestedBitmapSizeChangedHandler=()=>{this._isSettingSize||this._chart.model().lightUpdate()},this._topCanvasSuggestedBitmapSizeChangedHandler=()=>{this._isSettingSize||this._chart.model().lightUpdate()},this._chart=t,this._options=t.options().layout,this._element=document.createElement("tr"),this._leftStubCell=document.createElement("td"),this._leftStubCell.style.padding="0",this._rightStubCell=document.createElement("td"),this._rightStubCell.style.padding="0",this._cell=document.createElement("td"),this._cell.style.height="25px",this._cell.style.padding="0",this._dv=document.createElement("div"),this._dv.style.width="100%",this._dv.style.height="100%",this._dv.style.position="relative",this._dv.style.overflow="hidden",this._cell.appendChild(this._dv),this._canvasBinding=Js(this._dv,Ds({width:16,height:16})),this._canvasBinding.subscribeSuggestedBitmapSizeChanged(this._canvasSuggestedBitmapSizeChangedHandler);const i=this._canvasBinding.canvasElement;i.style.position="absolute",i.style.zIndex="1",i.style.left="0",i.style.top="0",this._topCanvasBinding=Js(this._dv,Ds({width:16,height:16})),this._topCanvasBinding.subscribeSuggestedBitmapSizeChanged(this._topCanvasSuggestedBitmapSizeChangedHandler);const s=this._topCanvasBinding.canvasElement;s.style.position="absolute",s.style.zIndex="2",s.style.left="0",s.style.top="0",this._element.appendChild(this._leftStubCell),this._element.appendChild(this._cell),this._element.appendChild(this._rightStubCell),this._recreateStubs(),this._chart.model().priceScalesOptionsChanged().subscribe(this._recreateStubs.bind(this),this),this._mouseEventHandler=new te(this._topCanvasBinding.canvasElement,this,{treatVertTouchDragAsPageScroll:()=>!0,treatHorzTouchDragAsPageScroll:()=>!1})}destroy(){this._mouseEventHandler.destroy(),null!==this._leftStub&&this._leftStub.destroy(),null!==this._rightStub&&this._rightStub.destroy(),this._topCanvasBinding.unsubscribeSuggestedBitmapSizeChanged(this._topCanvasSuggestedBitmapSizeChangedHandler),this._topCanvasBinding.dispose(),this._canvasBinding.unsubscribeSuggestedBitmapSizeChanged(this._canvasSuggestedBitmapSizeChangedHandler),this._canvasBinding.dispose()}getElement(){return this._element}leftStub(){return this._leftStub}rightStub(){return this._rightStub}mouseDownEvent(t){if(this._mouseDown)return;this._mouseDown=!0;const i=this._chart.model();!i.timeScale().isEmpty()&&this._chart.options().handleScale.axisPressedMouseMove.time&&i.startScaleTime(t.localX)}touchStartEvent(t){this.mouseDownEvent(t)}mouseDownOutsideEvent(){const t=this._chart.model();!t.timeScale().isEmpty()&&this._mouseDown&&(this._mouseDown=!1,this._chart.options().handleScale.axisPressedMouseMove.time&&t.endScaleTime())}pressedMouseMoveEvent(t){const i=this._chart.model();!i.timeScale().isEmpty()&&this._chart.options().handleScale.axisPressedMouseMove.time&&i.scaleTimeTo(t.localX)}touchMoveEvent(t){this.pressedMouseMoveEvent(t)}mouseUpEvent(){this._mouseDown=!1;const t=this._chart.model();t.timeScale().isEmpty()&&!this._chart.options().handleScale.axisPressedMouseMove.time||t.endScaleTime()}touchEndEvent(){this.mouseUpEvent()}mouseDoubleClickEvent(){this._chart.options().handleScale.axisDoubleClickReset.time&&this._chart.model().resetTimeScale()}doubleTapEvent(){this.mouseDoubleClickEvent()}mouseEnterEvent(){this._chart.model().options().handleScale.axisPressedMouseMove.time&&this._setCursor(1)}mouseLeaveEvent(){this._setCursor(0)}getSize(){return this._size}sizeChanged(){return this._sizeChanged}setSizes(t,i,s){As(this._size,t)||(this._size=t,this._isSettingSize=!0,this._canvasBinding.resizeCanvasElement(t),this._topCanvasBinding.resizeCanvasElement(t),this._isSettingSize=!1,this._cell.style.width=`${t.width}px`,this._cell.style.height=`${t.height}px`,this._sizeChanged.fire(t)),null!==this._leftStub&&this._leftStub.setSize(Ds({width:i,height:t.height})),null!==this._rightStub&&this._rightStub.setSize(Ds({width:s,height:t.height}))}optimalHeight(){const t=this._getRendererOptions();return Math.ceil(t.borderSize+t.tickLength+t.fontSize+t.paddingTop+t.paddingBottom+t.labelBottomOffset)}update(){this._chart.model().timeScale().marks()}getBitmapSize(){return this._canvasBinding.bitmapSize}drawBitmap(t,i,s){const e=this.getBitmapSize();e.width>0&&e.height>0&&t.drawImage(this._canvasBinding.canvasElement,i,s)}paint(t){if(0===t)return;if(1!==t){this._canvasBinding.applySuggestedBitmapSize();const i=zs(this._canvasBinding);null!==i&&(i.useBitmapCoordinateSpace((t=>{this._drawBackground(t),this._drawBorder(t)})),this._drawTickMarks(i)),null!==this._leftStub&&this._leftStub.paint(t),null!==this._rightStub&&this._rightStub.paint(t)}this._topCanvasBinding.applySuggestedBitmapSize();const i=zs(this._topCanvasBinding);null!==i&&(i.useBitmapCoordinateSpace((({context:t,bitmapSize:i})=>{t.clearRect(0,0,i.width,i.height)})),this._drawLabels([this._chart.model().crosshairSource()],i))}_drawBackground({context:t,bitmapSize:i}){F(t,0,0,i.width,i.height,this._chart.model().backgroundBottomColor())}_drawBorder({context:t,bitmapSize:i,verticalPixelRatio:s}){if(this._chart.options().timeScale.borderVisible){t.fillStyle=this._lineColor();const e=Math.max(1,Math.floor(this._getRendererOptions().borderSize*s));t.fillRect(0,0,i.width,e)}}_drawTickMarks(t){const i=this._chart.model().timeScale(),s=i.marks();if(!s||0===s.length)return;let e=s.reduce(pe,s[0]).weight;e>30&&e<50&&(e=30);const h=this._getRendererOptions(),n=i.options();n.borderVisible&&n.ticksVisible&&t.useBitmapCoordinateSpace((({context:t,horizontalPixelRatio:i,verticalPixelRatio:e})=>{t.strokeStyle=this._lineColor(),t.fillStyle=this._lineColor();const n=Math.max(1,Math.floor(i)),r=Math.floor(.5*i);t.beginPath();const o=Math.round(h.tickLength*e);for(let e=s.length;e--;){const h=Math.round(s[e].coord*i);t.rect(h-r,0,n,o)}t.fill()})),t.useMediaCoordinateSpace((({context:t})=>{const i=h.borderSize+h.tickLength+h.paddingTop+h.fontSize/2;t.textAlign="center",t.textBaseline="middle",t.fillStyle=this._textColor(),t.font=this._baseFont();for(const h of s)if(h.weight<e){const s=h.needAlignCoordinate?this._alignTickMarkLabelCoordinate(t,h.coord,h.label):h.coord;t.fillText(h.label,s,i)}t.font=this._baseBoldFont();for(const h of s)if(h.weight>=e){const s=h.needAlignCoordinate?this._alignTickMarkLabelCoordinate(t,h.coord,h.label):h.coord;t.fillText(h.label,s,i)}}))}_alignTickMarkLabelCoordinate(t,i,s){const e=this._widthCache.measureText(t,s),h=e/2,n=Math.floor(i-h)+.5;return n<0?i+=Math.abs(0-n):n+e>this._size.width&&(i-=Math.abs(this._size.width-(n+e))),i}_drawLabels(t,i){const s=this._getRendererOptions();for(const e of t)for(const t of e.timeAxisViews())t.renderer().draw(i,s)}_lineColor(){return this._chart.options().timeScale.borderColor}_textColor(){return this._options.textColor}_fontSize(){return this._options.fontSize}_baseFont(){return T(this._fontSize(),this._options.fontFamily)}_baseBoldFont(){return T(this._fontSize(),this._options.fontFamily,"bold")}_getRendererOptions(){null===this._rendererOptions&&(this._rendererOptions={borderSize:1,baselineOffset:NaN,paddingTop:NaN,paddingBottom:NaN,paddingHorizontal:NaN,tickLength:5,fontSize:NaN,font:"",widthCache:new Yt,labelBottomOffset:0});const t=this._rendererOptions,i=this._baseFont();if(t.font!==i){const s=this._fontSize();t.fontSize=s,t.font=i,t.paddingTop=3*s/12,t.paddingBottom=3*s/12,t.paddingHorizontal=9*s/12,t.baselineOffset=0,t.labelBottomOffset=4*s/12,t.widthCache.reset()}return this._rendererOptions}_setCursor(t){this._cell.style.cursor=1===t?"ew-resize":"default"}_recreateStubs(){const t=this._chart.model(),i=t.options();i.leftPriceScale.visible||null===this._leftStub||(this._leftStubCell.removeChild(this._leftStub.getElement()),this._leftStub.destroy(),this._leftStub=null),i.rightPriceScale.visible||null===this._rightStub||(this._rightStubCell.removeChild(this._rightStub.getElement()),this._rightStub.destroy(),this._rightStub=null);const s={rendererOptionsProvider:this._chart.model().rendererOptionsProvider()},e=()=>i.leftPriceScale.borderVisible&&t.timeScale().options().borderVisible,h=()=>t.backgroundBottomColor();i.leftPriceScale.visible&&null===this._leftStub&&(this._leftStub=new fe("left",i,s,e,h),this._leftStubCell.appendChild(this._leftStub.getElement())),i.rightPriceScale.visible&&null===this._rightStub&&(this._rightStub=new fe("right",i,s,e,h),this._rightStubCell.appendChild(this._rightStub.getElement()))}}const ve=!!Is&&!!navigator.userAgentData&&navigator.userAgentData.brands.some((t=>t.brand.includes("Chromium")))&&!!Is&&((null===(be=null===navigator||void 0===navigator?void 0:navigator.userAgentData)||void 0===be?void 0:be.platform)?"Windows"===navigator.userAgentData.platform:navigator.userAgent.toLowerCase().indexOf("win")>=0);var be;class ge{constructor(t,i){var s;this._paneWidgets=[],this._drawRafId=0,this._height=0,this._width=0,this._leftPriceAxisWidth=0,this._rightPriceAxisWidth=0,this._invalidateMask=null,this._drawPlanned=!1,this._clicked=new b,this._crosshairMoved=new b,this._observer=null,this._container=t,this._options=i,this._element=document.createElement("div"),this._element.classList.add("tv-lightweight-charts"),this._element.style.overflow="hidden",this._element.style.direction="ltr",this._element.style.width="100%",this._element.style.height="100%",(s=this._element).style.userSelect="none",s.style.webkitUserSelect="none",s.style.msUserSelect="none",s.style.MozUserSelect="none",s.style.webkitTapHighlightColor="transparent",this._tableElement=document.createElement("table"),this._tableElement.setAttribute("cellspacing","0"),this._element.appendChild(this._tableElement),this._onWheelBound=this._onMousewheel.bind(this),we(this._options)&&this._setMouseWheelEventListener(!0),this._model=new Ts(this._invalidateHandler.bind(this),this._options),this.model().crosshairMoved().subscribe(this._onPaneWidgetCrosshairMoved.bind(this),this),this._timeAxisWidget=new me(this),this._tableElement.appendChild(this._timeAxisWidget.getElement());const e=i.autoSize&&this._installObserver();let h=this._options.width,n=this._options.height;if(e||0===h||0===n){const i=t.getBoundingClientRect();h=h||i.width,n=n||i.height}this.resize(h,n),this._syncGuiWithModel(),t.appendChild(this._element),this._updateTimeAxisVisibility(),this._model.timeScale().optionsApplied().subscribe(this._model.fullUpdate.bind(this._model),this),this._model.priceScalesOptionsChanged().subscribe(this._model.fullUpdate.bind(this._model),this)}model(){return this._model}options(){return this._options}paneWidgets(){return this._paneWidgets}timeAxisWidget(){return this._timeAxisWidget}destroy(){this._setMouseWheelEventListener(!1),0!==this._drawRafId&&window.cancelAnimationFrame(this._drawRafId),this._model.crosshairMoved().unsubscribeAll(this),this._model.timeScale().optionsApplied().unsubscribeAll(this),this._model.priceScalesOptionsChanged().unsubscribeAll(this),this._model.destroy();for(const t of this._paneWidgets)this._tableElement.removeChild(t.getElement()),t.clicked().unsubscribeAll(this),t.destroy();this._paneWidgets=[],r(this._timeAxisWidget).destroy(),null!==this._element.parentElement&&this._element.parentElement.removeChild(this._element),this._crosshairMoved.destroy(),this._clicked.destroy(),this._uninstallObserver()}resize(t,i,s=!1){if(this._height===i&&this._width===t)return;const e=function(t){const i=Math.floor(t.width),s=Math.floor(t.height);return Ds({width:i-i%2,height:s-s%2})}(Ds({width:t,height:i}));this._height=e.height,this._width=e.width;const h=this._height+"px",n=this._width+"px";r(this._element).style.height=h,r(this._element).style.width=n,this._tableElement.style.height=h,this._tableElement.style.width=n,s?this._drawImpl(ht.full(),performance.now()):this._model.fullUpdate()}paint(t){void 0===t&&(t=ht.full());for(let i=0;i<this._paneWidgets.length;i++)this._paneWidgets[i].paint(t.invalidateForPane(i).level);this._options.timeScale.visible&&this._timeAxisWidget.paint(t.fullInvalidation())}applyOptions(t){const i=we(this._options);this._model.applyOptions(t);const s=we(this._options);s!==i&&this._setMouseWheelEventListener(s),this._updateTimeAxisVisibility(),this._applyAutoSizeOptions(t)}clicked(){return this._clicked}crosshairMoved(){return this._crosshairMoved}takeScreenshot(){null!==this._invalidateMask&&(this._drawImpl(this._invalidateMask,performance.now()),this._invalidateMask=null);const t=this._traverseLayout(null),i=document.createElement("canvas");i.width=t.width,i.height=t.height;const s=r(i.getContext("2d"));return this._traverseLayout(s),i}getPriceAxisWidth(t){if("left"===t&&!this._isLeftAxisVisible())return 0;if("right"===t&&!this._isRightAxisVisible())return 0;if(0===this._paneWidgets.length)return 0;return r("left"===t?this._paneWidgets[0].leftPriceAxisWidget():this._paneWidgets[0].rightPriceAxisWidget()).getWidth()}autoSizeActive(){return this._options.autoSize&&null!==this._observer}_applyAutoSizeOptions(t){(void 0!==t.autoSize||!this._observer||void 0===t.width&&void 0===t.height)&&(t.autoSize&&!this._observer&&this._installObserver(),!1===t.autoSize&&null!==this._observer&&this._uninstallObserver(),t.autoSize||void 0===t.width&&void 0===t.height||this.resize(t.width||this._width,t.height||this._height))}_traverseLayout(t){let i=0,s=0;const e=this._paneWidgets[0],h=(i,s)=>{let e=0;for(let h=0;h<this._paneWidgets.length;h++){const n=this._paneWidgets[h],o=r("left"===i?n.leftPriceAxisWidget():n.rightPriceAxisWidget()),l=o.getBitmapSize();null!==t&&o.drawBitmap(t,s,e),e+=l.height}};if(this._isLeftAxisVisible()){h("left",0);i+=r(e.leftPriceAxisWidget()).getBitmapSize().width}for(let e=0;e<this._paneWidgets.length;e++){const h=this._paneWidgets[e],n=h.getBitmapSize();null!==t&&h.drawBitmap(t,i,s),s+=n.height}if(i+=e.getBitmapSize().width,this._isRightAxisVisible()){h("right",i);i+=r(e.rightPriceAxisWidget()).getBitmapSize().width}const n=(i,s,e)=>{r("left"===i?this._timeAxisWidget.leftStub():this._timeAxisWidget.rightStub()).drawBitmap(r(t),s,e)};if(this._options.timeScale.visible){const i=this._timeAxisWidget.getBitmapSize();if(null!==t){let h=0;this._isLeftAxisVisible()&&(n("left",h,s),h=r(e.leftPriceAxisWidget()).getBitmapSize().width),this._timeAxisWidget.drawBitmap(t,h,s),h+=i.width,this._isRightAxisVisible()&&n("right",h,s)}s+=i.height}return Ds({width:i,height:s})}_adjustSizeImpl(){let t=0,i=0,s=0;for(const e of this._paneWidgets)this._isLeftAxisVisible()&&(i=Math.max(i,r(e.leftPriceAxisWidget()).optimalWidth())),this._isRightAxisVisible()&&(s=Math.max(s,r(e.rightPriceAxisWidget()).optimalWidth())),t+=e.stretchFactor();i=Fs(i),s=Fs(s);const e=this._width,h=this._height,n=Math.max(e-i-s,0),o=this._options.timeScale.visible;let l=o?this._timeAxisWidget.optimalHeight():0;var a;l=(a=l)+a%2;const u=0+l,c=h<u?0:h-u,d=c/t;let f=0;for(let t=0;t<this._paneWidgets.length;++t){const e=this._paneWidgets[t];e.setState(this._model.panes()[t]);let h=0,r=0;r=t===this._paneWidgets.length-1?c-f:Math.round(e.stretchFactor()*d),h=Math.max(r,2),f+=h,e.setSize(Ds({width:n,height:h})),this._isLeftAxisVisible()&&e.setPriceAxisSize(i,"left"),this._isRightAxisVisible()&&e.setPriceAxisSize(s,"right"),e.state()&&this._model.setPaneHeight(e.state(),h)}this._timeAxisWidget.setSizes(Ds({width:o?n:0,height:l}),o?i:0,o?s:0),this._model.setWidth(n),this._leftPriceAxisWidth!==i&&(this._leftPriceAxisWidth=i),this._rightPriceAxisWidth!==s&&(this._rightPriceAxisWidth=s)}_setMouseWheelEventListener(t){t?this._element.addEventListener("wheel",this._onWheelBound,{passive:!1}):this._element.removeEventListener("wheel",this._onWheelBound)}_determineWheelSpeedAdjustment(t){switch(t.deltaMode){case t.DOM_DELTA_PAGE:return 120;case t.DOM_DELTA_LINE:return 32}return ve?1/window.devicePixelRatio:1}_onMousewheel(t){if(!(0!==t.deltaX&&this._options.handleScroll.mouseWheel||0!==t.deltaY&&this._options.handleScale.mouseWheel))return;const i=this._determineWheelSpeedAdjustment(t),s=i*t.deltaX/100,e=-i*t.deltaY/100;if(t.cancelable&&t.preventDefault(),0!==e&&this._options.handleScale.mouseWheel){const i=Math.sign(e)*Math.min(1,Math.abs(e)),s=t.clientX-this._element.getBoundingClientRect().left;this.model().zoomTime(s,i)}0!==s&&this._options.handleScroll.mouseWheel&&this.model().scrollChart(-80*s)}_drawImpl(t,i){var s;const e=t.fullInvalidation();3===e&&this._updateGui(),3!==e&&2!==e||(this._applyMomentaryAutoScale(t),this._applyTimeScaleInvalidations(t,i),this._timeAxisWidget.update(),this._paneWidgets.forEach((t=>{t.updatePriceAxisWidgets()})),3===(null===(s=this._invalidateMask)||void 0===s?void 0:s.fullInvalidation())&&(this._invalidateMask.merge(t),this._updateGui(),this._applyMomentaryAutoScale(this._invalidateMask),this._applyTimeScaleInvalidations(this._invalidateMask,i),t=this._invalidateMask,this._invalidateMask=null)),this.paint(t)}_applyTimeScaleInvalidations(t,i){for(const s of t.timeScaleInvalidations())this._applyTimeScaleInvalidation(s,i)}_applyMomentaryAutoScale(t){const i=this._model.panes();for(let s=0;s<i.length;s++)t.invalidateForPane(s).autoScale&&i[s].momentaryAutoScale()}_applyTimeScaleInvalidation(t,i){const s=this._model.timeScale();switch(t.type){case 0:s.fitContent();break;case 1:s.setLogicalRange(t.value);break;case 2:s.setBarSpacing(t.value);break;case 3:s.setRightOffset(t.value);break;case 4:s.restoreDefault();break;case 5:t.value.finished(i)||s.setRightOffset(t.value.getPosition(i))}}_invalidateHandler(t){null!==this._invalidateMask?this._invalidateMask.merge(t):this._invalidateMask=t,this._drawPlanned||(this._drawPlanned=!0,this._drawRafId=window.requestAnimationFrame((t=>{if(this._drawPlanned=!1,this._drawRafId=0,null!==this._invalidateMask){const i=this._invalidateMask;this._invalidateMask=null,this._drawImpl(i,t);for(const s of i.timeScaleInvalidations())if(5===s.type&&!s.value.finished(t)){this.model().setTimeScaleAnimation(s.value);break}}})))}_updateGui(){this._syncGuiWithModel()}_syncGuiWithModel(){const t=this._model.panes(),i=t.length,s=this._paneWidgets.length;for(let t=i;t<s;t++){const t=n(this._paneWidgets.pop());this._tableElement.removeChild(t.getElement()),t.clicked().unsubscribeAll(this),t.destroy()}for(let e=s;e<i;e++){const i=new de(this,t[e]);i.clicked().subscribe(this._onPaneWidgetClicked.bind(this),this),this._paneWidgets.push(i),this._tableElement.insertBefore(i.getElement(),this._timeAxisWidget.getElement())}for(let s=0;s<i;s++){const i=t[s],e=this._paneWidgets[s];e.state()!==i?e.setState(i):e.updatePriceAxisWidgetsStates()}this._updateTimeAxisVisibility(),this._adjustSizeImpl()}_getMouseEventParamsImpl(t,i,s){var e;const h=new Map;if(null!==t){this._model.serieses().forEach((i=>{const s=i.bars().search(t);null!==s&&h.set(i,s)}))}let n;if(null!==t){const i=null===(e=this._model.timeScale().indexToTimeScalePoint(t))||void 0===e?void 0:e.originalTime;void 0!==i&&(n=i)}const r=this.model().hoveredSource(),o=null!==r&&r.source instanceof Bi?r.source:void 0,l=null!==r&&void 0!==r.object?r.object.externalId:void 0;return{time:n,index:null!=t?t:void 0,point:null!=i?i:void 0,hoveredSeries:o,seriesData:h,hoveredObject:l,touchMouseEventData:null!=s?s:void 0}}_onPaneWidgetClicked(t,i,s){this._clicked.fire((()=>this._getMouseEventParamsImpl(t,i,s)))}_onPaneWidgetCrosshairMoved(t,i,s){this._crosshairMoved.fire((()=>this._getMouseEventParamsImpl(t,i,s)))}_updateTimeAxisVisibility(){const t=this._options.timeScale.visible?"":"none";this._timeAxisWidget.getElement().style.display=t}_isLeftAxisVisible(){return this._paneWidgets[0].state().leftPriceScale().options().visible}_isRightAxisVisible(){return this._paneWidgets[0].state().rightPriceScale().options().visible}_installObserver(){return"ResizeObserver"in window&&(this._observer=new ResizeObserver((t=>{const i=t.find((t=>t.target===this._container));i&&this.resize(i.contentRect.width,i.contentRect.height)})),this._observer.observe(this._container,{box:"border-box"}),!0)}_uninstallObserver(){null!==this._observer&&this._observer.disconnect()}}function we(t){return Boolean(t.handleScroll.mouseWheel||t.handleScale.mouseWheel)}function Me(t,i,s,e){const h=s.value,n={index:i,time:t,value:[h,h,h,h],originalTime:e};return void 0!==s.color&&(n.color=s.color),n}function Se(t){return void 0!==t.value}function xe(t){return(i,s,e,h)=>{return void 0===(n=e).open&&void 0===n.value?{time:i,index:s,originalTime:h}:t(i,s,e,h);var n}}const _e={Candlestick:xe((function(t,i,s,e){const h={index:i,time:t,value:[s.open,s.high,s.low,s.close],originalTime:e};return void 0!==s.color&&(h.color=s.color),void 0!==s.borderColor&&(h.borderColor=s.borderColor),void 0!==s.wickColor&&(h.wickColor=s.wickColor),h})),Bar:xe((function(t,i,s,e){const h={index:i,time:t,value:[s.open,s.high,s.low,s.close],originalTime:e};return void 0!==s.color&&(h.color=s.color),h})),Area:xe((function(t,i,s,e){const h=s.value,n={index:i,time:t,value:[h,h,h,h],originalTime:e};return void 0!==s.lineColor&&(n.lineColor=s.lineColor),void 0!==s.topColor&&(n.topColor=s.topColor),void 0!==s.bottomColor&&(n.bottomColor=s.bottomColor),n})),Baseline:xe((function(t,i,s,e){const h=s.value,n={index:i,time:t,value:[h,h,h,h],originalTime:e};return void 0!==s.topLineColor&&(n.topLineColor=s.topLineColor),void 0!==s.bottomLineColor&&(n.bottomLineColor=s.bottomLineColor),void 0!==s.topFillColor1&&(n.topFillColor1=s.topFillColor1),void 0!==s.topFillColor2&&(n.topFillColor2=s.topFillColor2),void 0!==s.bottomFillColor1&&(n.bottomFillColor1=s.bottomFillColor1),void 0!==s.bottomFillColor2&&(n.bottomFillColor2=s.bottomFillColor2),n})),Histogram:xe(Me),Line:xe(Me)};function ye(t){return _e[t]}function ke(t){return 60*t*60*1e3}function Ce(t){return 60*t*1e3}const Te=[{divisor:(Pe=1,1e3*Pe),weight:10},{divisor:Ce(1),weight:20},{divisor:Ce(5),weight:21},{divisor:Ce(30),weight:22},{divisor:ke(1),weight:30},{divisor:ke(3),weight:31},{divisor:ke(6),weight:32},{divisor:ke(12),weight:33}];var Pe;function Re(t,i){if(t.getUTCFullYear()!==i.getUTCFullYear())return 70;if(t.getUTCMonth()!==i.getUTCMonth())return 60;if(t.getUTCDate()!==i.getUTCDate())return 50;for(let s=Te.length-1;s>=0;--s)if(Math.floor(i.getTime()/Te[s].divisor)!==Math.floor(t.getTime()/Te[s].divisor))return Te[s].weight;return 0}function De(t,i=0){if(0===t.length)return;let s=0===i?null:t[i-1].time.timestamp,e=null!==s?new Date(1e3*s):null,h=0;for(let n=i;n<t.length;++n){const i=t[n],r=new Date(1e3*i.time.timestamp);null!==e&&(i.timeWeight=Re(r,e)),h+=i.time.timestamp-(s||i.time.timestamp),s=i.time.timestamp,e=r}if(0===i&&t.length>1){const i=Math.ceil(h/(t.length-1)),s=new Date(1e3*(t[0].time.timestamp-i));t[0].timeWeight=Re(new Date(1e3*t[0].time.timestamp),s)}}function Ae(t){if(!Ps(t))throw new Error("time must be of type BusinessDay");const i=new Date(Date.UTC(t.year,t.month-1,t.day,0,0,0,0));return{timestamp:Math.round(i.getTime()/1e3),businessDay:t}}function Ee(t){if(!Rs(t))throw new Error("time must be of type isUTCTimestamp");return{timestamp:t}}function Oe(t){return 0===t.length?null:Ps(t[0].time)?Ae:Ee}function Be(t){return Rs(t)?Ee(t):Ps(t)?Ae(t):Ae(Le(t))}function Le(t){const i=new Date(t);if(isNaN(i.getTime()))throw new Error(`Invalid date string=${t}, expected format=yyyy-mm-dd`);return{day:i.getUTCDate(),month:i.getUTCMonth()+1,year:i.getUTCFullYear()}}function ze(t){S(t.time)&&(t.time=Le(t.time))}function Ie(t){return{index:0,mapping:new Map,timePoint:t}}function Ne(t){if(void 0!==t&&0!==t.length)return{firstTime:t[0].time.timestamp,lastTime:t[t.length-1].time.timestamp}}function Ve(t){let i;return t.forEach((t=>{void 0===i&&(i=t.originalTime)})),n(i)}function Fe(t){void 0===t.originalTime&&(t.originalTime=t.time)}class We{constructor(){this._pointDataByTimePoint=new Map,this._seriesRowsBySeries=new Map,this._seriesLastTimePoint=new Map,this._sortedTimePoints=[]}destroy(){this._pointDataByTimePoint.clear(),this._seriesRowsBySeries.clear(),this._seriesLastTimePoint.clear(),this._sortedTimePoints=[]}setSeriesData(t,i){let s=0!==this._pointDataByTimePoint.size,e=!1;const h=this._seriesRowsBySeries.get(t);if(void 0!==h)if(1===this._seriesRowsBySeries.size)s=!1,e=!0,this._pointDataByTimePoint.clear();else for(const i of this._sortedTimePoints)i.pointData.mapping.delete(t)&&(e=!0);let n=[];if(0!==i.length){const s=i;s.forEach((t=>Fe(t))),function(t){t.forEach(ze)}(i);const h=r(Oe(i)),o=ye(t.seriesType());n=s.map((i=>{const s=h(i.time);let n=this._pointDataByTimePoint.get(s.timestamp);void 0===n&&(n=Ie(s),this._pointDataByTimePoint.set(s.timestamp,n),e=!0);const r=o(s,n.index,i,i.originalTime);return n.mapping.set(t,r),r}))}s&&this._cleanupPointsData(),this._setRowsToSeries(t,n);let o=-1;if(e){const t=[];this._pointDataByTimePoint.forEach((i=>{t.push({timeWeight:0,time:i.timePoint,pointData:i,originalTime:Ve(i.mapping)})})),t.sort(((t,i)=>t.time.timestamp-i.time.timestamp)),o=this._replaceTimeScalePoints(t)}return this._getUpdateResponse(t,o,function(t,i){const s=Ne(t),e=Ne(i);if(void 0!==s&&void 0!==e)return{lastBarUpdatedOrNewBarsAddedToTheRight:s.lastTime>=e.lastTime&&s.firstTime>=e.firstTime}}(this._seriesRowsBySeries.get(t),h))}removeSeries(t){return this.setSeriesData(t,[])}updateSeriesData(t,i){const s=i;Fe(s),ze(i);const e=r(Oe([i]))(i.time),h=this._seriesLastTimePoint.get(t);if(void 0!==h&&e.timestamp<h.timestamp)throw new Error(`Cannot update oldest data, last time=${h.timestamp}, new time=${e.timestamp}`);let n=this._pointDataByTimePoint.get(e.timestamp);const o=void 0===n;void 0===n&&(n=Ie(e),this._pointDataByTimePoint.set(e.timestamp,n));const l=ye(t.seriesType())(e,n.index,i,s.originalTime);n.mapping.set(t,l),this._updateLastSeriesRow(t,l);const a={lastBarUpdatedOrNewBarsAddedToTheRight:Se(l)};if(!o)return this._getUpdateResponse(t,-1,a);const u={timeWeight:0,time:n.timePoint,pointData:n,originalTime:Ve(n.mapping)},c=St(this._sortedTimePoints,u.time.timestamp,((t,i)=>t.time.timestamp<i));this._sortedTimePoints.splice(c,0,u);for(let t=c;t<this._sortedTimePoints.length;++t)je(this._sortedTimePoints[t].pointData,t);return De(this._sortedTimePoints,c),this._getUpdateResponse(t,c,a)}_updateLastSeriesRow(t,i){let s=this._seriesRowsBySeries.get(t);void 0===s&&(s=[],this._seriesRowsBySeries.set(t,s));const e=0!==s.length?s[s.length-1]:null;null===e||i.time.timestamp>e.time.timestamp?Se(i)&&s.push(i):Se(i)?s[s.length-1]=i:s.splice(-1,1),this._seriesLastTimePoint.set(t,i.time)}_setRowsToSeries(t,i){0!==i.length?(this._seriesRowsBySeries.set(t,i.filter(Se)),this._seriesLastTimePoint.set(t,i[i.length-1].time)):(this._seriesRowsBySeries.delete(t),this._seriesLastTimePoint.delete(t))}_cleanupPointsData(){for(const t of this._sortedTimePoints)0===t.pointData.mapping.size&&this._pointDataByTimePoint.delete(t.time.timestamp)}_replaceTimeScalePoints(t){let i=-1;for(let s=0;s<this._sortedTimePoints.length&&s<t.length;++s){const e=this._sortedTimePoints[s],h=t[s];if(e.time.timestamp!==h.time.timestamp){i=s;break}h.timeWeight=e.timeWeight,je(h.pointData,s)}if(-1===i&&this._sortedTimePoints.length!==t.length&&(i=Math.min(this._sortedTimePoints.length,t.length)),-1===i)return-1;for(let s=i;s<t.length;++s)je(t[s].pointData,s);return De(t,i),this._sortedTimePoints=t,i}_getBaseIndex(){if(0===this._seriesRowsBySeries.size)return null;let t=0;return this._seriesRowsBySeries.forEach((i=>{0!==i.length&&(t=Math.max(t,i[i.length-1].index))})),t}_getUpdateResponse(t,i,s){const e={series:new Map,timeScale:{baseIndex:this._getBaseIndex()}};if(-1!==i)this._seriesRowsBySeries.forEach(((i,h)=>{e.series.set(h,{data:i,info:h===t?s:void 0})})),this._seriesRowsBySeries.has(t)||e.series.set(t,{data:[],info:s}),e.timeScale.points=this._sortedTimePoints,e.timeScale.firstChangedPointIndex=i;else{const i=this._seriesRowsBySeries.get(t);e.series.set(t,{data:i||[],info:s})}return e}}function je(t,i){t.index=i,t.mapping.forEach((t=>{t.index=i}))}function He(t){return{value:t.value[3],time:t.originalTime}}function $e(t){const i=He(t);return void 0!==t.color&&(i.color=t.color),i}function Ue(t){return{open:t.value[0],high:t.value[1],low:t.value[2],close:t.value[3],time:t.originalTime}}const qe={Area:function(t){const i=He(t);return void 0!==t.lineColor&&(i.lineColor=t.lineColor),void 0!==t.topColor&&(i.topColor=t.topColor),void 0!==t.bottomColor&&(i.bottomColor=t.bottomColor),i},Line:$e,Baseline:function(t){const i=He(t);return void 0!==t.topLineColor&&(i.topLineColor=t.topLineColor),void 0!==t.bottomLineColor&&(i.bottomLineColor=t.bottomLineColor),void 0!==t.topFillColor1&&(i.topFillColor1=t.topFillColor1),void 0!==t.topFillColor2&&(i.topFillColor2=t.topFillColor2),void 0!==t.bottomFillColor1&&(i.bottomFillColor1=t.bottomFillColor1),void 0!==t.bottomFillColor2&&(i.bottomFillColor2=t.bottomFillColor2),i},Histogram:$e,Bar:function(t){const i=Ue(t);return void 0!==t.color&&(i.color=t.color),i},Candlestick:function(t){const i=Ue(t),{color:s,borderColor:e,wickColor:h}=t;return void 0!==s&&(i.color=s),void 0!==e&&(i.borderColor=e),void 0!==h&&(i.wickColor=h),i}};function Ye(t){return qe[t]}const Xe={autoScale:!0,mode:0,invertScale:!1,alignLabels:!0,borderVisible:!0,borderColor:"#2B2B43",entireTextOnly:!1,visible:!1,ticksVisible:!1,scaleMargins:{bottom:.1,top:.2}},Ze={color:"rgba(0, 0, 0, 0)",visible:!1,fontSize:48,fontFamily:C,fontStyle:"",text:"",horzAlign:"center",vertAlign:"center"},Ke={width:0,height:0,autoSize:!1,layout:{background:{type:"solid",color:"#FFFFFF"},textColor:"#191919",fontSize:12,fontFamily:C},crosshair:{vertLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},horzLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},mode:1},grid:{vertLines:{color:"#D6DCDE",style:0,visible:!0},horzLines:{color:"#D6DCDE",style:0,visible:!0}},overlayPriceScales:Object.assign({},Xe),leftPriceScale:Object.assign(Object.assign({},Xe),{visible:!1}),rightPriceScale:Object.assign(Object.assign({},Xe),{visible:!0}),timeScale:{rightOffset:0,barSpacing:6,minBarSpacing:.5,fixLeftEdge:!1,fixRightEdge:!1,lockVisibleTimeRangeOnResize:!1,rightBarStaysOnScroll:!1,borderVisible:!0,borderColor:"#2B2B43",visible:!0,timeVisible:!1,secondsVisible:!0,shiftVisibleRangeOnNewBar:!0,ticksVisible:!1},watermark:Ze,localization:{locale:Is?navigator.language:"",dateFormat:"dd MMM 'yy"},handleScroll:{mouseWheel:!0,pressedMouseMove:!0,horzTouchDrag:!0,vertTouchDrag:!0},handleScale:{axisPressedMouseMove:{time:!0,price:!0},axisDoubleClickReset:{time:!0,price:!0},mouseWheel:!0,pinch:!0},kineticScroll:{mouse:!1,touch:!0},trackingMode:{exitMode:1}},Ge={upColor:"#26a69a",downColor:"#ef5350",wickVisible:!0,borderVisible:!0,borderColor:"#378658",borderUpColor:"#26a69a",borderDownColor:"#ef5350",wickColor:"#737375",wickUpColor:"#26a69a",wickDownColor:"#ef5350"},Je={upColor:"#26a69a",downColor:"#ef5350",openVisible:!0,thinBars:!0},Qe={color:"#2196f3",lineStyle:0,lineWidth:3,lineType:0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0},th={topColor:"rgba( 46, 220, 135, 0.4)",bottomColor:"rgba( 40, 221, 100, 0)",invertFilledArea:!1,lineColor:"#33D778",lineStyle:0,lineWidth:3,lineType:0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0},ih={baseValue:{type:"price",price:0},topFillColor1:"rgba(38, 166, 154, 0.28)",topFillColor2:"rgba(38, 166, 154, 0.05)",topLineColor:"rgba(38, 166, 154, 1)",bottomFillColor1:"rgba(239, 83, 80, 0.05)",bottomFillColor2:"rgba(239, 83, 80, 0.28)",bottomLineColor:"rgba(239, 83, 80, 1)",lineWidth:3,lineStyle:0,lineType:0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0},sh={color:"#26a69a",base:0},eh={title:"",visible:!0,lastValueVisible:!0,priceLineVisible:!0,priceLineSource:0,priceLineWidth:1,priceLineColor:"",priceLineStyle:2,baseLineVisible:!0,baseLineWidth:1,baseLineColor:"#B2B5BE",baseLineStyle:0,priceFormat:{type:"price",precision:2,minMove:.01}};class hh{constructor(t,i){this._chartWidget=t,this._priceScaleId=i}applyOptions(t){this._chartWidget.model().applyPriceScaleOptions(this._priceScaleId,t)}options(){return this._priceScale().options()}width(){return et(this._priceScaleId)?this._chartWidget.getPriceAxisWidth(this._priceScaleId):0}_priceScale(){return r(this._chartWidget.model().findPriceScale(this._priceScaleId)).priceScale}}const nh={color:"#FF0000",price:0,lineStyle:2,lineWidth:1,lineVisible:!0,axisLabelVisible:!0,title:"",axisLabelColor:"",axisLabelTextColor:""};class rh{constructor(t){this._priceLine=t}applyOptions(t){this._priceLine.applyOptions(t)}options(){return this._priceLine.options()}priceLine(){return this._priceLine}}class oh{constructor(t,i,s){this._series=t,this._dataUpdatesConsumer=i,this._priceScaleApiProvider=s}priceFormatter(){return this._series.formatter()}priceToCoordinate(t){const i=this._series.firstValue();return null===i?null:this._series.priceScale().priceToCoordinate(t,i.value)}coordinateToPrice(t){const i=this._series.firstValue();return null===i?null:this._series.priceScale().coordinateToPrice(t,i.value)}barsInLogicalRange(t){if(null===t)return null;const i=new ps(new cs(t.from,t.to)).strictRange(),s=this._series.bars();if(s.isEmpty())return null;const e=s.search(i.left(),1),h=s.search(i.right(),-1),n=r(s.firstIndex()),o=r(s.lastIndex());if(null!==e&&null!==h&&e.index>h.index)return{barsBefore:t.from-n,barsAfter:o-t.to};const l={barsBefore:null===e||e.index===n?t.from-n:e.index-n,barsAfter:null===h||h.index===o?o-t.to:o-h.index};return null!==e&&null!==h&&(l.from=e.time.businessDay||e.time.timestamp,l.to=h.time.businessDay||h.time.timestamp),l}setData(t){this._series.seriesType(),this._dataUpdatesConsumer.applyNewData(this._series,t)}update(t){this._series.seriesType(),this._dataUpdatesConsumer.updateData(this._series,t)}dataByIndex(t,i){const s=this._series.bars().search(t,i);return null===s?null:Ye(this.seriesType())(s)}setMarkers(t){const i=t.map((t=>Object.assign(Object.assign({},t),{originalTime:t.time,time:Be(t.time)})));this._series.setMarkers(i)}markers(){return this._series.markers().map((t=>{const{originalTime:i,time:s}=t,e=function(t,i){var s={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&i.indexOf(e)<0&&(s[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var h=0;for(e=Object.getOwnPropertySymbols(t);h<e.length;h++)i.indexOf(e[h])<0&&Object.prototype.propertyIsEnumerable.call(t,e[h])&&(s[e[h]]=t[e[h]])}return s}(t,["originalTime","time"]);return Object.assign({time:i},e)}))}applyOptions(t){this._series.applyOptions(t)}options(){return _(this._series.options())}priceScale(){return this._priceScaleApiProvider.priceScale(this._series.priceScale().id())}createPriceLine(t){const i=g(_(nh),t),s=this._series.createPriceLine(i);return new rh(s)}removePriceLine(t){this._series.removePriceLine(t.priceLine())}seriesType(){return this._series.seriesType()}}var lh;!function(t){t[t.AnimationDurationMs=1e3]="AnimationDurationMs"}(lh||(lh={}));class ah{constructor(t,i){this._timeRangeChanged=new b,this._logicalRangeChanged=new b,this._sizeChanged=new b,this._model=t,this._timeScale=t.timeScale(),this._timeAxisWidget=i,this._timeScale.visibleBarsChanged().subscribe(this._onVisibleBarsChanged.bind(this)),this._timeScale.logicalRangeChanged().subscribe(this._onVisibleLogicalRangeChanged.bind(this)),this._timeAxisWidget.sizeChanged().subscribe(this._onSizeChanged.bind(this))}destroy(){this._timeScale.visibleBarsChanged().unsubscribeAll(this),this._timeScale.logicalRangeChanged().unsubscribeAll(this),this._timeAxisWidget.sizeChanged().unsubscribeAll(this),this._timeRangeChanged.destroy(),this._logicalRangeChanged.destroy(),this._sizeChanged.destroy()}scrollPosition(){return this._timeScale.rightOffset()}scrollToPosition(t,i){i?this._timeScale.scrollToOffsetAnimated(t,1e3):this._model.setRightOffset(t)}scrollToRealTime(){this._timeScale.scrollToRealTime()}getVisibleRange(){var t,i;const s=this._timeScale.visibleTimeRange();return null===s?null:{from:null!==(t=s.from.businessDay)&&void 0!==t?t:s.from.timestamp,to:null!==(i=s.to.businessDay)&&void 0!==i?i:s.to.timestamp}}setVisibleRange(t){const i={from:Be(t.from),to:Be(t.to)},s=this._timeScale.logicalRangeForTimeRange(i);this._model.setTargetLogicalRange(s)}getVisibleLogicalRange(){const t=this._timeScale.visibleLogicalRange();return null===t?null:{from:t.left(),to:t.right()}}setVisibleLogicalRange(t){h(t.from<=t.to,"The from index cannot be after the to index."),this._model.setTargetLogicalRange(t)}resetTimeScale(){this._model.resetTimeScale()}fitContent(){this._model.fitContent()}logicalToCoordinate(t){const i=this._model.timeScale();return i.isEmpty()?null:i.indexToCoordinate(t)}coordinateToLogical(t){return this._timeScale.isEmpty()?null:this._timeScale.coordinateToIndex(t)}timeToCoordinate(t){const i=Be(t),s=this._timeScale.timeToIndex(i,!1);return null===s?null:this._timeScale.indexToCoordinate(s)}coordinateToTime(t){var i;const s=this._model.timeScale(),e=s.coordinateToIndex(t),h=s.indexToTime(e);return null===h?null:null!==(i=h.businessDay)&&void 0!==i?i:h.timestamp}width(){return this._timeAxisWidget.getSize().width}height(){return this._timeAxisWidget.getSize().height}subscribeVisibleTimeRangeChange(t){this._timeRangeChanged.subscribe(t)}unsubscribeVisibleTimeRangeChange(t){this._timeRangeChanged.unsubscribe(t)}subscribeVisibleLogicalRangeChange(t){this._logicalRangeChanged.subscribe(t)}unsubscribeVisibleLogicalRangeChange(t){this._logicalRangeChanged.unsubscribe(t)}subscribeSizeChange(t){this._sizeChanged.subscribe(t)}unsubscribeSizeChange(t){this._sizeChanged.unsubscribe(t)}applyOptions(t){this._timeScale.applyOptions(t)}options(){return _(this._timeScale.options())}_onVisibleBarsChanged(){this._timeRangeChanged.hasListeners()&&this._timeRangeChanged.fire(this.getVisibleRange())}_onVisibleLogicalRangeChanged(){this._logicalRangeChanged.hasListeners()&&this._logicalRangeChanged.fire(this.getVisibleLogicalRange())}_onSizeChanged(t){this._sizeChanged.fire(t.width,t.height)}}function uh(t){if(void 0===t||"custom"===t.type)return;const i=t;void 0!==i.minMove&&void 0===i.precision&&(i.precision=function(t){if(t>=1)return 0;let i=0;for(;i<8;i++){const s=Math.round(t);if(Math.abs(s-t)<1e-8)return i;t*=10}return i}(i.minMove))}function ch(t){return function(t){if(x(t.handleScale)){const i=t.handleScale;t.handleScale={axisDoubleClickReset:{time:i,price:i},axisPressedMouseMove:{time:i,price:i},mouseWheel:i,pinch:i}}else if(void 0!==t.handleScale){const{axisPressedMouseMove:i,axisDoubleClickReset:s}=t.handleScale;x(i)&&(t.handleScale.axisPressedMouseMove={time:i,price:i}),x(s)&&(t.handleScale.axisDoubleClickReset={time:s,price:s})}const i=t.handleScroll;x(i)&&(t.handleScroll={horzTouchDrag:i,vertTouchDrag:i,mouseWheel:i,pressedMouseMove:i})}(t),t}class dh{constructor(t,i){this._dataLayer=new We,this._seriesMap=new Map,this._seriesMapReversed=new Map,this._clickedDelegate=new b,this._crosshairMovedDelegate=new b;const s=void 0===i?_(Ke):g(_(Ke),ch(i));this._chartWidget=new ge(t,s),this._chartWidget.clicked().subscribe((t=>{this._clickedDelegate.hasListeners()&&this._clickedDelegate.fire(this._convertMouseParams(t()))}),this),this._chartWidget.crosshairMoved().subscribe((t=>{this._crosshairMovedDelegate.hasListeners()&&this._crosshairMovedDelegate.fire(this._convertMouseParams(t()))}),this);const e=this._chartWidget.model();this._timeScaleApi=new ah(e,this._chartWidget.timeAxisWidget())}remove(){this._chartWidget.clicked().unsubscribeAll(this),this._chartWidget.crosshairMoved().unsubscribeAll(this),this._timeScaleApi.destroy(),this._chartWidget.destroy(),this._seriesMap.clear(),this._seriesMapReversed.clear(),this._clickedDelegate.destroy(),this._crosshairMovedDelegate.destroy(),this._dataLayer.destroy()}resize(t,i,s){this.autoSizeActive()||this._chartWidget.resize(t,i,s)}addAreaSeries(t){return this._addSeriesImpl("Area",th,t)}addBaselineSeries(t){return this._addSeriesImpl("Baseline",ih,t)}addBarSeries(t){return this._addSeriesImpl("Bar",Je,t)}addCandlestickSeries(t={}){return function(t){void 0!==t.borderColor&&(t.borderUpColor=t.borderColor,t.borderDownColor=t.borderColor),void 0!==t.wickColor&&(t.wickUpColor=t.wickColor,t.wickDownColor=t.wickColor)}(t),this._addSeriesImpl("Candlestick",Ge,t)}addHistogramSeries(t){return this._addSeriesImpl("Histogram",sh,t)}addLineSeries(t){return this._addSeriesImpl("Line",Qe,t)}removeSeries(t){const i=n(this._seriesMap.get(t)),s=this._dataLayer.removeSeries(i);this._chartWidget.model().removeSeries(i),this._sendUpdateToChart(s),this._seriesMap.delete(t),this._seriesMapReversed.delete(i)}applyNewData(t,i){this._sendUpdateToChart(this._dataLayer.setSeriesData(t,i))}updateData(t,i){this._sendUpdateToChart(this._dataLayer.updateSeriesData(t,i))}subscribeClick(t){this._clickedDelegate.subscribe(t)}unsubscribeClick(t){this._clickedDelegate.unsubscribe(t)}subscribeCrosshairMove(t){this._crosshairMovedDelegate.subscribe(t)}setCrosshairXY(t,i,s){this._chartWidget.paneWidgets()[0].setCrosshair(t,i,s)}unsubscribeCrosshairMove(t){this._crosshairMovedDelegate.unsubscribe(t)}priceScale(t){return new hh(this._chartWidget,t)}timeScale(){return this._timeScaleApi}applyOptions(t){this._chartWidget.applyOptions(ch(t))}options(){return this._chartWidget.options()}takeScreenshot(){return this._chartWidget.takeScreenshot()}autoSizeActive(){return this._chartWidget.autoSizeActive()}_addSeriesImpl(t,i,s={}){uh(s.priceFormat);const e=g(_(eh),_(i),s),h=this._chartWidget.model().createSeries(t,e),n=new oh(h,this,this);return this._seriesMap.set(n,h),this._seriesMapReversed.set(h,n),n}_sendUpdateToChart(t){const i=this._chartWidget.model();i.updateTimeScale(t.timeScale.baseIndex,t.timeScale.points,t.timeScale.firstChangedPointIndex),t.series.forEach(((t,i)=>i.setData(t.data,t.info))),i.recalculateAllPanes()}_mapSeriesToApi(t){return n(this._seriesMapReversed.get(t))}_convertMouseParams(t){const i=new Map;t.seriesData.forEach(((t,s)=>{const e=Ye(s.seriesType())(t);h(function(t){return void 0!==t.open||void 0!==t.value}(e)),i.set(this._mapSeriesToApi(s),e)}));const s=void 0===t.hoveredSeries?void 0:this._mapSeriesToApi(t.hoveredSeries);return{time:t.time,logical:t.index,point:t.point,hoveredSeries:s,hoveredObjectId:t.hoveredObject,seriesData:i,sourceEvent:t.touchMouseEventData}}}var fh=Object.freeze({__proto__:null,get ColorType(){return xs},get CrosshairMode(){return J},get LastPriceAnimationMode(){return ws},get LineStyle(){return i},get LineType(){return t},get MismatchDirection(){return Di},get PriceLineSource(){return Ss},get PriceScaleMode(){return Qi},get TickMarkType(){return vs},get TrackingModeExitMode(){return gs},createChart:function(t,i){let s;if(S(t)){const i=document.getElementById(t);h(null!==i,`Cannot find element in DOM with id=${t}`),s=i}else s=t;return new dh(s,i)},isBusinessDay:Ps,isUTCTimestamp:Rs,version:function(){return"4.1.0-dev+202306102016"}});window.LightweightCharts=fh}();